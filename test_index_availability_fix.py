#!/usr/bin/env python3
"""
Test the optimized index availability query
"""
import time
from heavydb import connect
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_index_availability():
    """Test the optimized query for index availability"""
    try:
        # Connect to HeavyDB
        conn = connect(
            host='localhost',
            port=6274,
            user='admin',
            password='HyperInteractive',
            dbname='heavyai',
            protocol='binary'
        )
        logger.info("Connected to HeavyDB")
        
        cursor = conn.cursor()
        
        # Original slow query (one index at a time)
        logger.info("\n=== Testing ORIGINAL (slow) approach ===")
        indices = ['NIFTY', 'BANKNIFTY', 'MIDCAPNIFTY', 'SENSEX', 'FINNIFTY', 'BANKEX']
        start_time = time.time()
        
        result_old = {}
        for index in indices:
            query = """
            SELECT 
                COUNT(*) as row_count,
                COUNT(DISTINCT trade_date) as trading_days,
                MIN(trade_date) as min_date,
                MAX(trade_date) as max_date,
                COUNT(DISTINCT strike) as unique_strikes,
                COUNT(DISTINCT expiry_bucket) as expiry_types
            FROM nifty_option_chain
            WHERE index_name = %s
            """
            
            # HeavyDB uses different parameter syntax
            query_with_value = query.replace('%s', f"'{index}'")
            cursor.execute(query_with_value)
            data = cursor.fetchone()
            
            if data and data[0] > 0:
                result_old[index] = {
                    'available': True,
                    'row_count': data[0]
                }
            else:
                result_old[index] = {
                    'available': False,
                    'row_count': 0
                }
        
        old_duration = time.time() - start_time
        logger.info(f"Old approach took: {old_duration:.3f} seconds")
        
        # New optimized query (all indices at once)
        logger.info("\n=== Testing OPTIMIZED (fast) approach ===")
        start_time = time.time()
        
        query = """
        SELECT 
            index_name,
            COUNT(*) as row_count,
            COUNT(DISTINCT trade_date) as trading_days,
            MIN(trade_date) as min_date,
            MAX(trade_date) as max_date,
            COUNT(DISTINCT strike) as unique_strikes,
            COUNT(DISTINCT expiry_bucket) as expiry_types
        FROM nifty_option_chain
        WHERE index_name IN ('NIFTY', 'BANKNIFTY', 'MIDCAPNIFTY', 'SENSEX', 'FINNIFTY', 'BANKEX')
        GROUP BY index_name
        """
        
        cursor.execute(query)
        rows = cursor.fetchall()
        
        result_new = {}
        found_indices = set()
        
        for row in rows:
            index_name = row[0]
            found_indices.add(index_name)
            result_new[index_name] = {
                'available': True,
                'row_count': row[1],
                'trading_days': row[2],
                'min_date': row[3].strftime('%Y-%m-%d') if row[3] else None,
                'max_date': row[4].strftime('%Y-%m-%d') if row[4] else None,
                'unique_strikes': row[5],
                'expiry_types': row[6]
            }
        
        # Add entries for indices with no data
        for index in indices:
            if index not in found_indices:
                result_new[index] = {
                    'available': False,
                    'row_count': 0,
                    'trading_days': 0,
                    'min_date': None,
                    'max_date': None,
                    'unique_strikes': 0,
                    'expiry_types': 0
                }
        
        new_duration = time.time() - start_time
        logger.info(f"New approach took: {new_duration:.3f} seconds")
        
        # Calculate improvement
        speedup = old_duration / new_duration if new_duration > 0 else 0
        logger.info(f"\n🚀 Performance improvement: {speedup:.1f}x faster!")
        logger.info(f"Time saved: {old_duration - new_duration:.3f} seconds")
        
        # Display results
        logger.info("\n=== Results ===")
        for index in indices:
            if index in result_new:
                info = result_new[index]
                if info['available']:
                    logger.info(f"{index}: ✅ {info['row_count']:,} rows, "
                              f"{info['trading_days']} days, "
                              f"dates: {info['min_date']} to {info['max_date']}")
                else:
                    logger.info(f"{index}: ❌ No data available")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        raise

if __name__ == "__main__":
    test_index_availability()