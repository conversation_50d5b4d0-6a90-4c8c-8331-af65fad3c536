2025-06-05 02:00:02,667 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
/srv/samba/shared/etl_performance_optimizer.py:73: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:111: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-05 02:00:03,569 - __main__ - ERROR - Failed to analyze memory: Execution failed on sql: 
                SELECT 
                    DATE_TRUNC('hour', metric_time) as hour,
                    AVG(memory_mb) as avg_memory,
                    MAX(memory_mb) as max_memory,
                    AVG(rows_per_second) as avg_speed
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '24' HOUR
                GROUP BY hour
                ORDER BY hour
            
SQL Error: Encountered "hour" at line 2, column 59.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
/srv/samba/shared/etl_performance_optimizer.py:157: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:210: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:257: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:374: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:73: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:111: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-05 02:00:05,472 - __main__ - ERROR - Failed to analyze memory: Execution failed on sql: 
                SELECT 
                    DATE_TRUNC('hour', metric_time) as hour,
                    AVG(memory_mb) as avg_memory,
                    MAX(memory_mb) as max_memory,
                    AVG(rows_per_second) as avg_speed
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '24' HOUR
                GROUP BY hour
                ORDER BY hour
            
SQL Error: Encountered "hour" at line 2, column 59.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
/srv/samba/shared/etl_performance_optimizer.py:157: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:210: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:257: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-05 02:00:05,656 - __main__ - INFO - Optimization script generated: /srv/samba/shared/apply_etl_optimizations.sh
================================================================================
ETL OPTIMIZATION REPORT - 2025-06-05 02:00:02
================================================================================

## DISK I/O ANALYSIS
----------------------------------------

## MEMORY USAGE ANALYSIS
----------------------------------------

## GPU UTILIZATION ANALYSIS
----------------------------------------
GPU Enabled: No
• No GPU usage detected
• Consider: Enabling GPU acceleration for better performance

## QUERY PERFORMANCE ANALYSIS
----------------------------------------

## DATA DISTRIBUTION ANALYSIS
----------------------------------------
Index Distribution:
  NIFTY: 14,953,923 rows, 590 days
  BANKNIFTY: 1,354,604 rows, 140 days
  MIDCAPNIFTY: 26,830 rows, 3 days
  SENSEX: 324,451 rows, 38 days
• Significant data imbalance: 557.4x difference
• Consider: Partitioning by index, separate tables for large indices
• Sparse data detected for:
•   - NIFTY: 67.4% density
•   - BANKNIFTY: 37.9% density
•   - SENSEX: 10.1% density
• Consider: Data validation, filling missing dates

## AUTO-TUNING RESULTS
----------------------------------------
Auto-tuning not available (insufficient data)

## GENERATED OPTIMIZATION SCRIPTS
----------------------------------------
1. /srv/samba/shared/apply_etl_optimizations.sh - Apply recommended optimizations
2. /srv/samba/shared/etl_tuning_params.json - Auto-tuned parameters

================================================================================

Generating optimization script...
Optimization script saved to: /srv/samba/shared/apply_etl_optimizations.sh

Optimization report saved to: /srv/samba/shared/logs/etl_optimization_report_20250605_020005.txt
Exception ignored in: <function Connection.__del__ at 0x7fa4a01a2e60>
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 353, in __del__
    self.close()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 369, in close
    self._client.disconnect(self._session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1102, in disconnect
    self.send_disconnect(session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1106, in send_disconnect
    self._oprot.writeMessageBegin('disconnect', TMessageType.CALL, self._seqid)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 55, in writeMessageBegin
    self.writeI32(TBinaryProtocol.VERSION_1 | type)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 120, in writeI32
    self.trans.write(buff)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 173, in write
    raise e
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 169, in write
    self.__wbuf.write(buf)
ValueError: I/O operation on closed file.
2025-06-06 02:00:02,282 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
/srv/samba/shared/etl_performance_optimizer.py:73: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:111: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-06 02:00:02,574 - __main__ - ERROR - Failed to analyze memory: Execution failed on sql: 
                SELECT 
                    DATE_TRUNC('hour', metric_time) as hour,
                    AVG(memory_mb) as avg_memory,
                    MAX(memory_mb) as max_memory,
                    AVG(rows_per_second) as avg_speed
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '24' HOUR
                GROUP BY hour
                ORDER BY hour
            
SQL Error: Encountered "hour" at line 2, column 59.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
/srv/samba/shared/etl_performance_optimizer.py:157: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:210: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:257: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:374: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:73: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:111: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-06 02:00:02,915 - __main__ - ERROR - Failed to analyze memory: Execution failed on sql: 
                SELECT 
                    DATE_TRUNC('hour', metric_time) as hour,
                    AVG(memory_mb) as avg_memory,
                    MAX(memory_mb) as max_memory,
                    AVG(rows_per_second) as avg_speed
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '24' HOUR
                GROUP BY hour
                ORDER BY hour
            
SQL Error: Encountered "hour" at line 2, column 59.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
/srv/samba/shared/etl_performance_optimizer.py:157: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:210: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:257: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-06 02:00:03,091 - __main__ - INFO - Optimization script generated: /srv/samba/shared/apply_etl_optimizations.sh
================================================================================
ETL OPTIMIZATION REPORT - 2025-06-06 02:00:02
================================================================================

## DISK I/O ANALYSIS
----------------------------------------

## MEMORY USAGE ANALYSIS
----------------------------------------

## GPU UTILIZATION ANALYSIS
----------------------------------------
GPU Enabled: No
• No GPU usage detected
• Consider: Enabling GPU acceleration for better performance

## QUERY PERFORMANCE ANALYSIS
----------------------------------------

## DATA DISTRIBUTION ANALYSIS
----------------------------------------
Index Distribution:
  NIFTY: 14,953,923 rows, 590 days
  BANKNIFTY: 1,354,604 rows, 140 days
  MIDCAPNIFTY: 26,830 rows, 3 days
  SENSEX: 324,451 rows, 38 days
• Significant data imbalance: 557.4x difference
• Consider: Partitioning by index, separate tables for large indices
• Sparse data detected for:
•   - NIFTY: 67.4% density
•   - BANKNIFTY: 37.9% density
•   - SENSEX: 10.1% density
• Consider: Data validation, filling missing dates

## AUTO-TUNING RESULTS
----------------------------------------
Auto-tuning not available (insufficient data)

## GENERATED OPTIMIZATION SCRIPTS
----------------------------------------
1. /srv/samba/shared/apply_etl_optimizations.sh - Apply recommended optimizations
2. /srv/samba/shared/etl_tuning_params.json - Auto-tuned parameters

================================================================================

Generating optimization script...
Optimization script saved to: /srv/samba/shared/apply_etl_optimizations.sh

Optimization report saved to: /srv/samba/shared/logs/etl_optimization_report_20250606_020003.txt
Exception ignored in: <function Connection.__del__ at 0x7fa89b9f6e60>
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 353, in __del__
    self.close()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 369, in close
    self._client.disconnect(self._session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1102, in disconnect
    self.send_disconnect(session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1106, in send_disconnect
    self._oprot.writeMessageBegin('disconnect', TMessageType.CALL, self._seqid)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 55, in writeMessageBegin
    self.writeI32(TBinaryProtocol.VERSION_1 | type)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 120, in writeI32
    self.trans.write(buff)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 173, in write
    raise e
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 169, in write
    self.__wbuf.write(buf)
ValueError: I/O operation on closed file.
2025-06-07 02:00:02,688 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
/srv/samba/shared/etl_performance_optimizer.py:73: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:111: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-07 02:00:03,006 - __main__ - ERROR - Failed to analyze memory: Execution failed on sql: 
                SELECT 
                    DATE_TRUNC('hour', metric_time) as hour,
                    AVG(memory_mb) as avg_memory,
                    MAX(memory_mb) as max_memory,
                    AVG(rows_per_second) as avg_speed
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '24' HOUR
                GROUP BY hour
                ORDER BY hour
            
SQL Error: Encountered "hour" at line 2, column 59.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
/srv/samba/shared/etl_performance_optimizer.py:157: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:210: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:257: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:374: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:73: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:111: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-07 02:00:03,347 - __main__ - ERROR - Failed to analyze memory: Execution failed on sql: 
                SELECT 
                    DATE_TRUNC('hour', metric_time) as hour,
                    AVG(memory_mb) as avg_memory,
                    MAX(memory_mb) as max_memory,
                    AVG(rows_per_second) as avg_speed
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '24' HOUR
                GROUP BY hour
                ORDER BY hour
            
SQL Error: Encountered "hour" at line 2, column 59.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
/srv/samba/shared/etl_performance_optimizer.py:157: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:210: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:257: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-07 02:00:03,536 - __main__ - INFO - Optimization script generated: /srv/samba/shared/apply_etl_optimizations.sh
================================================================================
ETL OPTIMIZATION REPORT - 2025-06-07 02:00:02
================================================================================

## DISK I/O ANALYSIS
----------------------------------------

## MEMORY USAGE ANALYSIS
----------------------------------------

## GPU UTILIZATION ANALYSIS
----------------------------------------
GPU Enabled: No
• No GPU usage detected
• Consider: Enabling GPU acceleration for better performance

## QUERY PERFORMANCE ANALYSIS
----------------------------------------

## DATA DISTRIBUTION ANALYSIS
----------------------------------------
Index Distribution:
  NIFTY: 14,953,923 rows, 590 days
  BANKNIFTY: 1,354,604 rows, 140 days
  MIDCAPNIFTY: 26,830 rows, 3 days
  SENSEX: 324,451 rows, 38 days
• Significant data imbalance: 557.4x difference
• Consider: Partitioning by index, separate tables for large indices
• Sparse data detected for:
•   - NIFTY: 67.4% density
•   - BANKNIFTY: 37.9% density
•   - SENSEX: 10.1% density
• Consider: Data validation, filling missing dates

## AUTO-TUNING RESULTS
----------------------------------------
Auto-tuning not available (insufficient data)

## GENERATED OPTIMIZATION SCRIPTS
----------------------------------------
1. /srv/samba/shared/apply_etl_optimizations.sh - Apply recommended optimizations
2. /srv/samba/shared/etl_tuning_params.json - Auto-tuned parameters

================================================================================

Generating optimization script...
Optimization script saved to: /srv/samba/shared/apply_etl_optimizations.sh

Optimization report saved to: /srv/samba/shared/logs/etl_optimization_report_20250607_020003.txt
Exception ignored in: <function Connection.__del__ at 0x7f0a0e7bae60>
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 353, in __del__
    self.close()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 369, in close
    self._client.disconnect(self._session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1102, in disconnect
    self.send_disconnect(session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1106, in send_disconnect
    self._oprot.writeMessageBegin('disconnect', TMessageType.CALL, self._seqid)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 55, in writeMessageBegin
    self.writeI32(TBinaryProtocol.VERSION_1 | type)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 120, in writeI32
    self.trans.write(buff)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 173, in write
    raise e
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 169, in write
    self.__wbuf.write(buf)
ValueError: I/O operation on closed file.
2025-06-08 02:00:01,955 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
/srv/samba/shared/etl_performance_optimizer.py:73: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:111: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-08 02:00:02,263 - __main__ - ERROR - Failed to analyze memory: Execution failed on sql: 
                SELECT 
                    DATE_TRUNC('hour', metric_time) as hour,
                    AVG(memory_mb) as avg_memory,
                    MAX(memory_mb) as max_memory,
                    AVG(rows_per_second) as avg_speed
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '24' HOUR
                GROUP BY hour
                ORDER BY hour
            
SQL Error: Encountered "hour" at line 2, column 59.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
/srv/samba/shared/etl_performance_optimizer.py:157: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:210: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:257: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:374: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:73: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:111: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-08 02:00:02,665 - __main__ - ERROR - Failed to analyze memory: Execution failed on sql: 
                SELECT 
                    DATE_TRUNC('hour', metric_time) as hour,
                    AVG(memory_mb) as avg_memory,
                    MAX(memory_mb) as max_memory,
                    AVG(rows_per_second) as avg_speed
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '24' HOUR
                GROUP BY hour
                ORDER BY hour
            
SQL Error: Encountered "hour" at line 2, column 59.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
/srv/samba/shared/etl_performance_optimizer.py:157: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:210: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:257: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-08 02:00:02,834 - __main__ - INFO - Optimization script generated: /srv/samba/shared/apply_etl_optimizations.sh
================================================================================
ETL OPTIMIZATION REPORT - 2025-06-08 02:00:02
================================================================================

## DISK I/O ANALYSIS
----------------------------------------

## MEMORY USAGE ANALYSIS
----------------------------------------

## GPU UTILIZATION ANALYSIS
----------------------------------------
GPU Enabled: No
• No GPU usage detected
• Consider: Enabling GPU acceleration for better performance

## QUERY PERFORMANCE ANALYSIS
----------------------------------------

## DATA DISTRIBUTION ANALYSIS
----------------------------------------
Index Distribution:
  NIFTY: 14,953,923 rows, 590 days
  BANKNIFTY: 1,354,604 rows, 140 days
  MIDCAPNIFTY: 26,830 rows, 3 days
  SENSEX: 324,451 rows, 38 days
• Significant data imbalance: 557.4x difference
• Consider: Partitioning by index, separate tables for large indices
• Sparse data detected for:
•   - NIFTY: 67.4% density
•   - BANKNIFTY: 37.9% density
•   - SENSEX: 10.1% density
• Consider: Data validation, filling missing dates

## AUTO-TUNING RESULTS
----------------------------------------
Auto-tuning not available (insufficient data)

## GENERATED OPTIMIZATION SCRIPTS
----------------------------------------
1. /srv/samba/shared/apply_etl_optimizations.sh - Apply recommended optimizations
2. /srv/samba/shared/etl_tuning_params.json - Auto-tuned parameters

================================================================================

Generating optimization script...
Optimization script saved to: /srv/samba/shared/apply_etl_optimizations.sh

Optimization report saved to: /srv/samba/shared/logs/etl_optimization_report_20250608_020002.txt
Exception ignored in: <function Connection.__del__ at 0x7fe7cc8cee60>
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 353, in __del__
    self.close()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 369, in close
    self._client.disconnect(self._session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1102, in disconnect
    self.send_disconnect(session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1106, in send_disconnect
    self._oprot.writeMessageBegin('disconnect', TMessageType.CALL, self._seqid)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 55, in writeMessageBegin
    self.writeI32(TBinaryProtocol.VERSION_1 | type)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 120, in writeI32
    self.trans.write(buff)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 173, in write
    raise e
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 169, in write
    self.__wbuf.write(buf)
ValueError: I/O operation on closed file.
2025-06-09 02:00:02,137 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
/srv/samba/shared/etl_performance_optimizer.py:73: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:111: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-09 02:00:02,439 - __main__ - ERROR - Failed to analyze memory: Execution failed on sql: 
                SELECT 
                    DATE_TRUNC('hour', metric_time) as hour,
                    AVG(memory_mb) as avg_memory,
                    MAX(memory_mb) as max_memory,
                    AVG(rows_per_second) as avg_speed
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '24' HOUR
                GROUP BY hour
                ORDER BY hour
            
SQL Error: Encountered "hour" at line 2, column 59.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
/srv/samba/shared/etl_performance_optimizer.py:157: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:210: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:257: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:374: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:73: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:111: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-09 02:00:02,819 - __main__ - ERROR - Failed to analyze memory: Execution failed on sql: 
                SELECT 
                    DATE_TRUNC('hour', metric_time) as hour,
                    AVG(memory_mb) as avg_memory,
                    MAX(memory_mb) as max_memory,
                    AVG(rows_per_second) as avg_speed
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '24' HOUR
                GROUP BY hour
                ORDER BY hour
            
SQL Error: Encountered "hour" at line 2, column 59.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
/srv/samba/shared/etl_performance_optimizer.py:157: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:210: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:257: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-09 02:00:02,991 - __main__ - INFO - Optimization script generated: /srv/samba/shared/apply_etl_optimizations.sh
================================================================================
ETL OPTIMIZATION REPORT - 2025-06-09 02:00:02
================================================================================

## DISK I/O ANALYSIS
----------------------------------------

## MEMORY USAGE ANALYSIS
----------------------------------------

## GPU UTILIZATION ANALYSIS
----------------------------------------
GPU Enabled: No
• No GPU usage detected
• Consider: Enabling GPU acceleration for better performance

## QUERY PERFORMANCE ANALYSIS
----------------------------------------

## DATA DISTRIBUTION ANALYSIS
----------------------------------------
Index Distribution:
  NIFTY: 14,953,923 rows, 590 days
  BANKNIFTY: 1,354,604 rows, 140 days
  MIDCAPNIFTY: 26,830 rows, 3 days
  SENSEX: 324,451 rows, 38 days
• Significant data imbalance: 557.4x difference
• Consider: Partitioning by index, separate tables for large indices
• Sparse data detected for:
•   - NIFTY: 67.4% density
•   - BANKNIFTY: 37.9% density
•   - SENSEX: 10.1% density
• Consider: Data validation, filling missing dates

## AUTO-TUNING RESULTS
----------------------------------------
Auto-tuning not available (insufficient data)

## GENERATED OPTIMIZATION SCRIPTS
----------------------------------------
1. /srv/samba/shared/apply_etl_optimizations.sh - Apply recommended optimizations
2. /srv/samba/shared/etl_tuning_params.json - Auto-tuned parameters

================================================================================

Generating optimization script...
Optimization script saved to: /srv/samba/shared/apply_etl_optimizations.sh

Optimization report saved to: /srv/samba/shared/logs/etl_optimization_report_20250609_020002.txt
Exception ignored in: <function Connection.__del__ at 0x7f0c223aae60>
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 353, in __del__
    self.close()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 369, in close
    self._client.disconnect(self._session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1102, in disconnect
    self.send_disconnect(session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1106, in send_disconnect
    self._oprot.writeMessageBegin('disconnect', TMessageType.CALL, self._seqid)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 55, in writeMessageBegin
    self.writeI32(TBinaryProtocol.VERSION_1 | type)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 120, in writeI32
    self.trans.write(buff)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 173, in write
    raise e
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 169, in write
    self.__wbuf.write(buf)
ValueError: I/O operation on closed file.
2025-06-10 02:00:01,789 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
/srv/samba/shared/etl_performance_optimizer.py:73: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:111: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-10 02:00:02,095 - __main__ - ERROR - Failed to analyze memory: Execution failed on sql: 
                SELECT 
                    DATE_TRUNC('hour', metric_time) as hour,
                    AVG(memory_mb) as avg_memory,
                    MAX(memory_mb) as max_memory,
                    AVG(rows_per_second) as avg_speed
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '24' HOUR
                GROUP BY hour
                ORDER BY hour
            
SQL Error: Encountered "hour" at line 2, column 59.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
/srv/samba/shared/etl_performance_optimizer.py:157: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:210: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:257: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:374: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:73: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:111: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-10 02:00:02,454 - __main__ - ERROR - Failed to analyze memory: Execution failed on sql: 
                SELECT 
                    DATE_TRUNC('hour', metric_time) as hour,
                    AVG(memory_mb) as avg_memory,
                    MAX(memory_mb) as max_memory,
                    AVG(rows_per_second) as avg_speed
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '24' HOUR
                GROUP BY hour
                ORDER BY hour
            
SQL Error: Encountered "hour" at line 2, column 59.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
/srv/samba/shared/etl_performance_optimizer.py:157: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:210: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:257: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-10 02:00:02,633 - __main__ - INFO - Optimization script generated: /srv/samba/shared/apply_etl_optimizations.sh
================================================================================
ETL OPTIMIZATION REPORT - 2025-06-10 02:00:02
================================================================================

## DISK I/O ANALYSIS
----------------------------------------

## MEMORY USAGE ANALYSIS
----------------------------------------

## GPU UTILIZATION ANALYSIS
----------------------------------------
GPU Enabled: No
• No GPU usage detected
• Consider: Enabling GPU acceleration for better performance

## QUERY PERFORMANCE ANALYSIS
----------------------------------------

## DATA DISTRIBUTION ANALYSIS
----------------------------------------
Index Distribution:
  NIFTY: 14,953,923 rows, 590 days
  BANKNIFTY: 1,354,604 rows, 140 days
  MIDCAPNIFTY: 26,830 rows, 3 days
  SENSEX: 324,451 rows, 38 days
• Significant data imbalance: 557.4x difference
• Consider: Partitioning by index, separate tables for large indices
• Sparse data detected for:
•   - NIFTY: 67.4% density
•   - BANKNIFTY: 37.9% density
•   - SENSEX: 10.1% density
• Consider: Data validation, filling missing dates

## AUTO-TUNING RESULTS
----------------------------------------
Auto-tuning not available (insufficient data)

## GENERATED OPTIMIZATION SCRIPTS
----------------------------------------
1. /srv/samba/shared/apply_etl_optimizations.sh - Apply recommended optimizations
2. /srv/samba/shared/etl_tuning_params.json - Auto-tuned parameters

================================================================================

Generating optimization script...
Optimization script saved to: /srv/samba/shared/apply_etl_optimizations.sh

Optimization report saved to: /srv/samba/shared/logs/etl_optimization_report_20250610_020002.txt
Exception ignored in: <function Connection.__del__ at 0x7f2383e3ee60>
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 353, in __del__
    self.close()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 369, in close
    self._client.disconnect(self._session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1102, in disconnect
    self.send_disconnect(session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1106, in send_disconnect
    self._oprot.writeMessageBegin('disconnect', TMessageType.CALL, self._seqid)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 55, in writeMessageBegin
    self.writeI32(TBinaryProtocol.VERSION_1 | type)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 120, in writeI32
    self.trans.write(buff)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 173, in write
    raise e
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 169, in write
    self.__wbuf.write(buf)
ValueError: I/O operation on closed file.
2025-06-11 02:00:02,589 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
/srv/samba/shared/etl_performance_optimizer.py:73: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:111: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-11 02:00:02,887 - __main__ - ERROR - Failed to analyze memory: Execution failed on sql: 
                SELECT 
                    DATE_TRUNC('hour', metric_time) as hour,
                    AVG(memory_mb) as avg_memory,
                    MAX(memory_mb) as max_memory,
                    AVG(rows_per_second) as avg_speed
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '24' HOUR
                GROUP BY hour
                ORDER BY hour
            
SQL Error: Encountered "hour" at line 2, column 59.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
/srv/samba/shared/etl_performance_optimizer.py:157: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:210: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:257: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:374: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:73: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:111: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-11 02:00:03,243 - __main__ - ERROR - Failed to analyze memory: Execution failed on sql: 
                SELECT 
                    DATE_TRUNC('hour', metric_time) as hour,
                    AVG(memory_mb) as avg_memory,
                    MAX(memory_mb) as max_memory,
                    AVG(rows_per_second) as avg_speed
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '24' HOUR
                GROUP BY hour
                ORDER BY hour
            
SQL Error: Encountered "hour" at line 2, column 59.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
/srv/samba/shared/etl_performance_optimizer.py:157: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:210: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_optimizer.py:257: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-11 02:00:03,392 - __main__ - INFO - Optimization script generated: /srv/samba/shared/apply_etl_optimizations.sh
================================================================================
ETL OPTIMIZATION REPORT - 2025-06-11 02:00:02
================================================================================

## DISK I/O ANALYSIS
----------------------------------------

## MEMORY USAGE ANALYSIS
----------------------------------------

## GPU UTILIZATION ANALYSIS
----------------------------------------
GPU Enabled: No
• No GPU usage detected
• Consider: Enabling GPU acceleration for better performance

## QUERY PERFORMANCE ANALYSIS
----------------------------------------

## DATA DISTRIBUTION ANALYSIS
----------------------------------------
Index Distribution:
  NIFTY: 14,953,923 rows, 590 days
  BANKNIFTY: 1,354,604 rows, 140 days
  MIDCAPNIFTY: 26,830 rows, 3 days
  SENSEX: 324,451 rows, 38 days
• Significant data imbalance: 557.4x difference
• Consider: Partitioning by index, separate tables for large indices
• Sparse data detected for:
•   - NIFTY: 67.4% density
•   - BANKNIFTY: 37.9% density
•   - SENSEX: 10.1% density
• Consider: Data validation, filling missing dates

## AUTO-TUNING RESULTS
----------------------------------------
Auto-tuning not available (insufficient data)

## GENERATED OPTIMIZATION SCRIPTS
----------------------------------------
1. /srv/samba/shared/apply_etl_optimizations.sh - Apply recommended optimizations
2. /srv/samba/shared/etl_tuning_params.json - Auto-tuned parameters

================================================================================

Generating optimization script...
Optimization script saved to: /srv/samba/shared/apply_etl_optimizations.sh

Optimization report saved to: /srv/samba/shared/logs/etl_optimization_report_20250611_020003.txt
Exception ignored in: <function Connection.__del__ at 0x7fbd64be6e60>
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 353, in __del__
    self.close()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 369, in close
    self._client.disconnect(self._session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1102, in disconnect
    self.send_disconnect(session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1106, in send_disconnect
    self._oprot.writeMessageBegin('disconnect', TMessageType.CALL, self._seqid)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 55, in writeMessageBegin
    self.writeI32(TBinaryProtocol.VERSION_1 | type)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 120, in writeI32
    self.trans.write(buff)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 173, in write
    raise e
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 169, in write
    self.__wbuf.write(buf)
ValueError: I/O operation on closed file.
