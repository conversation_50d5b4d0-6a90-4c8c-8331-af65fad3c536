================================================================================
ETL OPTIMIZATION REPORT - 2025-06-11 02:00:02
================================================================================

## DISK I/O ANALYSIS
----------------------------------------

## MEMORY USAGE ANALYSIS
----------------------------------------

## GPU UTILIZATION ANALYSIS
----------------------------------------
GPU Enabled: No
• No GPU usage detected
• Consider: Enabling GPU acceleration for better performance

## QUERY PERFORMANCE ANALYSIS
----------------------------------------

## DATA DISTRIBUTION ANALYSIS
----------------------------------------
Index Distribution:
  NIFTY: 14,953,923 rows, 590 days
  BANKNIFTY: 1,354,604 rows, 140 days
  MIDCAPNIFTY: 26,830 rows, 3 days
  SENSEX: 324,451 rows, 38 days
• Significant data imbalance: 557.4x difference
• Consider: Partitioning by index, separate tables for large indices
• Sparse data detected for:
•   - NIFTY: 67.4% density
•   - BANKNIFTY: 37.9% density
•   - SENSEX: 10.1% density
• Consider: Data validation, filling missing dates

## AUTO-TUNING RESULTS
----------------------------------------
Auto-tuning not available (insufficient data)

## GENERATED OPTIMIZATION SCRIPTS
----------------------------------------
1. /srv/samba/shared/apply_etl_optimizations.sh - Apply recommended optimizations
2. /srv/samba/shared/etl_tuning_params.json - Auto-tuned parameters

================================================================================