2025-06-04 12:00:02,340 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 12:00:02,574 - __main__ - INFO - Connected to HeavyDB
2025-06-04 12:00:02,597 - __main__ - INFO - Metrics table ready
/srv/samba/shared/etl_performance_monitor.py:175: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_monitor.py:312: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-04 12:00:02,746 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
================================================================================
ETL PERFORMANCE REPORT - 2025-06-04 12:00:02
================================================================================

## DATA FRESHNESS STATUS
----------------------------------------
⚠️ NIFTY: Stale (9 days behind)
   - Rows: 14,953,923
   - Latest: 2025-05-26
❌ BANKNIFTY: Outdated (152 days behind)
   - Rows: 1,354,604
   - Latest: 2025-01-03
❌ MIDCAPNIFTY: Outdated (152 days behind)
   - Rows: 26,830
   - Latest: 2025-01-03
❌ SENSEX: Outdated (124 days behind)
   - Rows: 324,451
   - Latest: 2025-01-31
❌ FINNIFTY: No Data (-1 days behind)
❌ BANKEX: No Data (-1 days behind)

## PERFORMANCE SUMMARY (Last 24 Hours)
----------------------------------------

## PERFORMANCE TRENDS (Last 7 Days)
----------------------------------------

## CURRENT SYSTEM RESOURCES
----------------------------------------
CPU Usage: 3.8%
Memory Usage: 14,659 MB
GPU Utilization: 0.0%
GPU Memory: 4,528 MB

================================================================================

## ALERTS
----------------------------------------
🟡 BANKNIFTY data is 152 days old
🟡 MIDCAPNIFTY data is 152 days old
🟡 SENSEX data is 124 days old

Report saved to: /srv/samba/shared/logs/etl_performance_report_20250604_120003.txt
Exception ignored in: <function Connection.__del__ at 0x7f0b00a37250>
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 353, in __del__
    self.close()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 369, in close
    self._client.disconnect(self._session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1102, in disconnect
    self.send_disconnect(session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1106, in send_disconnect
    self._oprot.writeMessageBegin('disconnect', TMessageType.CALL, self._seqid)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 55, in writeMessageBegin
    self.writeI32(TBinaryProtocol.VERSION_1 | type)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 120, in writeI32
    self.trans.write(buff)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 173, in write
    raise e
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 169, in write
    self.__wbuf.write(buf)
ValueError: I/O operation on closed file.
2025-06-04 18:00:02,717 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-04 18:00:02,950 - __main__ - INFO - Connected to HeavyDB
2025-06-04 18:00:02,961 - __main__ - INFO - Metrics table ready
/srv/samba/shared/etl_performance_monitor.py:175: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_monitor.py:312: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-04 18:00:03,182 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
================================================================================
ETL PERFORMANCE REPORT - 2025-06-04 18:00:02
================================================================================

## DATA FRESHNESS STATUS
----------------------------------------
⚠️ NIFTY: Stale (9 days behind)
   - Rows: 14,953,923
   - Latest: 2025-05-26
❌ BANKNIFTY: Outdated (152 days behind)
   - Rows: 1,354,604
   - Latest: 2025-01-03
❌ MIDCAPNIFTY: Outdated (152 days behind)
   - Rows: 26,830
   - Latest: 2025-01-03
❌ SENSEX: Outdated (124 days behind)
   - Rows: 324,451
   - Latest: 2025-01-31
❌ FINNIFTY: No Data (-1 days behind)
❌ BANKEX: No Data (-1 days behind)

## PERFORMANCE SUMMARY (Last 24 Hours)
----------------------------------------

## PERFORMANCE TRENDS (Last 7 Days)
----------------------------------------

## CURRENT SYSTEM RESOURCES
----------------------------------------
CPU Usage: 4.5%
Memory Usage: 16,431 MB
GPU Utilization: 0.0%
GPU Memory: 4,528 MB

================================================================================

## ALERTS
----------------------------------------
🟡 BANKNIFTY data is 152 days old
🟡 MIDCAPNIFTY data is 152 days old
🟡 SENSEX data is 124 days old

Report saved to: /srv/samba/shared/logs/etl_performance_report_20250604_180004.txt
Exception ignored in: <function Connection.__del__ at 0x7fc8f2b47250>
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 353, in __del__
    self.close()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 369, in close
    self._client.disconnect(self._session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1102, in disconnect
    self.send_disconnect(session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1106, in send_disconnect
    self._oprot.writeMessageBegin('disconnect', TMessageType.CALL, self._seqid)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 55, in writeMessageBegin
    self.writeI32(TBinaryProtocol.VERSION_1 | type)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 120, in writeI32
    self.trans.write(buff)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 173, in write
    raise e
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 169, in write
    self.__wbuf.write(buf)
ValueError: I/O operation on closed file.
2025-06-05 00:00:02,344 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 00:00:02,580 - __main__ - INFO - Connected to HeavyDB
2025-06-05 00:00:02,620 - __main__ - INFO - Metrics table ready
/srv/samba/shared/etl_performance_monitor.py:175: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_monitor.py:312: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-05 00:00:02,774 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
================================================================================
ETL PERFORMANCE REPORT - 2025-06-05 00:00:02
================================================================================

## DATA FRESHNESS STATUS
----------------------------------------
⚠️ NIFTY: Stale (10 days behind)
   - Rows: 14,953,923
   - Latest: 2025-05-26
❌ BANKNIFTY: Outdated (153 days behind)
   - Rows: 1,354,604
   - Latest: 2025-01-03
❌ MIDCAPNIFTY: Outdated (153 days behind)
   - Rows: 26,830
   - Latest: 2025-01-03
❌ SENSEX: Outdated (125 days behind)
   - Rows: 324,451
   - Latest: 2025-01-31
❌ FINNIFTY: No Data (-1 days behind)
❌ BANKEX: No Data (-1 days behind)

## PERFORMANCE SUMMARY (Last 24 Hours)
----------------------------------------

## PERFORMANCE TRENDS (Last 7 Days)
----------------------------------------

## CURRENT SYSTEM RESOURCES
----------------------------------------
CPU Usage: 3.1%
Memory Usage: 15,026 MB
GPU Utilization: 0.0%
GPU Memory: 4,528 MB

================================================================================

## ALERTS
----------------------------------------
🟡 BANKNIFTY data is 153 days old
🟡 MIDCAPNIFTY data is 153 days old
🟡 SENSEX data is 125 days old

Report saved to: /srv/samba/shared/logs/etl_performance_report_20250605_000003.txt
Exception ignored in: <function Connection.__del__ at 0x7f578584f250>
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 353, in __del__
    self.close()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 369, in close
    self._client.disconnect(self._session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1102, in disconnect
    self.send_disconnect(session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1106, in send_disconnect
    self._oprot.writeMessageBegin('disconnect', TMessageType.CALL, self._seqid)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 55, in writeMessageBegin
    self.writeI32(TBinaryProtocol.VERSION_1 | type)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 120, in writeI32
    self.trans.write(buff)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 173, in write
    raise e
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 169, in write
    self.__wbuf.write(buf)
ValueError: I/O operation on closed file.
2025-06-05 06:00:02,405 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 06:00:02,638 - __main__ - INFO - Connected to HeavyDB
2025-06-05 06:00:02,662 - __main__ - INFO - Metrics table ready
/srv/samba/shared/etl_performance_monitor.py:175: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_monitor.py:312: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-05 06:00:02,814 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
================================================================================
ETL PERFORMANCE REPORT - 2025-06-05 06:00:02
================================================================================

## DATA FRESHNESS STATUS
----------------------------------------
⚠️ NIFTY: Stale (10 days behind)
   - Rows: 14,953,923
   - Latest: 2025-05-26
❌ BANKNIFTY: Outdated (153 days behind)
   - Rows: 1,354,604
   - Latest: 2025-01-03
❌ MIDCAPNIFTY: Outdated (153 days behind)
   - Rows: 26,830
   - Latest: 2025-01-03
❌ SENSEX: Outdated (125 days behind)
   - Rows: 324,451
   - Latest: 2025-01-31
❌ FINNIFTY: No Data (-1 days behind)
❌ BANKEX: No Data (-1 days behind)

## PERFORMANCE SUMMARY (Last 24 Hours)
----------------------------------------

## PERFORMANCE TRENDS (Last 7 Days)
----------------------------------------

## CURRENT SYSTEM RESOURCES
----------------------------------------
CPU Usage: 4.9%
Memory Usage: 17,512 MB
GPU Utilization: 0.0%
GPU Memory: 4,530 MB

================================================================================

## ALERTS
----------------------------------------
🟡 BANKNIFTY data is 153 days old
🟡 MIDCAPNIFTY data is 153 days old
🟡 SENSEX data is 125 days old

Report saved to: /srv/samba/shared/logs/etl_performance_report_20250605_060004.txt
Exception ignored in: <function Connection.__del__ at 0x7f54dcf53250>
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 353, in __del__
    self.close()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 369, in close
    self._client.disconnect(self._session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1102, in disconnect
    self.send_disconnect(session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1106, in send_disconnect
    self._oprot.writeMessageBegin('disconnect', TMessageType.CALL, self._seqid)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 55, in writeMessageBegin
    self.writeI32(TBinaryProtocol.VERSION_1 | type)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 120, in writeI32
    self.trans.write(buff)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 173, in write
    raise e
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 169, in write
    self.__wbuf.write(buf)
ValueError: I/O operation on closed file.
2025-06-05 12:00:01,797 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 12:00:02,033 - __main__ - INFO - Connected to HeavyDB
2025-06-05 12:00:02,047 - __main__ - INFO - Metrics table ready
/srv/samba/shared/etl_performance_monitor.py:175: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_monitor.py:312: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-05 12:00:02,206 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
================================================================================
ETL PERFORMANCE REPORT - 2025-06-05 12:00:02
================================================================================

## DATA FRESHNESS STATUS
----------------------------------------
⚠️ NIFTY: Stale (10 days behind)
   - Rows: 14,953,923
   - Latest: 2025-05-26
❌ BANKNIFTY: Outdated (153 days behind)
   - Rows: 1,354,604
   - Latest: 2025-01-03
❌ MIDCAPNIFTY: Outdated (153 days behind)
   - Rows: 26,830
   - Latest: 2025-01-03
❌ SENSEX: Outdated (125 days behind)
   - Rows: 324,451
   - Latest: 2025-01-31
❌ FINNIFTY: No Data (-1 days behind)
❌ BANKEX: No Data (-1 days behind)

## PERFORMANCE SUMMARY (Last 24 Hours)
----------------------------------------

## PERFORMANCE TRENDS (Last 7 Days)
----------------------------------------

## CURRENT SYSTEM RESOURCES
----------------------------------------
CPU Usage: 3.9%
Memory Usage: 14,857 MB
GPU Utilization: 0.0%
GPU Memory: 4,530 MB

================================================================================

## ALERTS
----------------------------------------
🟡 BANKNIFTY data is 153 days old
🟡 MIDCAPNIFTY data is 153 days old
🟡 SENSEX data is 125 days old

Report saved to: /srv/samba/shared/logs/etl_performance_report_20250605_120003.txt
Exception ignored in: <function Connection.__del__ at 0x7f9805d17250>
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 353, in __del__
    self.close()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 369, in close
    self._client.disconnect(self._session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1102, in disconnect
    self.send_disconnect(session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1106, in send_disconnect
    self._oprot.writeMessageBegin('disconnect', TMessageType.CALL, self._seqid)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 55, in writeMessageBegin
    self.writeI32(TBinaryProtocol.VERSION_1 | type)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 120, in writeI32
    self.trans.write(buff)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 173, in write
    raise e
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 169, in write
    self.__wbuf.write(buf)
ValueError: I/O operation on closed file.
2025-06-05 18:00:01,962 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-05 18:00:02,191 - __main__ - INFO - Connected to HeavyDB
2025-06-05 18:00:02,225 - __main__ - INFO - Metrics table ready
/srv/samba/shared/etl_performance_monitor.py:175: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_monitor.py:312: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-05 18:00:02,372 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
================================================================================
ETL PERFORMANCE REPORT - 2025-06-05 18:00:02
================================================================================

## DATA FRESHNESS STATUS
----------------------------------------
⚠️ NIFTY: Stale (10 days behind)
   - Rows: 14,953,923
   - Latest: 2025-05-26
❌ BANKNIFTY: Outdated (153 days behind)
   - Rows: 1,354,604
   - Latest: 2025-01-03
❌ MIDCAPNIFTY: Outdated (153 days behind)
   - Rows: 26,830
   - Latest: 2025-01-03
❌ SENSEX: Outdated (125 days behind)
   - Rows: 324,451
   - Latest: 2025-01-31
❌ FINNIFTY: No Data (-1 days behind)
❌ BANKEX: No Data (-1 days behind)

## PERFORMANCE SUMMARY (Last 24 Hours)
----------------------------------------

## PERFORMANCE TRENDS (Last 7 Days)
----------------------------------------

## CURRENT SYSTEM RESOURCES
----------------------------------------
CPU Usage: 5.4%
Memory Usage: 15,355 MB
GPU Utilization: 0.0%
GPU Memory: 4,530 MB

================================================================================

## ALERTS
----------------------------------------
🟡 BANKNIFTY data is 153 days old
🟡 MIDCAPNIFTY data is 153 days old
🟡 SENSEX data is 125 days old

Report saved to: /srv/samba/shared/logs/etl_performance_report_20250605_180003.txt
Exception ignored in: <function Connection.__del__ at 0x7fa0aad9b250>
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 353, in __del__
    self.close()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 369, in close
    self._client.disconnect(self._session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1102, in disconnect
    self.send_disconnect(session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1106, in send_disconnect
    self._oprot.writeMessageBegin('disconnect', TMessageType.CALL, self._seqid)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 55, in writeMessageBegin
    self.writeI32(TBinaryProtocol.VERSION_1 | type)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 120, in writeI32
    self.trans.write(buff)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 173, in write
    raise e
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 169, in write
    self.__wbuf.write(buf)
ValueError: I/O operation on closed file.
2025-06-06 00:00:02,113 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 00:00:02,347 - __main__ - INFO - Connected to HeavyDB
2025-06-06 00:00:02,353 - __main__ - INFO - Metrics table ready
/srv/samba/shared/etl_performance_monitor.py:175: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_monitor.py:312: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-06 00:00:02,516 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
================================================================================
ETL PERFORMANCE REPORT - 2025-06-06 00:00:02
================================================================================

## DATA FRESHNESS STATUS
----------------------------------------
⚠️ NIFTY: Stale (11 days behind)
   - Rows: 14,953,923
   - Latest: 2025-05-26
❌ BANKNIFTY: Outdated (154 days behind)
   - Rows: 1,354,604
   - Latest: 2025-01-03
❌ MIDCAPNIFTY: Outdated (154 days behind)
   - Rows: 26,830
   - Latest: 2025-01-03
❌ SENSEX: Outdated (126 days behind)
   - Rows: 324,451
   - Latest: 2025-01-31
❌ FINNIFTY: No Data (-1 days behind)
❌ BANKEX: No Data (-1 days behind)

## PERFORMANCE SUMMARY (Last 24 Hours)
----------------------------------------

## PERFORMANCE TRENDS (Last 7 Days)
----------------------------------------

## CURRENT SYSTEM RESOURCES
----------------------------------------
CPU Usage: 3.9%
Memory Usage: 17,732 MB
GPU Utilization: 0.0%
GPU Memory: 4,530 MB

================================================================================

## ALERTS
----------------------------------------
🟡 BANKNIFTY data is 154 days old
🟡 MIDCAPNIFTY data is 154 days old
🟡 SENSEX data is 126 days old

Report saved to: /srv/samba/shared/logs/etl_performance_report_20250606_000003.txt
Exception ignored in: <function Connection.__del__ at 0x7feecdca3250>
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 353, in __del__
    self.close()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 369, in close
    self._client.disconnect(self._session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1102, in disconnect
    self.send_disconnect(session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1106, in send_disconnect
    self._oprot.writeMessageBegin('disconnect', TMessageType.CALL, self._seqid)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 55, in writeMessageBegin
    self.writeI32(TBinaryProtocol.VERSION_1 | type)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 120, in writeI32
    self.trans.write(buff)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 173, in write
    raise e
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 169, in write
    self.__wbuf.write(buf)
ValueError: I/O operation on closed file.
2025-06-06 06:00:02,635 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 06:00:02,865 - __main__ - INFO - Connected to HeavyDB
2025-06-06 06:00:02,904 - __main__ - INFO - Metrics table ready
/srv/samba/shared/etl_performance_monitor.py:175: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_monitor.py:312: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-06 06:00:03,063 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
================================================================================
ETL PERFORMANCE REPORT - 2025-06-06 06:00:02
================================================================================

## DATA FRESHNESS STATUS
----------------------------------------
⚠️ NIFTY: Stale (11 days behind)
   - Rows: 14,953,923
   - Latest: 2025-05-26
❌ BANKNIFTY: Outdated (154 days behind)
   - Rows: 1,354,604
   - Latest: 2025-01-03
❌ MIDCAPNIFTY: Outdated (154 days behind)
   - Rows: 26,830
   - Latest: 2025-01-03
❌ SENSEX: Outdated (126 days behind)
   - Rows: 324,451
   - Latest: 2025-01-31
❌ FINNIFTY: No Data (-1 days behind)
❌ BANKEX: No Data (-1 days behind)

## PERFORMANCE SUMMARY (Last 24 Hours)
----------------------------------------

## PERFORMANCE TRENDS (Last 7 Days)
----------------------------------------

## CURRENT SYSTEM RESOURCES
----------------------------------------
CPU Usage: 3.7%
Memory Usage: 17,793 MB
GPU Utilization: 0.0%
GPU Memory: 4,530 MB

================================================================================

## ALERTS
----------------------------------------
🟡 BANKNIFTY data is 154 days old
🟡 MIDCAPNIFTY data is 154 days old
🟡 SENSEX data is 126 days old

Report saved to: /srv/samba/shared/logs/etl_performance_report_20250606_060004.txt
Exception ignored in: <function Connection.__del__ at 0x7f387a22f250>
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 353, in __del__
    self.close()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 369, in close
    self._client.disconnect(self._session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1102, in disconnect
    self.send_disconnect(session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1106, in send_disconnect
    self._oprot.writeMessageBegin('disconnect', TMessageType.CALL, self._seqid)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 55, in writeMessageBegin
    self.writeI32(TBinaryProtocol.VERSION_1 | type)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 120, in writeI32
    self.trans.write(buff)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 173, in write
    raise e
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 169, in write
    self.__wbuf.write(buf)
ValueError: I/O operation on closed file.
2025-06-06 12:00:01,884 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 12:00:02,117 - __main__ - INFO - Connected to HeavyDB
2025-06-06 12:00:02,131 - __main__ - INFO - Metrics table ready
/srv/samba/shared/etl_performance_monitor.py:175: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_monitor.py:312: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-06 12:00:02,274 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
================================================================================
ETL PERFORMANCE REPORT - 2025-06-06 12:00:02
================================================================================

## DATA FRESHNESS STATUS
----------------------------------------
⚠️ NIFTY: Stale (11 days behind)
   - Rows: 14,953,923
   - Latest: 2025-05-26
❌ BANKNIFTY: Outdated (154 days behind)
   - Rows: 1,354,604
   - Latest: 2025-01-03
❌ MIDCAPNIFTY: Outdated (154 days behind)
   - Rows: 26,830
   - Latest: 2025-01-03
❌ SENSEX: Outdated (126 days behind)
   - Rows: 324,451
   - Latest: 2025-01-31
❌ FINNIFTY: No Data (-1 days behind)
❌ BANKEX: No Data (-1 days behind)

## PERFORMANCE SUMMARY (Last 24 Hours)
----------------------------------------

## PERFORMANCE TRENDS (Last 7 Days)
----------------------------------------

## CURRENT SYSTEM RESOURCES
----------------------------------------
CPU Usage: 3.8%
Memory Usage: 15,135 MB
GPU Utilization: 0.0%
GPU Memory: 4,530 MB

================================================================================

## ALERTS
----------------------------------------
🟡 BANKNIFTY data is 154 days old
🟡 MIDCAPNIFTY data is 154 days old
🟡 SENSEX data is 126 days old

Report saved to: /srv/samba/shared/logs/etl_performance_report_20250606_120003.txt
Exception ignored in: <function Connection.__del__ at 0x7f2ca2b97250>
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 353, in __del__
    self.close()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 369, in close
    self._client.disconnect(self._session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1102, in disconnect
    self.send_disconnect(session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1106, in send_disconnect
    self._oprot.writeMessageBegin('disconnect', TMessageType.CALL, self._seqid)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 55, in writeMessageBegin
    self.writeI32(TBinaryProtocol.VERSION_1 | type)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 120, in writeI32
    self.trans.write(buff)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 173, in write
    raise e
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 169, in write
    self.__wbuf.write(buf)
ValueError: I/O operation on closed file.
2025-06-06 18:00:02,145 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-06 18:00:02,382 - __main__ - INFO - Connected to HeavyDB
2025-06-06 18:00:02,388 - __main__ - INFO - Metrics table ready
/srv/samba/shared/etl_performance_monitor.py:175: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_monitor.py:312: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-06 18:00:02,534 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
================================================================================
ETL PERFORMANCE REPORT - 2025-06-06 18:00:02
================================================================================

## DATA FRESHNESS STATUS
----------------------------------------
⚠️ NIFTY: Stale (11 days behind)
   - Rows: 14,953,923
   - Latest: 2025-05-26
❌ BANKNIFTY: Outdated (154 days behind)
   - Rows: 1,354,604
   - Latest: 2025-01-03
❌ MIDCAPNIFTY: Outdated (154 days behind)
   - Rows: 26,830
   - Latest: 2025-01-03
❌ SENSEX: Outdated (126 days behind)
   - Rows: 324,451
   - Latest: 2025-01-31
❌ FINNIFTY: No Data (-1 days behind)
❌ BANKEX: No Data (-1 days behind)

## PERFORMANCE SUMMARY (Last 24 Hours)
----------------------------------------

## PERFORMANCE TRENDS (Last 7 Days)
----------------------------------------

## CURRENT SYSTEM RESOURCES
----------------------------------------
CPU Usage: 3.9%
Memory Usage: 15,651 MB
GPU Utilization: 0.0%
GPU Memory: 4,530 MB

================================================================================

## ALERTS
----------------------------------------
🟡 BANKNIFTY data is 154 days old
🟡 MIDCAPNIFTY data is 154 days old
🟡 SENSEX data is 126 days old

Report saved to: /srv/samba/shared/logs/etl_performance_report_20250606_180003.txt
Exception ignored in: <function Connection.__del__ at 0x7fe5d3dcf250>
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 353, in __del__
    self.close()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 369, in close
    self._client.disconnect(self._session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1102, in disconnect
    self.send_disconnect(session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1106, in send_disconnect
    self._oprot.writeMessageBegin('disconnect', TMessageType.CALL, self._seqid)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 55, in writeMessageBegin
    self.writeI32(TBinaryProtocol.VERSION_1 | type)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 120, in writeI32
    self.trans.write(buff)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 173, in write
    raise e
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 169, in write
    self.__wbuf.write(buf)
ValueError: I/O operation on closed file.
2025-06-07 00:00:02,486 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 00:00:02,721 - __main__ - INFO - Connected to HeavyDB
2025-06-07 00:00:02,749 - __main__ - INFO - Metrics table ready
/srv/samba/shared/etl_performance_monitor.py:175: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_monitor.py:312: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-07 00:00:02,910 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
================================================================================
ETL PERFORMANCE REPORT - 2025-06-07 00:00:02
================================================================================

## DATA FRESHNESS STATUS
----------------------------------------
⚠️ NIFTY: Stale (12 days behind)
   - Rows: 14,953,923
   - Latest: 2025-05-26
❌ BANKNIFTY: Outdated (155 days behind)
   - Rows: 1,354,604
   - Latest: 2025-01-03
❌ MIDCAPNIFTY: Outdated (155 days behind)
   - Rows: 26,830
   - Latest: 2025-01-03
❌ SENSEX: Outdated (127 days behind)
   - Rows: 324,451
   - Latest: 2025-01-31
❌ FINNIFTY: No Data (-1 days behind)
❌ BANKEX: No Data (-1 days behind)

## PERFORMANCE SUMMARY (Last 24 Hours)
----------------------------------------

## PERFORMANCE TRENDS (Last 7 Days)
----------------------------------------

## CURRENT SYSTEM RESOURCES
----------------------------------------
CPU Usage: 3.8%
Memory Usage: 16,164 MB
GPU Utilization: 0.0%
GPU Memory: 4,530 MB

================================================================================

## ALERTS
----------------------------------------
🟡 BANKNIFTY data is 155 days old
🟡 MIDCAPNIFTY data is 155 days old
🟡 SENSEX data is 127 days old

Report saved to: /srv/samba/shared/logs/etl_performance_report_20250607_000004.txt
Exception ignored in: <function Connection.__del__ at 0x7f0a58c4f250>
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 353, in __del__
    self.close()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 369, in close
    self._client.disconnect(self._session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1102, in disconnect
    self.send_disconnect(session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1106, in send_disconnect
    self._oprot.writeMessageBegin('disconnect', TMessageType.CALL, self._seqid)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 55, in writeMessageBegin
    self.writeI32(TBinaryProtocol.VERSION_1 | type)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 120, in writeI32
    self.trans.write(buff)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 173, in write
    raise e
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 169, in write
    self.__wbuf.write(buf)
ValueError: I/O operation on closed file.
2025-06-07 06:00:02,143 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 06:00:02,378 - __main__ - INFO - Connected to HeavyDB
2025-06-07 06:00:02,382 - __main__ - INFO - Metrics table ready
/srv/samba/shared/etl_performance_monitor.py:175: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_monitor.py:312: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-07 06:00:02,538 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
================================================================================
ETL PERFORMANCE REPORT - 2025-06-07 06:00:02
================================================================================

## DATA FRESHNESS STATUS
----------------------------------------
⚠️ NIFTY: Stale (12 days behind)
   - Rows: 14,953,923
   - Latest: 2025-05-26
❌ BANKNIFTY: Outdated (155 days behind)
   - Rows: 1,354,604
   - Latest: 2025-01-03
❌ MIDCAPNIFTY: Outdated (155 days behind)
   - Rows: 26,830
   - Latest: 2025-01-03
❌ SENSEX: Outdated (127 days behind)
   - Rows: 324,451
   - Latest: 2025-01-31
❌ FINNIFTY: No Data (-1 days behind)
❌ BANKEX: No Data (-1 days behind)

## PERFORMANCE SUMMARY (Last 24 Hours)
----------------------------------------

## PERFORMANCE TRENDS (Last 7 Days)
----------------------------------------

## CURRENT SYSTEM RESOURCES
----------------------------------------
CPU Usage: 4.2%
Memory Usage: 16,242 MB
GPU Utilization: 0.0%
GPU Memory: 4,530 MB

================================================================================

## ALERTS
----------------------------------------
🟡 BANKNIFTY data is 155 days old
🟡 MIDCAPNIFTY data is 155 days old
🟡 SENSEX data is 127 days old

Report saved to: /srv/samba/shared/logs/etl_performance_report_20250607_060003.txt
Exception ignored in: <function Connection.__del__ at 0x7f98a9adf250>
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 353, in __del__
    self.close()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 369, in close
    self._client.disconnect(self._session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1102, in disconnect
    self.send_disconnect(session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1106, in send_disconnect
    self._oprot.writeMessageBegin('disconnect', TMessageType.CALL, self._seqid)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 55, in writeMessageBegin
    self.writeI32(TBinaryProtocol.VERSION_1 | type)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 120, in writeI32
    self.trans.write(buff)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 173, in write
    raise e
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 169, in write
    self.__wbuf.write(buf)
ValueError: I/O operation on closed file.
2025-06-07 12:00:02,068 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 12:00:02,300 - __main__ - INFO - Connected to HeavyDB
2025-06-07 12:00:02,308 - __main__ - INFO - Metrics table ready
/srv/samba/shared/etl_performance_monitor.py:175: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_monitor.py:312: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-07 12:00:02,470 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
================================================================================
ETL PERFORMANCE REPORT - 2025-06-07 12:00:02
================================================================================

## DATA FRESHNESS STATUS
----------------------------------------
⚠️ NIFTY: Stale (12 days behind)
   - Rows: 14,953,923
   - Latest: 2025-05-26
❌ BANKNIFTY: Outdated (155 days behind)
   - Rows: 1,354,604
   - Latest: 2025-01-03
❌ MIDCAPNIFTY: Outdated (155 days behind)
   - Rows: 26,830
   - Latest: 2025-01-03
❌ SENSEX: Outdated (127 days behind)
   - Rows: 324,451
   - Latest: 2025-01-31
❌ FINNIFTY: No Data (-1 days behind)
❌ BANKEX: No Data (-1 days behind)

## PERFORMANCE SUMMARY (Last 24 Hours)
----------------------------------------

## PERFORMANCE TRENDS (Last 7 Days)
----------------------------------------

## CURRENT SYSTEM RESOURCES
----------------------------------------
CPU Usage: 4.0%
Memory Usage: 15,794 MB
GPU Utilization: 0.0%
GPU Memory: 4,530 MB

================================================================================

## ALERTS
----------------------------------------
🟡 BANKNIFTY data is 155 days old
🟡 MIDCAPNIFTY data is 155 days old
🟡 SENSEX data is 127 days old

Report saved to: /srv/samba/shared/logs/etl_performance_report_20250607_120003.txt
Exception ignored in: <function Connection.__del__ at 0x7fb347bc3250>
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 353, in __del__
    self.close()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 369, in close
    self._client.disconnect(self._session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1102, in disconnect
    self.send_disconnect(session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1106, in send_disconnect
    self._oprot.writeMessageBegin('disconnect', TMessageType.CALL, self._seqid)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 55, in writeMessageBegin
    self.writeI32(TBinaryProtocol.VERSION_1 | type)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 120, in writeI32
    self.trans.write(buff)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 173, in write
    raise e
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 169, in write
    self.__wbuf.write(buf)
ValueError: I/O operation on closed file.
2025-06-07 18:00:02,032 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-07 18:00:02,258 - __main__ - INFO - Connected to HeavyDB
2025-06-07 18:00:02,266 - __main__ - INFO - Metrics table ready
/srv/samba/shared/etl_performance_monitor.py:175: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_monitor.py:312: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-07 18:00:02,427 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
================================================================================
ETL PERFORMANCE REPORT - 2025-06-07 18:00:02
================================================================================

## DATA FRESHNESS STATUS
----------------------------------------
⚠️ NIFTY: Stale (12 days behind)
   - Rows: 14,953,923
   - Latest: 2025-05-26
❌ BANKNIFTY: Outdated (155 days behind)
   - Rows: 1,354,604
   - Latest: 2025-01-03
❌ MIDCAPNIFTY: Outdated (155 days behind)
   - Rows: 26,830
   - Latest: 2025-01-03
❌ SENSEX: Outdated (127 days behind)
   - Rows: 324,451
   - Latest: 2025-01-31
❌ FINNIFTY: No Data (-1 days behind)
❌ BANKEX: No Data (-1 days behind)

## PERFORMANCE SUMMARY (Last 24 Hours)
----------------------------------------

## PERFORMANCE TRENDS (Last 7 Days)
----------------------------------------

## CURRENT SYSTEM RESOURCES
----------------------------------------
CPU Usage: 3.2%
Memory Usage: 14,735 MB
GPU Utilization: 0.0%
GPU Memory: 4,530 MB

================================================================================

## ALERTS
----------------------------------------
🟡 BANKNIFTY data is 155 days old
🟡 MIDCAPNIFTY data is 155 days old
🟡 SENSEX data is 127 days old

Report saved to: /srv/samba/shared/logs/etl_performance_report_20250607_180003.txt
Exception ignored in: <function Connection.__del__ at 0x7f73f063b250>
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 353, in __del__
    self.close()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 369, in close
    self._client.disconnect(self._session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1102, in disconnect
    self.send_disconnect(session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1106, in send_disconnect
    self._oprot.writeMessageBegin('disconnect', TMessageType.CALL, self._seqid)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 55, in writeMessageBegin
    self.writeI32(TBinaryProtocol.VERSION_1 | type)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 120, in writeI32
    self.trans.write(buff)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 173, in write
    raise e
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 169, in write
    self.__wbuf.write(buf)
ValueError: I/O operation on closed file.
2025-06-08 00:00:01,865 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 00:00:02,099 - __main__ - INFO - Connected to HeavyDB
2025-06-08 00:00:02,103 - __main__ - INFO - Metrics table ready
/srv/samba/shared/etl_performance_monitor.py:175: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_monitor.py:312: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-08 00:00:02,270 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
================================================================================
ETL PERFORMANCE REPORT - 2025-06-08 00:00:02
================================================================================

## DATA FRESHNESS STATUS
----------------------------------------
⚠️ NIFTY: Stale (13 days behind)
   - Rows: 14,953,923
   - Latest: 2025-05-26
❌ BANKNIFTY: Outdated (156 days behind)
   - Rows: 1,354,604
   - Latest: 2025-01-03
❌ MIDCAPNIFTY: Outdated (156 days behind)
   - Rows: 26,830
   - Latest: 2025-01-03
❌ SENSEX: Outdated (128 days behind)
   - Rows: 324,451
   - Latest: 2025-01-31
❌ FINNIFTY: No Data (-1 days behind)
❌ BANKEX: No Data (-1 days behind)

## PERFORMANCE SUMMARY (Last 24 Hours)
----------------------------------------

## PERFORMANCE TRENDS (Last 7 Days)
----------------------------------------

## CURRENT SYSTEM RESOURCES
----------------------------------------
CPU Usage: 4.7%
Memory Usage: 15,361 MB
GPU Utilization: 0.0%
GPU Memory: 4,530 MB

================================================================================

## ALERTS
----------------------------------------
🟡 BANKNIFTY data is 156 days old
🟡 MIDCAPNIFTY data is 156 days old
🟡 SENSEX data is 128 days old

Report saved to: /srv/samba/shared/logs/etl_performance_report_20250608_000003.txt
Exception ignored in: <function Connection.__del__ at 0x7f779cf43250>
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 353, in __del__
    self.close()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 369, in close
    self._client.disconnect(self._session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1102, in disconnect
    self.send_disconnect(session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1106, in send_disconnect
    self._oprot.writeMessageBegin('disconnect', TMessageType.CALL, self._seqid)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 55, in writeMessageBegin
    self.writeI32(TBinaryProtocol.VERSION_1 | type)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 120, in writeI32
    self.trans.write(buff)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 173, in write
    raise e
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 169, in write
    self.__wbuf.write(buf)
ValueError: I/O operation on closed file.
2025-06-08 06:00:02,212 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 06:00:02,442 - __main__ - INFO - Connected to HeavyDB
2025-06-08 06:00:02,478 - __main__ - INFO - Metrics table ready
/srv/samba/shared/etl_performance_monitor.py:175: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_monitor.py:312: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-08 06:00:02,699 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
================================================================================
ETL PERFORMANCE REPORT - 2025-06-08 06:00:02
================================================================================

## DATA FRESHNESS STATUS
----------------------------------------
⚠️ NIFTY: Stale (13 days behind)
   - Rows: 14,953,923
   - Latest: 2025-05-26
❌ BANKNIFTY: Outdated (156 days behind)
   - Rows: 1,354,604
   - Latest: 2025-01-03
❌ MIDCAPNIFTY: Outdated (156 days behind)
   - Rows: 26,830
   - Latest: 2025-01-03
❌ SENSEX: Outdated (128 days behind)
   - Rows: 324,451
   - Latest: 2025-01-31
❌ FINNIFTY: No Data (-1 days behind)
❌ BANKEX: No Data (-1 days behind)

## PERFORMANCE SUMMARY (Last 24 Hours)
----------------------------------------

## PERFORMANCE TRENDS (Last 7 Days)
----------------------------------------

## CURRENT SYSTEM RESOURCES
----------------------------------------
CPU Usage: 2.9%
Memory Usage: 15,411 MB
GPU Utilization: 0.0%
GPU Memory: 4,530 MB

================================================================================

## ALERTS
----------------------------------------
🟡 BANKNIFTY data is 156 days old
🟡 MIDCAPNIFTY data is 156 days old
🟡 SENSEX data is 128 days old

Report saved to: /srv/samba/shared/logs/etl_performance_report_20250608_060003.txt
Exception ignored in: <function Connection.__del__ at 0x7f6fefcb3250>
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 353, in __del__
    self.close()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 369, in close
    self._client.disconnect(self._session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1102, in disconnect
    self.send_disconnect(session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1106, in send_disconnect
    self._oprot.writeMessageBegin('disconnect', TMessageType.CALL, self._seqid)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 55, in writeMessageBegin
    self.writeI32(TBinaryProtocol.VERSION_1 | type)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 120, in writeI32
    self.trans.write(buff)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 173, in write
    raise e
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 169, in write
    self.__wbuf.write(buf)
ValueError: I/O operation on closed file.
2025-06-08 12:00:02,160 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 12:00:02,389 - __main__ - INFO - Connected to HeavyDB
2025-06-08 12:00:02,397 - __main__ - INFO - Metrics table ready
/srv/samba/shared/etl_performance_monitor.py:175: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_monitor.py:312: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-08 12:00:02,553 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
================================================================================
ETL PERFORMANCE REPORT - 2025-06-08 12:00:02
================================================================================

## DATA FRESHNESS STATUS
----------------------------------------
⚠️ NIFTY: Stale (13 days behind)
   - Rows: 14,953,923
   - Latest: 2025-05-26
❌ BANKNIFTY: Outdated (156 days behind)
   - Rows: 1,354,604
   - Latest: 2025-01-03
❌ MIDCAPNIFTY: Outdated (156 days behind)
   - Rows: 26,830
   - Latest: 2025-01-03
❌ SENSEX: Outdated (128 days behind)
   - Rows: 324,451
   - Latest: 2025-01-31
❌ FINNIFTY: No Data (-1 days behind)
❌ BANKEX: No Data (-1 days behind)

## PERFORMANCE SUMMARY (Last 24 Hours)
----------------------------------------

## PERFORMANCE TRENDS (Last 7 Days)
----------------------------------------

## CURRENT SYSTEM RESOURCES
----------------------------------------
CPU Usage: 3.5%
Memory Usage: 15,265 MB
GPU Utilization: 0.0%
GPU Memory: 4,530 MB

================================================================================

## ALERTS
----------------------------------------
🟡 BANKNIFTY data is 156 days old
🟡 MIDCAPNIFTY data is 156 days old
🟡 SENSEX data is 128 days old

Report saved to: /srv/samba/shared/logs/etl_performance_report_20250608_120003.txt
Exception ignored in: <function Connection.__del__ at 0x7f3f55b63250>
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 353, in __del__
    self.close()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 369, in close
    self._client.disconnect(self._session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1102, in disconnect
    self.send_disconnect(session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1106, in send_disconnect
    self._oprot.writeMessageBegin('disconnect', TMessageType.CALL, self._seqid)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 55, in writeMessageBegin
    self.writeI32(TBinaryProtocol.VERSION_1 | type)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 120, in writeI32
    self.trans.write(buff)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 173, in write
    raise e
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 169, in write
    self.__wbuf.write(buf)
ValueError: I/O operation on closed file.
2025-06-08 18:00:02,104 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-08 18:00:02,334 - __main__ - INFO - Connected to HeavyDB
2025-06-08 18:00:02,370 - __main__ - INFO - Metrics table ready
/srv/samba/shared/etl_performance_monitor.py:175: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_monitor.py:312: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-08 18:00:02,591 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
================================================================================
ETL PERFORMANCE REPORT - 2025-06-08 18:00:02
================================================================================

## DATA FRESHNESS STATUS
----------------------------------------
⚠️ NIFTY: Stale (13 days behind)
   - Rows: 14,953,923
   - Latest: 2025-05-26
❌ BANKNIFTY: Outdated (156 days behind)
   - Rows: 1,354,604
   - Latest: 2025-01-03
❌ MIDCAPNIFTY: Outdated (156 days behind)
   - Rows: 26,830
   - Latest: 2025-01-03
❌ SENSEX: Outdated (128 days behind)
   - Rows: 324,451
   - Latest: 2025-01-31
❌ FINNIFTY: No Data (-1 days behind)
❌ BANKEX: No Data (-1 days behind)

## PERFORMANCE SUMMARY (Last 24 Hours)
----------------------------------------

## PERFORMANCE TRENDS (Last 7 Days)
----------------------------------------

## CURRENT SYSTEM RESOURCES
----------------------------------------
CPU Usage: 2.9%
Memory Usage: 15,258 MB
GPU Utilization: 0.0%
GPU Memory: 4,530 MB

================================================================================

## ALERTS
----------------------------------------
🟡 BANKNIFTY data is 156 days old
🟡 MIDCAPNIFTY data is 156 days old
🟡 SENSEX data is 128 days old

Report saved to: /srv/samba/shared/logs/etl_performance_report_20250608_180003.txt
Exception ignored in: <function Connection.__del__ at 0x7f3d688eb250>
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 353, in __del__
    self.close()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 369, in close
    self._client.disconnect(self._session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1102, in disconnect
    self.send_disconnect(session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1106, in send_disconnect
    self._oprot.writeMessageBegin('disconnect', TMessageType.CALL, self._seqid)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 55, in writeMessageBegin
    self.writeI32(TBinaryProtocol.VERSION_1 | type)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 120, in writeI32
    self.trans.write(buff)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 173, in write
    raise e
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 169, in write
    self.__wbuf.write(buf)
ValueError: I/O operation on closed file.
2025-06-09 00:00:02,022 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 00:00:02,253 - __main__ - INFO - Connected to HeavyDB
2025-06-09 00:00:02,258 - __main__ - INFO - Metrics table ready
/srv/samba/shared/etl_performance_monitor.py:175: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_monitor.py:312: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-09 00:00:02,421 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
================================================================================
ETL PERFORMANCE REPORT - 2025-06-09 00:00:02
================================================================================

## DATA FRESHNESS STATUS
----------------------------------------
⚠️ NIFTY: Stale (14 days behind)
   - Rows: 14,953,923
   - Latest: 2025-05-26
❌ BANKNIFTY: Outdated (157 days behind)
   - Rows: 1,354,604
   - Latest: 2025-01-03
❌ MIDCAPNIFTY: Outdated (157 days behind)
   - Rows: 26,830
   - Latest: 2025-01-03
❌ SENSEX: Outdated (129 days behind)
   - Rows: 324,451
   - Latest: 2025-01-31
❌ FINNIFTY: No Data (-1 days behind)
❌ BANKEX: No Data (-1 days behind)

## PERFORMANCE SUMMARY (Last 24 Hours)
----------------------------------------

## PERFORMANCE TRENDS (Last 7 Days)
----------------------------------------

## CURRENT SYSTEM RESOURCES
----------------------------------------
CPU Usage: 3.3%
Memory Usage: 15,226 MB
GPU Utilization: 0.0%
GPU Memory: 4,530 MB

================================================================================

## ALERTS
----------------------------------------
🟡 BANKNIFTY data is 157 days old
🟡 MIDCAPNIFTY data is 157 days old
🟡 SENSEX data is 129 days old

Report saved to: /srv/samba/shared/logs/etl_performance_report_20250609_000003.txt
Exception ignored in: <function Connection.__del__ at 0x7fd4f317f250>
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 353, in __del__
    self.close()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 369, in close
    self._client.disconnect(self._session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1102, in disconnect
    self.send_disconnect(session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1106, in send_disconnect
    self._oprot.writeMessageBegin('disconnect', TMessageType.CALL, self._seqid)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 55, in writeMessageBegin
    self.writeI32(TBinaryProtocol.VERSION_1 | type)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 120, in writeI32
    self.trans.write(buff)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 173, in write
    raise e
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 169, in write
    self.__wbuf.write(buf)
ValueError: I/O operation on closed file.
2025-06-09 06:00:02,278 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 06:00:02,505 - __main__ - INFO - Connected to HeavyDB
2025-06-09 06:00:02,511 - __main__ - INFO - Metrics table ready
/srv/samba/shared/etl_performance_monitor.py:175: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_monitor.py:312: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-09 06:00:02,666 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
================================================================================
ETL PERFORMANCE REPORT - 2025-06-09 06:00:02
================================================================================

## DATA FRESHNESS STATUS
----------------------------------------
⚠️ NIFTY: Stale (14 days behind)
   - Rows: 14,953,923
   - Latest: 2025-05-26
❌ BANKNIFTY: Outdated (157 days behind)
   - Rows: 1,354,604
   - Latest: 2025-01-03
❌ MIDCAPNIFTY: Outdated (157 days behind)
   - Rows: 26,830
   - Latest: 2025-01-03
❌ SENSEX: Outdated (129 days behind)
   - Rows: 324,451
   - Latest: 2025-01-31
❌ FINNIFTY: No Data (-1 days behind)
❌ BANKEX: No Data (-1 days behind)

## PERFORMANCE SUMMARY (Last 24 Hours)
----------------------------------------

## PERFORMANCE TRENDS (Last 7 Days)
----------------------------------------

## CURRENT SYSTEM RESOURCES
----------------------------------------
CPU Usage: 3.4%
Memory Usage: 15,463 MB
GPU Utilization: 0.0%
GPU Memory: 4,530 MB

================================================================================

## ALERTS
----------------------------------------
🟡 BANKNIFTY data is 157 days old
🟡 MIDCAPNIFTY data is 157 days old
🟡 SENSEX data is 129 days old

Report saved to: /srv/samba/shared/logs/etl_performance_report_20250609_060003.txt
Exception ignored in: <function Connection.__del__ at 0x7f06def97250>
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 353, in __del__
    self.close()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 369, in close
    self._client.disconnect(self._session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1102, in disconnect
    self.send_disconnect(session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1106, in send_disconnect
    self._oprot.writeMessageBegin('disconnect', TMessageType.CALL, self._seqid)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 55, in writeMessageBegin
    self.writeI32(TBinaryProtocol.VERSION_1 | type)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 120, in writeI32
    self.trans.write(buff)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 173, in write
    raise e
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 169, in write
    self.__wbuf.write(buf)
ValueError: I/O operation on closed file.
2025-06-09 12:00:01,897 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 12:00:02,129 - __main__ - INFO - Connected to HeavyDB
2025-06-09 12:00:02,143 - __main__ - INFO - Metrics table ready
/srv/samba/shared/etl_performance_monitor.py:175: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_monitor.py:312: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-09 12:00:02,399 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
================================================================================
ETL PERFORMANCE REPORT - 2025-06-09 12:00:02
================================================================================

## DATA FRESHNESS STATUS
----------------------------------------
⚠️ NIFTY: Stale (14 days behind)
   - Rows: 14,953,923
   - Latest: 2025-05-26
❌ BANKNIFTY: Outdated (157 days behind)
   - Rows: 1,354,604
   - Latest: 2025-01-03
❌ MIDCAPNIFTY: Outdated (157 days behind)
   - Rows: 26,830
   - Latest: 2025-01-03
❌ SENSEX: Outdated (129 days behind)
   - Rows: 324,451
   - Latest: 2025-01-31
❌ FINNIFTY: No Data (-1 days behind)
❌ BANKEX: No Data (-1 days behind)

## PERFORMANCE SUMMARY (Last 24 Hours)
----------------------------------------

## PERFORMANCE TRENDS (Last 7 Days)
----------------------------------------

## CURRENT SYSTEM RESOURCES
----------------------------------------
CPU Usage: 2.9%
Memory Usage: 15,303 MB
GPU Utilization: 0.0%
GPU Memory: 4,530 MB

================================================================================

## ALERTS
----------------------------------------
🟡 BANKNIFTY data is 157 days old
🟡 MIDCAPNIFTY data is 157 days old
🟡 SENSEX data is 129 days old

Report saved to: /srv/samba/shared/logs/etl_performance_report_20250609_120003.txt
Exception ignored in: <function Connection.__del__ at 0x7f16df0bb250>
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 353, in __del__
    self.close()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 369, in close
    self._client.disconnect(self._session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1102, in disconnect
    self.send_disconnect(session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1106, in send_disconnect
    self._oprot.writeMessageBegin('disconnect', TMessageType.CALL, self._seqid)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 55, in writeMessageBegin
    self.writeI32(TBinaryProtocol.VERSION_1 | type)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 120, in writeI32
    self.trans.write(buff)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 173, in write
    raise e
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 169, in write
    self.__wbuf.write(buf)
ValueError: I/O operation on closed file.
2025-06-09 18:00:01,975 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-09 18:00:02,201 - __main__ - INFO - Connected to HeavyDB
2025-06-09 18:00:02,284 - __main__ - INFO - Metrics table ready
/srv/samba/shared/etl_performance_monitor.py:175: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_monitor.py:312: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-09 18:00:02,423 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
================================================================================
ETL PERFORMANCE REPORT - 2025-06-09 18:00:02
================================================================================

## DATA FRESHNESS STATUS
----------------------------------------
⚠️ NIFTY: Stale (14 days behind)
   - Rows: 14,953,923
   - Latest: 2025-05-26
❌ BANKNIFTY: Outdated (157 days behind)
   - Rows: 1,354,604
   - Latest: 2025-01-03
❌ MIDCAPNIFTY: Outdated (157 days behind)
   - Rows: 26,830
   - Latest: 2025-01-03
❌ SENSEX: Outdated (129 days behind)
   - Rows: 324,451
   - Latest: 2025-01-31
❌ FINNIFTY: No Data (-1 days behind)
❌ BANKEX: No Data (-1 days behind)

## PERFORMANCE SUMMARY (Last 24 Hours)
----------------------------------------

## PERFORMANCE TRENDS (Last 7 Days)
----------------------------------------

## CURRENT SYSTEM RESOURCES
----------------------------------------
CPU Usage: 3.2%
Memory Usage: 15,527 MB
GPU Utilization: 0.0%
GPU Memory: 4,532 MB

================================================================================

## ALERTS
----------------------------------------
🟡 BANKNIFTY data is 157 days old
🟡 MIDCAPNIFTY data is 157 days old
🟡 SENSEX data is 129 days old

Report saved to: /srv/samba/shared/logs/etl_performance_report_20250609_180003.txt
Exception ignored in: <function Connection.__del__ at 0x7f9de9147250>
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 353, in __del__
    self.close()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 369, in close
    self._client.disconnect(self._session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1102, in disconnect
    self.send_disconnect(session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1106, in send_disconnect
    self._oprot.writeMessageBegin('disconnect', TMessageType.CALL, self._seqid)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 55, in writeMessageBegin
    self.writeI32(TBinaryProtocol.VERSION_1 | type)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 120, in writeI32
    self.trans.write(buff)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 173, in write
    raise e
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 169, in write
    self.__wbuf.write(buf)
ValueError: I/O operation on closed file.
2025-06-10 00:00:01,796 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 00:00:02,026 - __main__ - INFO - Connected to HeavyDB
2025-06-10 00:00:02,032 - __main__ - INFO - Metrics table ready
/srv/samba/shared/etl_performance_monitor.py:175: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_monitor.py:312: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-10 00:00:02,204 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
================================================================================
ETL PERFORMANCE REPORT - 2025-06-10 00:00:02
================================================================================

## DATA FRESHNESS STATUS
----------------------------------------
⚠️ NIFTY: Stale (15 days behind)
   - Rows: 14,953,923
   - Latest: 2025-05-26
❌ BANKNIFTY: Outdated (158 days behind)
   - Rows: 1,354,604
   - Latest: 2025-01-03
❌ MIDCAPNIFTY: Outdated (158 days behind)
   - Rows: 26,830
   - Latest: 2025-01-03
❌ SENSEX: Outdated (130 days behind)
   - Rows: 324,451
   - Latest: 2025-01-31
❌ FINNIFTY: No Data (-1 days behind)
❌ BANKEX: No Data (-1 days behind)

## PERFORMANCE SUMMARY (Last 24 Hours)
----------------------------------------

## PERFORMANCE TRENDS (Last 7 Days)
----------------------------------------

## CURRENT SYSTEM RESOURCES
----------------------------------------
CPU Usage: 3.3%
Memory Usage: 15,033 MB
GPU Utilization: 0.0%
GPU Memory: 4,532 MB

================================================================================

## ALERTS
----------------------------------------
🟡 BANKNIFTY data is 158 days old
🟡 MIDCAPNIFTY data is 158 days old
🟡 SENSEX data is 130 days old

Report saved to: /srv/samba/shared/logs/etl_performance_report_20250610_000003.txt
Exception ignored in: <function Connection.__del__ at 0x7fe03d8cb250>
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 353, in __del__
    self.close()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 369, in close
    self._client.disconnect(self._session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1102, in disconnect
    self.send_disconnect(session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1106, in send_disconnect
    self._oprot.writeMessageBegin('disconnect', TMessageType.CALL, self._seqid)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 55, in writeMessageBegin
    self.writeI32(TBinaryProtocol.VERSION_1 | type)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 120, in writeI32
    self.trans.write(buff)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 173, in write
    raise e
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 169, in write
    self.__wbuf.write(buf)
ValueError: I/O operation on closed file.
2025-06-10 06:00:01,964 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 06:00:02,191 - __main__ - INFO - Connected to HeavyDB
2025-06-10 06:00:02,195 - __main__ - INFO - Metrics table ready
/srv/samba/shared/etl_performance_monitor.py:175: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_monitor.py:312: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-10 06:00:02,356 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
================================================================================
ETL PERFORMANCE REPORT - 2025-06-10 06:00:02
================================================================================

## DATA FRESHNESS STATUS
----------------------------------------
⚠️ NIFTY: Stale (15 days behind)
   - Rows: 14,953,923
   - Latest: 2025-05-26
❌ BANKNIFTY: Outdated (158 days behind)
   - Rows: 1,354,604
   - Latest: 2025-01-03
❌ MIDCAPNIFTY: Outdated (158 days behind)
   - Rows: 26,830
   - Latest: 2025-01-03
❌ SENSEX: Outdated (130 days behind)
   - Rows: 324,451
   - Latest: 2025-01-31
❌ FINNIFTY: No Data (-1 days behind)
❌ BANKEX: No Data (-1 days behind)

## PERFORMANCE SUMMARY (Last 24 Hours)
----------------------------------------

## PERFORMANCE TRENDS (Last 7 Days)
----------------------------------------

## CURRENT SYSTEM RESOURCES
----------------------------------------
CPU Usage: 3.3%
Memory Usage: 15,034 MB
GPU Utilization: 0.0%
GPU Memory: 4,532 MB

================================================================================

## ALERTS
----------------------------------------
🟡 BANKNIFTY data is 158 days old
🟡 MIDCAPNIFTY data is 158 days old
🟡 SENSEX data is 130 days old

Report saved to: /srv/samba/shared/logs/etl_performance_report_20250610_060003.txt
Exception ignored in: <function Connection.__del__ at 0x7f95e2bb7250>
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 353, in __del__
    self.close()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 369, in close
    self._client.disconnect(self._session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1102, in disconnect
    self.send_disconnect(session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1106, in send_disconnect
    self._oprot.writeMessageBegin('disconnect', TMessageType.CALL, self._seqid)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 55, in writeMessageBegin
    self.writeI32(TBinaryProtocol.VERSION_1 | type)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 120, in writeI32
    self.trans.write(buff)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 173, in write
    raise e
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 169, in write
    self.__wbuf.write(buf)
ValueError: I/O operation on closed file.
2025-06-10 12:00:01,841 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 12:00:02,075 - __main__ - INFO - Connected to HeavyDB
2025-06-10 12:00:02,079 - __main__ - INFO - Metrics table ready
/srv/samba/shared/etl_performance_monitor.py:175: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_monitor.py:312: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-10 12:00:02,227 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
================================================================================
ETL PERFORMANCE REPORT - 2025-06-10 12:00:02
================================================================================

## DATA FRESHNESS STATUS
----------------------------------------
⚠️ NIFTY: Stale (15 days behind)
   - Rows: 14,953,923
   - Latest: 2025-05-26
❌ BANKNIFTY: Outdated (158 days behind)
   - Rows: 1,354,604
   - Latest: 2025-01-03
❌ MIDCAPNIFTY: Outdated (158 days behind)
   - Rows: 26,830
   - Latest: 2025-01-03
❌ SENSEX: Outdated (130 days behind)
   - Rows: 324,451
   - Latest: 2025-01-31
❌ FINNIFTY: No Data (-1 days behind)
❌ BANKEX: No Data (-1 days behind)

## PERFORMANCE SUMMARY (Last 24 Hours)
----------------------------------------

## PERFORMANCE TRENDS (Last 7 Days)
----------------------------------------

## CURRENT SYSTEM RESOURCES
----------------------------------------
CPU Usage: 3.5%
Memory Usage: 16,070 MB
GPU Utilization: 0.0%
GPU Memory: 4,538 MB

================================================================================

## ALERTS
----------------------------------------
🟡 BANKNIFTY data is 158 days old
🟡 MIDCAPNIFTY data is 158 days old
🟡 SENSEX data is 130 days old

Report saved to: /srv/samba/shared/logs/etl_performance_report_20250610_120003.txt
Exception ignored in: <function Connection.__del__ at 0x7f8ea86ef250>
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 353, in __del__
    self.close()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 369, in close
    self._client.disconnect(self._session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1102, in disconnect
    self.send_disconnect(session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1106, in send_disconnect
    self._oprot.writeMessageBegin('disconnect', TMessageType.CALL, self._seqid)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 55, in writeMessageBegin
    self.writeI32(TBinaryProtocol.VERSION_1 | type)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 120, in writeI32
    self.trans.write(buff)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 173, in write
    raise e
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 169, in write
    self.__wbuf.write(buf)
ValueError: I/O operation on closed file.
2025-06-10 18:00:02,571 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-10 18:00:02,792 - __main__ - INFO - Connected to HeavyDB
2025-06-10 18:00:02,798 - __main__ - INFO - Metrics table ready
/srv/samba/shared/etl_performance_monitor.py:175: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_monitor.py:312: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-10 18:00:02,946 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
================================================================================
ETL PERFORMANCE REPORT - 2025-06-10 18:00:02
================================================================================

## DATA FRESHNESS STATUS
----------------------------------------
⚠️ NIFTY: Stale (15 days behind)
   - Rows: 14,953,923
   - Latest: 2025-05-26
❌ BANKNIFTY: Outdated (158 days behind)
   - Rows: 1,354,604
   - Latest: 2025-01-03
❌ MIDCAPNIFTY: Outdated (158 days behind)
   - Rows: 26,830
   - Latest: 2025-01-03
❌ SENSEX: Outdated (130 days behind)
   - Rows: 324,451
   - Latest: 2025-01-31
❌ FINNIFTY: No Data (-1 days behind)
❌ BANKEX: No Data (-1 days behind)

## PERFORMANCE SUMMARY (Last 24 Hours)
----------------------------------------

## PERFORMANCE TRENDS (Last 7 Days)
----------------------------------------

## CURRENT SYSTEM RESOURCES
----------------------------------------
CPU Usage: 3.3%
Memory Usage: 15,375 MB
GPU Utilization: 0.0%
GPU Memory: 4,538 MB

================================================================================

## ALERTS
----------------------------------------
🟡 BANKNIFTY data is 158 days old
🟡 MIDCAPNIFTY data is 158 days old
🟡 SENSEX data is 130 days old

Report saved to: /srv/samba/shared/logs/etl_performance_report_20250610_180004.txt
Exception ignored in: <function Connection.__del__ at 0x7ffafaacb250>
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 353, in __del__
    self.close()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 369, in close
    self._client.disconnect(self._session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1102, in disconnect
    self.send_disconnect(session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1106, in send_disconnect
    self._oprot.writeMessageBegin('disconnect', TMessageType.CALL, self._seqid)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 55, in writeMessageBegin
    self.writeI32(TBinaryProtocol.VERSION_1 | type)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 120, in writeI32
    self.trans.write(buff)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 173, in write
    raise e
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 169, in write
    self.__wbuf.write(buf)
ValueError: I/O operation on closed file.
2025-06-11 00:00:02,545 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 00:00:02,781 - __main__ - INFO - Connected to HeavyDB
2025-06-11 00:00:02,788 - __main__ - INFO - Metrics table ready
/srv/samba/shared/etl_performance_monitor.py:175: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_monitor.py:312: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-11 00:00:02,947 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
================================================================================
ETL PERFORMANCE REPORT - 2025-06-11 00:00:02
================================================================================

## DATA FRESHNESS STATUS
----------------------------------------
⚠️ NIFTY: Stale (16 days behind)
   - Rows: 14,953,923
   - Latest: 2025-05-26
❌ BANKNIFTY: Outdated (159 days behind)
   - Rows: 1,354,604
   - Latest: 2025-01-03
❌ MIDCAPNIFTY: Outdated (159 days behind)
   - Rows: 26,830
   - Latest: 2025-01-03
❌ SENSEX: Outdated (131 days behind)
   - Rows: 324,451
   - Latest: 2025-01-31
❌ FINNIFTY: No Data (-1 days behind)
❌ BANKEX: No Data (-1 days behind)

## PERFORMANCE SUMMARY (Last 24 Hours)
----------------------------------------

## PERFORMANCE TRENDS (Last 7 Days)
----------------------------------------

## CURRENT SYSTEM RESOURCES
----------------------------------------
CPU Usage: 3.6%
Memory Usage: 16,415 MB
GPU Utilization: 0.0%
GPU Memory: 4,540 MB

================================================================================

## ALERTS
----------------------------------------
🟡 BANKNIFTY data is 159 days old
🟡 MIDCAPNIFTY data is 159 days old
🟡 SENSEX data is 131 days old

Report saved to: /srv/samba/shared/logs/etl_performance_report_20250611_000004.txt
Exception ignored in: <function Connection.__del__ at 0x7f2f692b3250>
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 353, in __del__
    self.close()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 369, in close
    self._client.disconnect(self._session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1102, in disconnect
    self.send_disconnect(session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1106, in send_disconnect
    self._oprot.writeMessageBegin('disconnect', TMessageType.CALL, self._seqid)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 55, in writeMessageBegin
    self.writeI32(TBinaryProtocol.VERSION_1 | type)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 120, in writeI32
    self.trans.write(buff)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 173, in write
    raise e
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 169, in write
    self.__wbuf.write(buf)
ValueError: I/O operation on closed file.
2025-06-11 06:00:01,821 - thrift.transport.TSocket - INFO - Could not connect to ('::1', 6274, 0, 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TSocket.py", line 144, in open
    handle.connect(sockaddr)
ConnectionRefusedError: [Errno 111] Connection refused
2025-06-11 06:00:02,053 - __main__ - INFO - Connected to HeavyDB
2025-06-11 06:00:02,059 - __main__ - INFO - Metrics table ready
/srv/samba/shared/etl_performance_monitor.py:175: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
/srv/samba/shared/etl_performance_monitor.py:312: UserWarning: pandas only supports SQLAlchemy connectable (engine/connection) or database string URI or sqlite3 DBAPI2 connection. Other DBAPI2 objects are not tested. Please consider using SQLAlchemy.
  df = pd.read_sql(query, self.conn)
2025-06-11 06:00:02,201 - __main__ - ERROR - Failed to analyze trends: Execution failed on sql: 
                SELECT 
                    CAST(metric_time AS DATE) as day, EXTRACT(HOUR FROM metric_time) as hour_of_day,
                    AVG(rows_per_second) as avg_speed,
                    AVG(cpu_percent) as avg_cpu,
                    AVG(gpu_utilization) as avg_gpu,
                    SUM(rows_processed) as total_rows,
                    COUNT(*) as operations
                FROM etl_performance_metrics
                WHERE metric_time >= NOW() - INTERVAL '7' DAY
                    AND status = 'success'
                GROUP BY day, hour_of_day
ORDER BY day, hour_of_day
            
SQL Error: Encountered "day" at line 2, column 50.
Was expecting one of:
    <QUOTED_STRING> ...
    <BRACKET_QUOTED_IDENTIFIER> ...
    <QUOTED_IDENTIFIER> ...
    <BACK_QUOTED_IDENTIFIER> ...
    <IDENTIFIER> ...
    <UNICODE_QUOTED_IDENTIFIER> ...
    
unable to rollback
================================================================================
ETL PERFORMANCE REPORT - 2025-06-11 06:00:02
================================================================================

## DATA FRESHNESS STATUS
----------------------------------------
⚠️ NIFTY: Stale (16 days behind)
   - Rows: 14,953,923
   - Latest: 2025-05-26
❌ BANKNIFTY: Outdated (159 days behind)
   - Rows: 1,354,604
   - Latest: 2025-01-03
❌ MIDCAPNIFTY: Outdated (159 days behind)
   - Rows: 26,830
   - Latest: 2025-01-03
❌ SENSEX: Outdated (131 days behind)
   - Rows: 324,451
   - Latest: 2025-01-31
❌ FINNIFTY: No Data (-1 days behind)
❌ BANKEX: No Data (-1 days behind)

## PERFORMANCE SUMMARY (Last 24 Hours)
----------------------------------------

## PERFORMANCE TRENDS (Last 7 Days)
----------------------------------------

## CURRENT SYSTEM RESOURCES
----------------------------------------
CPU Usage: 3.4%
Memory Usage: 16,451 MB
GPU Utilization: 0.0%
GPU Memory: 4,540 MB

================================================================================

## ALERTS
----------------------------------------
🟡 BANKNIFTY data is 159 days old
🟡 MIDCAPNIFTY data is 159 days old
🟡 SENSEX data is 131 days old

Report saved to: /srv/samba/shared/logs/etl_performance_report_20250611_060003.txt
Exception ignored in: <function Connection.__del__ at 0x7f149378b250>
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 353, in __del__
    self.close()
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/connection.py", line 369, in close
    self._client.disconnect(self._session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1102, in disconnect
    self.send_disconnect(session)
  File "/home/<USER>/.local/lib/python3.10/site-packages/heavydb/thrift/Heavy.py", line 1106, in send_disconnect
    self._oprot.writeMessageBegin('disconnect', TMessageType.CALL, self._seqid)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 55, in writeMessageBegin
    self.writeI32(TBinaryProtocol.VERSION_1 | type)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/protocol/TBinaryProtocol.py", line 120, in writeI32
    self.trans.write(buff)
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 173, in write
    raise e
  File "/home/<USER>/.local/lib/python3.10/site-packages/thrift/transport/TTransport.py", line 169, in write
    self.__wbuf.write(buf)
ValueError: I/O operation on closed file.
