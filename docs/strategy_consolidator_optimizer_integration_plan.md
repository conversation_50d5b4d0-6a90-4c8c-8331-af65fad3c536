# Data-Driven Strategy Consolidator and Optimizer Integration Plan
## Pure Historical Performance-Based System with Market Regime Integration

### Table of Contents
1. [Executive Summary](#executive-summary)
2. [Core Principles](#core-principles)
3. [Architecture Overview](#architecture-overview)
4. [Phase 1: Historical Data-Driven Consolidator](#phase-1-historical-data-driven-consolidator)
5. [Phase 2: Data-Based Configuration System](#phase-2-data-based-configuration-system)
6. [Phase 3: Historical Performance Optimizer](#phase-3-historical-performance-optimizer)
7. [Phase 4: Statistical Metrics Engine](#phase-4-statistical-metrics-engine)
8. [Phase 5: Backtester Integration](#phase-5-backtester-integration)
9. [Phase 6: API and Dashboard](#phase-6-api-and-dashboard)
10. [Phase 7: Testing and Deployment](#phase-7-testing-and-deployment)

## Executive Summary

The Data-Driven Strategy Consolidator and Optimizer system will:
- Process thousands of strategies without any pre-assumptions about their performance
- Base ALL decisions purely on historical data points and statistical analysis
- Let historical performance data drive strategy selection and optimization
- Remove all subjective criteria and pre-defined thresholds
- Generate strategy combinations based solely on historical correlation and performance data
- Provide complete transparency in decision-making through data lineage

## Core Principles

### 1. No Pre-Judgmental Assumptions
- NO hardcoded performance thresholds (e.g., "Sharpe > 1.5")
- NO pre-defined "good" or "bad" strategy classifications
- NO assumptions about which strategies work in which conditions
- Let the data speak for itself

### 2. Pure Historical Data Analysis
- Every decision based on actual historical trade data
- Statistical significance testing for all conclusions
- Data sample size requirements before making any judgment
- Rolling window analysis to capture changing market dynamics

### 3. Transparent Decision Process
- Full audit trail of why each strategy was selected/rejected
- Statistical confidence levels for all recommendations
- Data lineage tracking from raw trades to final decisions
- Reproducible results with same historical data

## Architecture Overview

### System Components

```
┌─────────────────────────────────────────────────────────────────────┐
│                      Strategy Consolidator & Optimizer               │
├─────────────────────────────────────────────────────────────────────┤
│                                                                      │
│  ┌──────────────┐    ┌──────────────┐    ┌──────────────┐         │
│  │   Strategy   │    │   Market     │    │  Optimizer   │         │
│  │ Consolidator │ -> │   Regime     │ -> │   Engine     │         │
│  └──────────────┘    └──────────────┘    └──────────────┘         │
│         ↑                    ↑                    ↑                 │
│  ┌──────────────┐    ┌──────────────┐    ┌──────────────┐         │
│  │Excel Config  │    │ Performance  │    │  Rule Gen    │         │
│  │   Parser     │    │   Metrics    │    │   Engine     │         │
│  └──────────────┘    └──────────────┘    └──────────────┘         │
│                                                                      │
└─────────────────────────────────────────────────────────────────────┘
```

### Directory Structure

```
backtester_v2/
├── consolidator/
│   ├── __init__.py
│   ├── base.py                    # Base consolidator class
│   ├── parsers/
│   │   ├── tbs_parser.py         # TBS strategy parser
│   │   ├── tv_parser.py          # TradingView parser
│   │   ├── orb_parser.py         # ORB strategy parser
│   │   ├── oi_parser.py          # OI strategy parser
│   │   ├── ml_parser.py          # ML Indicator parser
│   │   └── pos_parser.py         # POS strategy parser
│   ├── consolidation/
│   │   ├── strategy_merger.py    # Merge strategies
│   │   ├── regime_integrator.py  # Integrate with market regime
│   │   └── performance_calc.py   # Calculate performance metrics
│   └── config/
│       ├── excel_parser.py       # Parse Excel configuration
│       └── validators.py         # Validate configurations
├── optimizer/
│   ├── __init__.py
│   ├── base.py                   # Base optimizer class
│   ├── engines/
│   │   ├── genetic.py           # Genetic algorithm optimizer
│   │   ├── bayesian.py          # Bayesian optimizer
│   │   ├── grid_search.py       # Grid search optimizer
│   │   └── ensemble.py          # Ensemble optimizer
│   ├── dimensions/
│   │   ├── market_regime.py     # Market regime dimension
│   │   ├── time_zone.py         # Time zone dimension
│   │   ├── dte.py               # DTE dimension
│   │   ├── day_of_week.py       # Day of week dimension
│   │   └── custom.py            # Custom dimensions
│   ├── metrics/
│   │   ├── sharpe.py            # Sharpe ratio
│   │   ├── sortino.py           # Sortino ratio
│   │   ├── calmar.py            # Calmar ratio
│   │   ├── max_drawdown.py      # Maximum drawdown
│   │   └── custom_metrics.py    # User-defined metrics
│   └── rules/
│       ├── rule_generator.py    # Generate trading rules
│       └── rule_validator.py    # Validate rules
└── integration/
    ├── backtester_adapter.py    # Adapt to existing backtester
    ├── api_endpoints.py         # API endpoints
    └── dashboard/               # Dashboard components
```

## Phase 1: Historical Data-Driven Consolidator

### Task 1.1: Pure Data-Driven Consolidator Base

```python
# backtester_v2/consolidator/base.py
from abc import ABC, abstractmethod
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Union, Tuple
from pydantic import BaseModel, Field
from scipy import stats

class StrategyDataPoint(BaseModel):
    """Raw historical data point from strategy"""
    timestamp: datetime
    strategy_id: str
    pnl: float
    position_size: Optional[float] = None
    entry_price: Optional[float] = None
    exit_price: Optional[float] = None
    market_conditions: Optional[Dict[str, Any]] = None
    
class DataDrivenConfig(BaseModel):
    """Configuration based on data analysis requirements"""
    min_data_points: int = 100  # Minimum trades for statistical significance
    confidence_level: float = 0.95  # Statistical confidence required
    lookback_windows: List[int] = [30, 60, 90, 180, 252]  # Days
    correlation_method: str = "pearson"  # pearson, spearman, kendall
    outlier_detection: str = "iqr"  # iqr, zscore, isolation_forest
    regime_detection: str = "hmm"  # hmm, clustering, breakpoint
    
class ConsolidationConfig(BaseModel):
    """Master consolidation configuration"""
    max_strategies: int = 10000
    batch_size: int = 100
    parallel_workers: int = 8
    memory_limit_gb: int = 32
    gpu_acceleration: bool = True
    
class DataDrivenConsolidator(ABC):
    """Base class for pure data-driven strategy consolidation"""
    
    def __init__(self, config: DataDrivenConfig):
        self.config = config
        self.raw_data = []
        self.statistical_summary = {}
        self.data_quality_report = {}
        
    def analyze_strategy_data(self, strategy_data: pd.DataFrame) -> Dict[str, Any]:
        """Analyze strategy purely from historical data"""
        analysis = {
            'data_points': len(strategy_data),
            'date_range': (strategy_data['timestamp'].min(), strategy_data['timestamp'].max()),
            'statistical_properties': self._calculate_statistical_properties(strategy_data),
            'regime_behavior': self._analyze_regime_behavior(strategy_data),
            'correlation_matrix': self._calculate_correlations(strategy_data),
            'confidence_intervals': self._calculate_confidence_intervals(strategy_data)
        }
        
        # No pre-judgment - just facts from data
        return analysis
        
    def _calculate_statistical_properties(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Calculate all statistical properties from historical data"""
        returns = data['pnl'].pct_change().dropna()
        
        return {
            'mean': float(returns.mean()),
            'std': float(returns.std()),
            'skew': float(stats.skew(returns)),
            'kurtosis': float(stats.kurtosis(returns)),
            'var_95': float(np.percentile(returns, 5)),
            'cvar_95': float(returns[returns <= np.percentile(returns, 5)].mean()),
            'autocorrelation': self._calculate_autocorrelation(returns),
            'stationarity': self._test_stationarity(returns),
            'distribution_test': self._test_distribution(returns)
        }
        
    def discover_patterns(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Discover patterns from data without assumptions"""
        patterns = {
            'seasonal_patterns': self._detect_seasonality(data),
            'regime_patterns': self._detect_regime_patterns(data),
            'correlation_patterns': self._detect_correlation_patterns(data),
            'anomaly_patterns': self._detect_anomalies(data)
        }
        return patterns
```

### Task 1.2: Strategy-Specific Parsers

```python
# backtester_v2/consolidator/parsers/tbs_parser.py
class TBSParser(BaseParser):
    """Parser for TBS (Trade Builder Strategy) files"""
    
    def parse(self, file_path: str, config: StrategyConfig) -> pd.DataFrame:
        """Parse TBS strategy file"""
        # Read multi-leg options strategy data
        df = pd.read_excel(file_path, sheet_name='LegParameter')
        
        # Extract relevant columns
        strategy_data = self._extract_strategy_metrics(df)
        
        # Assign zones based on time
        strategy_data['zone'] = self._assign_zones(
            strategy_data['timestamp'],
            config.zone_method,
            config.num_zones
        )
        
        # Calculate PnL metrics
        strategy_data = self._calculate_pnl_metrics(strategy_data)
        
        return strategy_data
```

### Task 1.3: High-Performance Consolidation Engine

```python
# backtester_v2/consolidator/consolidation/strategy_merger.py
import dask.dataframe as dd
from heavydb import connect

class StrategyMerger:
    """High-performance strategy merger using GPU acceleration"""
    
    def __init__(self, heavydb_conn):
        self.conn = heavydb_conn
        
    def merge_strategies_gpu(self, strategies: List[pd.DataFrame]) -> pd.DataFrame:
        """Merge strategies using GPU acceleration"""
        
        # Convert to GPU-friendly format
        for i, strategy in enumerate(strategies):
            # Load to HeavyDB for GPU processing
            strategy.to_sql(f'temp_strategy_{i}', self.conn, if_exists='replace')
        
        # GPU-accelerated merge query
        merge_query = """
        SELECT /*+ gpu_enable */
            s.strategy_name,
            s.timestamp,
            s.zone,
            s.pnl,
            s.cumulative_pnl,
            s.drawdown,
            s.trade_count,
            r.market_regime,
            r.regime_strength,
            r.volatility_regime
        FROM (
            SELECT * FROM temp_strategy_0
            UNION ALL
            SELECT * FROM temp_strategy_1
            -- ... dynamically add all strategies
        ) s
        LEFT JOIN market_regime_data r
            ON DATE_TRUNC('minute', s.timestamp) = DATE_TRUNC('minute', r.timestamp)
        ORDER BY s.strategy_name, s.timestamp
        """
        
        return pd.read_sql(merge_query, self.conn)
```

## Phase 2: Data-Based Configuration System

### Task 2.1: Data-Driven Configuration System

```
DATA_DRIVEN_CONSOLIDATOR_CONFIG.xlsx

Sheet 1: Data Analysis Settings
| Parameter                | Value       | Description                              |
|-------------------------|-------------|------------------------------------------|
| MIN_DATA_POINTS         | 100         | Minimum trades for analysis              |
| CONFIDENCE_LEVEL        | 0.95        | Statistical confidence required          |
| OUTLIER_METHOD          | IQR         | Method for outlier detection             |
| CORRELATION_METHOD      | PEARSON     | Correlation calculation method           |
| LOOKBACK_WINDOWS        | 30,60,90,252| Analysis windows in days                 |
| BOOTSTRAP_SAMPLES       | 10000       | Samples for confidence intervals         |

Sheet 2: Data Source Mapping (No Strategy Type Assumptions)
| File_Pattern    | Timestamp_Columns     | Value_Columns        | Optional_Columns         |
|----------------|-----------------------|---------------------|--------------------------|
| *.xlsx         | AUTO_DETECT           | AUTO_DETECT_NUMERIC | ALL_REMAINING            |
| *.csv          | AUTO_DETECT_DATETIME  | AUTO_DETECT_NUMERIC | ALL_REMAINING            |
| *.json         | AUTO_DETECT_TIMESTAMP | AUTO_DETECT_VALUE   | ALL_REMAINING            |

Sheet 3: Statistical Analysis Configuration
| Analysis_Type          | Method              | Parameters          | Output_Type         |
|-----------------------|---------------------|---------------------|---------------------|
| Distribution_Testing  | SHAPIRO,KS,ANDERSON | alpha=0.05          | p_value,statistic   |
| Stationarity_Testing  | ADF,KPSS,PP         | lags=auto           | p_value,critical    |
| Correlation_Analysis  | PEARSON,SPEARMAN    | rolling=TRUE        | matrix,significance |
| Regime_Detection      | HMM,BREAKPOINT      | n_regimes=auto      | states,transitions  |
| Pattern_Detection     | FFT,WAVELETS        | frequencies=auto    | patterns,strength   |

Sheet 4: Data Quality Requirements
| Quality_Check         | Threshold    | Action_If_Failed    | Description                |
|----------------------|--------------|---------------------|----------------------------|
| Completeness         | 95%          | FLAG_WARNING        | Non-null data percentage   |
| Consistency          | 99%          | FLAG_ERROR          | Data type consistency      |
| Timeliness           | 1_DAY        | FLAG_INFO           | Data freshness             |
| Uniqueness           | 100%         | DEDUPLICATE         | No duplicate timestamps    |
| Validity             | STATISTICAL  | FLAG_OUTLIER        | Statistical outlier check  |

Sheet 5: Historical Analysis Dimensions (Data-Driven Discovery)
| Dimension           | Discovery_Method      | Min_Samples | Significance_Test    |
|--------------------|----------------------|-------------|---------------------|
| Time_Patterns      | AUTOCORRELATION      | 100         | LJUNG_BOX           |
| Market_Conditions  | CLUSTERING           | 50          | SILHOUETTE          |
| Volatility_Regimes | GARCH                | 252         | ARCH_LM             |
| Correlation_Groups | HIERARCHICAL         | 30          | COPHENETIC          |
| Performance_Cycles | SPECTRAL             | 500         | PERIODOGRAM         |

Sheet 6: Data-Driven Combination Discovery
| Discovery_Method    | Parameters           | Statistical_Test      | Output               |
|--------------------|---------------------|----------------------|---------------------|
| Correlation_Matrix | rolling=60_days     | significance=0.05    | correlation_pairs   |
| Cointegration      | max_lag=10          | johansen,engle       | cointegrated_groups |
| Principal_Components| variance_explained=0.95| kaiser_criterion   | component_loadings  |
| Clustering         | method=hierarchical | silhouette_score     | natural_groups      |
| Network_Analysis   | threshold=0.3       | community_detection  | strategy_networks   |

Sheet 7: Output Configuration (Data-Driven Reports)
| Report_Type              | Content                        | Statistical_Evidence | Confidence_Levels |
|-------------------------|-------------------------------|---------------------|-------------------|
| Performance_Discovery   | Historical patterns found      | p_values, effect_sizes | 95% CI included  |
| Strategy_Clustering     | Natural strategy groups        | silhouette_scores   | bootstrap_validated|
| Correlation_Analysis    | Strategy relationships         | correlation_matrix  | significance_tested|
| Regime_Performance      | Performance by market state    | anova_results       | bonferroni_corrected|
| Optimal_Combinations    | Data-driven portfolios         | historical_backtest | out_of_sample_tested|
```

### Task 2.2: Excel Configuration Parser

```python
# backtester_v2/consolidator/config/excel_parser.py
import pandas as pd
from typing import Dict, Any
import openpyxl

class ExcelConfigParser:
    """Parse Excel configuration for consolidator and optimizer"""
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.workbook = openpyxl.load_workbook(config_path, data_only=True)
        
    def parse_all_sheets(self) -> Dict[str, Any]:
        """Parse all configuration sheets"""
        config = {
            'general': self._parse_general_settings(),
            'strategy_types': self._parse_strategy_types(),
            'optimization': self._parse_optimization_params(),
            'metrics': self._parse_performance_metrics(),
            'combination_rules': self._parse_combination_rules(),
            'output': self._parse_output_config()
        }
        return config
        
    def _parse_general_settings(self) -> Dict[str, Any]:
        """Parse general settings sheet"""
        sheet = self.workbook['General Settings']
        settings = {}
        
        for row in sheet.iter_rows(min_row=2, values_only=True):
            if row[0]:  # Parameter name exists
                param_name = row[0].lower().replace(' ', '_')
                settings[param_name] = self._convert_value(row[1])
                
        return settings
```

## Phase 3: Historical Performance Optimizer

### Task 3.1: Pure Historical Performance Optimizer

```python
# backtester_v2/optimizer/base.py
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
import pandas as pd
from scipy import stats
from sklearn.model_selection import TimeSeriesSplit
import warnings

class HistoricalPerformanceOptimizer:
    """Optimizer based purely on historical performance data"""
    
    def __init__(self, min_data_points: int = 100):
        self.min_data_points = min_data_points
        self.optimization_history = []
        
    def discover_optimal_conditions(self, historical_data: pd.DataFrame) -> Dict[str, Any]:
        """Discover optimal conditions from historical data without assumptions"""
        
        discoveries = {
            'data_summary': self._summarize_data(historical_data),
            'natural_clusters': self._discover_natural_clusters(historical_data),
            'performance_patterns': self._discover_performance_patterns(historical_data),
            'correlation_structures': self._discover_correlations(historical_data),
            'regime_analysis': self._analyze_historical_regimes(historical_data),
            'statistical_confidence': self._calculate_confidence_levels(historical_data)
        }
        
        return discoveries
        
    def _discover_natural_clusters(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Let data reveal its natural groupings"""
        from sklearn.cluster import DBSCAN, KMeans
        from sklearn.preprocessing import StandardScaler
        
        # Prepare features from historical data
        features = self._extract_features(data)
        scaled_features = StandardScaler().fit_transform(features)
        
        # Try multiple clustering approaches
        clustering_results = {}
        
        # DBSCAN - finds clusters of arbitrary shape
        dbscan = DBSCAN(eps=0.5, min_samples=5)
        dbscan_labels = dbscan.fit_predict(scaled_features)
        
        # Optimal K for KMeans using elbow method
        inertias = []
        K_range = range(2, min(15, len(data) // 50))
        for k in K_range:
            kmeans = KMeans(n_clusters=k, random_state=42)
            kmeans.fit(scaled_features)
            inertias.append(kmeans.inertia_)
        
        # Find elbow point
        optimal_k = self._find_elbow_point(list(K_range), inertias)
        
        # Apply KMeans with optimal K
        kmeans = KMeans(n_clusters=optimal_k, random_state=42)
        kmeans_labels = kmeans.fit_predict(scaled_features)
        
        return {
            'natural_groups': len(set(dbscan_labels)) - (1 if -1 in dbscan_labels else 0),
            'optimal_k': optimal_k,
            'cluster_sizes': pd.Series(kmeans_labels).value_counts().to_dict(),
            'cluster_performance': self._analyze_cluster_performance(data, kmeans_labels)
        }
        
    def _discover_performance_patterns(self, data: pd.DataFrame) -> Dict[str, Any]:
        """Discover patterns in historical performance"""
        patterns = {}
        
        # Time-based patterns
        data['hour'] = data['timestamp'].dt.hour
        data['day_of_week'] = data['timestamp'].dt.dayofweek
        data['month'] = data['timestamp'].dt.month
        
        # Calculate performance by time dimensions
        for time_dim in ['hour', 'day_of_week', 'month']:
            dim_performance = data.groupby(time_dim)['pnl'].agg([
                'count', 'mean', 'std', 'sum'
            ])
            
            # Only include if statistically significant
            if len(dim_performance) > 1:
                f_stat, p_value = stats.f_oneway(*[
                    group['pnl'].values 
                    for name, group in data.groupby(time_dim)
                    if len(group) >= 10
                ])
                
                if p_value < 0.05:
                    patterns[f'{time_dim}_effect'] = {
                        'significant': True,
                        'p_value': p_value,
                        'best_periods': dim_performance.nlargest(3, 'mean').index.tolist(),
                        'effect_size': self._calculate_effect_size(data, time_dim)
                    }
                    
        return patterns
```

### Task 3.2: Data-Driven Dimension Discovery

```python
# backtester_v2/optimizer/dimensions/data_driven_dimensions.py
class DataDrivenDimensionDiscovery:
    """Discover optimization dimensions from historical data"""
    
    def discover_dimensions(self, historical_data: pd.DataFrame) -> Dict[str, Any]:
        """Let data reveal its own dimensions for optimization"""
        
        discovered_dimensions = {}
        
        # Analyze all available data columns
        for column in historical_data.columns:
            if column not in ['timestamp', 'pnl', 'strategy_id']:
                dimension_analysis = self._analyze_dimension(
                    historical_data, 
                    column
                )
                
                # Only include if dimension shows statistical significance
                if dimension_analysis['is_significant']:
                    discovered_dimensions[column] = dimension_analysis
                    
        # Discover interaction effects between dimensions
        interaction_effects = self._discover_interactions(
            historical_data,
            discovered_dimensions.keys()
        )
        
        # Discover hidden dimensions using PCA/Factor Analysis
        hidden_dimensions = self._discover_hidden_dimensions(historical_data)
        
        return {
            'primary_dimensions': discovered_dimensions,
            'interaction_effects': interaction_effects,
            'hidden_dimensions': hidden_dimensions,
            'dimension_importance': self._rank_dimensions(discovered_dimensions)
        }
        
    def _analyze_dimension(self, data: pd.DataFrame, dimension: str) -> Dict[str, Any]:
        """Analyze if a dimension has significant impact on performance"""
        
        # Check if dimension is categorical or continuous
        if data[dimension].dtype in ['object', 'category'] or data[dimension].nunique() < 20:
            # Categorical analysis
            groups = data.groupby(dimension)['pnl'].apply(list)
            
            # ANOVA test for significance
            if len(groups) > 1:
                f_stat, p_value = stats.f_oneway(*[
                    group for group in groups if len(group) >= 10
                ])
                
                effect_size = self._calculate_eta_squared(data, dimension, 'pnl')
                
                return {
                    'dimension_type': 'categorical',
                    'is_significant': p_value < 0.05,
                    'p_value': p_value,
                    'effect_size': effect_size,
                    'n_categories': len(groups),
                    'performance_by_category': data.groupby(dimension)['pnl'].agg([
                        'count', 'mean', 'std', 'sum'
                    ]).to_dict()
                }
        else:
            # Continuous analysis
            correlation = data[dimension].corr(data['pnl'])
            
            # Test correlation significance
            n = len(data)
            t_stat = correlation * np.sqrt(n - 2) / np.sqrt(1 - correlation**2)
            p_value = stats.t.sf(np.abs(t_stat), n - 2) * 2
            
            # Non-linear relationship test
            from sklearn.ensemble import RandomForestRegressor
            rf = RandomForestRegressor(n_estimators=100, random_state=42)
            rf.fit(data[[dimension]], data['pnl'])
            feature_importance = rf.feature_importances_[0]
            
            return {
                'dimension_type': 'continuous',
                'is_significant': p_value < 0.05 or feature_importance > 0.1,
                'linear_correlation': correlation,
                'p_value': p_value,
                'feature_importance': feature_importance,
                'optimal_ranges': self._find_optimal_ranges(data, dimension)
            }
```

### Task 3.3: Advanced Optimization Algorithms

```python
# backtester_v2/optimizer/engines/ensemble.py
class EnsembleOptimizer:
    """Ensemble optimization using multiple algorithms"""
    
    def __init__(self, config: Dict[str, Any]):
        self.optimizers = [
            GeneticOptimizer(config),
            BayesianOptimizer(config),
            GridSearchOptimizer(config)
        ]
        
    def optimize(self, objective_function, bounds, constraints=None):
        """Run ensemble optimization"""
        
        results = []
        
        # Run each optimizer
        for optimizer in self.optimizers:
            result = optimizer.optimize(
                objective_function,
                bounds,
                constraints
            )
            results.append(result)
            
        # Combine results using voting or averaging
        ensemble_result = self._combine_results(results)
        
        # Validate ensemble result
        if self._validate_result(ensemble_result):
            return ensemble_result
        else:
            # Fallback to best individual result
            return max(results, key=lambda x: x['score'])
```

## Phase 4: Statistical Metrics Engine

### Task 4.1: Pure Statistical Metrics Engine

```python
# backtester_v2/optimizer/metrics/statistical_engine.py
import numpy as np
from scipy import stats
from statsmodels.stats.diagnostic import acorr_ljungbox
from statsmodels.tsa.stattools import adfuller
import pandas as pd

class StatisticalMetricsEngine:
    """Calculate metrics based purely on statistical analysis of historical data"""
    
    def analyze_performance_distribution(self, historical_returns: np.ndarray) -> Dict[str, Any]:
        """Analyze the statistical distribution of historical performance"""
        
        # Basic statistical moments
        moments = {
            'mean': np.mean(historical_returns),
            'variance': np.var(historical_returns),
            'skewness': stats.skew(historical_returns),
            'kurtosis': stats.kurtosis(historical_returns),
            'sample_size': len(historical_returns)
        }
        
        # Distribution testing
        distribution_tests = {
            'normal': stats.normaltest(historical_returns),
            'shapiro': stats.shapiro(historical_returns),
            'jarque_bera': stats.jarque_bera(historical_returns)
        }
        
        # Percentile analysis
        percentiles = np.percentile(historical_returns, [1, 5, 10, 25, 50, 75, 90, 95, 99])
        
        # Tail analysis
        tail_analysis = {
            'left_tail_weight': np.sum(historical_returns <= np.percentile(historical_returns, 5)) / len(historical_returns),
            'right_tail_weight': np.sum(historical_returns >= np.percentile(historical_returns, 95)) / len(historical_returns),
            'tail_ratio': self._calculate_tail_ratio(historical_returns)
        }
        
        # Time series properties
        time_series_properties = {
            'autocorrelation': self._analyze_autocorrelation(historical_returns),
            'stationarity': self._test_stationarity(historical_returns),
            'volatility_clustering': self._test_volatility_clustering(historical_returns)
        }
        
        # Risk metrics from data
        risk_metrics = {
            'historical_var': {p: np.percentile(historical_returns, p) for p in [1, 5, 10]},
            'historical_cvar': {p: np.mean(historical_returns[historical_returns <= np.percentile(historical_returns, p)]) for p in [1, 5, 10]},
            'maximum_loss': np.min(historical_returns),
            'maximum_gain': np.max(historical_returns),
            'loss_frequency': np.sum(historical_returns < 0) / len(historical_returns)
        }
        
        # Stability analysis
        stability_analysis = self._analyze_stability(historical_returns)
        
        return {
            'statistical_moments': moments,
            'distribution_tests': {k: {'statistic': v[0], 'p_value': v[1]} for k, v in distribution_tests.items()},
            'percentiles': dict(zip([1, 5, 10, 25, 50, 75, 90, 95, 99], percentiles)),
            'tail_analysis': tail_analysis,
            'time_series_properties': time_series_properties,
            'risk_metrics': risk_metrics,
            'stability_analysis': stability_analysis,
            'confidence_intervals': self._calculate_confidence_intervals(historical_returns)
        }
        
    def _analyze_stability(self, returns: np.ndarray) -> Dict[str, Any]:
        """Analyze performance stability over time"""
        
        # Rolling statistics
        window_size = min(60, len(returns) // 4)
        if window_size < 10:
            return {'insufficient_data': True}
            
        rolling_mean = pd.Series(returns).rolling(window_size).mean()
        rolling_std = pd.Series(returns).rolling(window_size).std()
        
        # Regime changes
        from sklearn.mixture import GaussianMixture
        
        # Reshape for GMM
        X = returns.reshape(-1, 1)
        
        # Find optimal number of regimes using BIC
        n_components_range = range(1, min(5, len(returns) // 50))
        bic_scores = []
        
        for n in n_components_range:
            gmm = GaussianMixture(n_components=n, random_state=42)
            gmm.fit(X)
            bic_scores.append(gmm.bic(X))
            
        optimal_regimes = n_components_range[np.argmin(bic_scores)]
        
        # Fit with optimal components
        gmm = GaussianMixture(n_components=optimal_regimes, random_state=42)
        gmm.fit(X)
        regime_labels = gmm.predict(X)
        
        return {
            'rolling_stats_variability': {
                'mean_cv': rolling_mean.std() / rolling_mean.mean() if rolling_mean.mean() != 0 else np.inf,
                'std_cv': rolling_std.std() / rolling_std.mean() if rolling_std.mean() != 0 else np.inf
            },
            'regime_analysis': {
                'n_regimes': optimal_regimes,
                'regime_probabilities': gmm.weights_.tolist(),
                'regime_means': gmm.means_.flatten().tolist(),
                'regime_stds': np.sqrt(gmm.covariances_.flatten()).tolist(),
                'regime_transitions': self._analyze_regime_transitions(regime_labels)
            }
        }
```

### Task 4.2: Real-time Performance Tracking

```python
# backtester_v2/optimizer/metrics/realtime_tracker.py
class RealtimePerformanceTracker:
    """Track performance metrics in real-time"""
    
    def __init__(self, window_size: int = 252):
        self.window_size = window_size
        self.metrics_buffer = deque(maxlen=window_size)
        
    async def update_metrics(self, new_return: float):
        """Update metrics with new return data"""
        
        self.metrics_buffer.append(new_return)
        
        if len(self.metrics_buffer) >= self.min_samples:
            current_metrics = self.calculate_current_metrics()
            
            # Check for significant changes
            if self.detect_regime_change(current_metrics):
                await self.trigger_reoptimization()
```

## Phase 5: Backtester Integration

### Task 5.1: Adapter for Existing Backtester

```python
# backtester_v2/integration/backtester_adapter.py
from BTRUN.services.strategy_executor import StrategyExecutor

class ConsolidatorBacktesterAdapter:
    """Adapter to integrate consolidator with existing backtester"""
    
    def __init__(self, strategy_executor: StrategyExecutor):
        self.executor = strategy_executor
        self.consolidator = EnhancedConsolidator()
        self.optimizer = StrategyOptimizer()
        
    async def run_consolidated_backtest(self, config: Dict[str, Any]):
        """Run backtest with consolidation and optimization"""
        
        # Step 1: Run individual backtests
        backtest_results = []
        
        for strategy_config in config['strategies']:
            result = await self.executor.execute_strategy(
                strategy_config['type'],
                strategy_config['params']
            )
            backtest_results.append(result)
            
        # Step 2: Consolidate results
        consolidated = self.consolidator.consolidate_results(
            backtest_results,
            config['consolidation_params']
        )
        
        # Step 3: Add market regime data
        with_regime = self.consolidator.add_market_regime(
            consolidated,
            config['market_regime_file']
        )
        
        # Step 4: Optimize
        optimization_results = self.optimizer.optimize(
            with_regime,
            config['optimization_params']
        )
        
        return {
            'consolidated_data': consolidated,
            'optimization_results': optimization_results,
            'trading_rules': optimization_results['trading_rules']
        }
```

### Task 5.2: Golden Format Output Generator

```python
# backtester_v2/consolidator/output/golden_format.py
class GoldenFormatGenerator:
    """Generate output in golden format compatible with existing system"""
    
    def generate_golden_output(self, 
                             consolidated_data: pd.DataFrame,
                             optimization_results: Dict[str, Any]) -> pd.DataFrame:
        """Generate golden format output"""
        
        # Create base structure
        golden_df = pd.DataFrame({
            'Date': consolidated_data['timestamp'].dt.date,
            'Time': consolidated_data['timestamp'].dt.time,
            'Strategy': consolidated_data['strategy_name'],
            'Symbol': consolidated_data['symbol'],
            'Entry_Price': consolidated_data['entry_price'],
            'Exit_Price': consolidated_data['exit_price'],
            'PNL': consolidated_data['pnl'],
            'Cumulative_PNL': consolidated_data['cumulative_pnl'],
            'Market_Regime': consolidated_data['market_regime'],
            'Zone': consolidated_data['zone'],
            'Optimization_Score': consolidated_data['optimization_score']
        })
        
        # Add optimization metadata
        for key, value in optimization_results['optimal_conditions'].items():
            golden_df[f'Optimal_{key}'] = value
            
        return golden_df
```

## Phase 6: API and Dashboard

### Task 6.1: RESTful API Endpoints

```python
# backtester_v2/integration/api_endpoints.py
from fastapi import APIRouter, UploadFile, File
from typing import List

router = APIRouter(prefix="/api/v2/consolidator")

@router.post("/upload/config")
async def upload_consolidator_config(file: UploadFile = File(...)):
    """Upload Excel configuration for consolidator"""
    # Validate Excel file
    config = ExcelConfigParser(file).parse_all_sheets()
    
    # Store configuration
    await store_configuration(config)
    
    return {"status": "success", "config_id": config_id}

@router.post("/consolidate")
async def consolidate_strategies(
    strategy_files: List[UploadFile],
    config_id: str,
    market_regime_file: Optional[UploadFile] = None
):
    """Consolidate multiple strategy files"""
    # Load configuration
    config = await load_configuration(config_id)
    
    # Run consolidation
    consolidator = EnhancedConsolidator(config)
    results = await consolidator.consolidate_async(strategy_files)
    
    return {
        "status": "success",
        "consolidated_file": results['file_path'],
        "summary": results['summary']
    }

@router.post("/optimize")
async def optimize_strategies(
    consolidated_file_id: str,
    optimization_params: Dict[str, Any]
):
    """Optimize consolidated strategies"""
    # Load consolidated data
    data = await load_consolidated_data(consolidated_file_id)
    
    # Run optimization
    optimizer = StrategyOptimizer(optimization_params)
    results = await optimizer.optimize_async(data)
    
    return {
        "status": "success",
        "optimization_results": results,
        "trading_rules": results['trading_rules']
    }
```

### Task 6.2: Real-time Dashboard

```python
# backtester_v2/integration/dashboard/consolidator_dashboard.py
import streamlit as st
import plotly.graph_objects as go

class ConsolidatorDashboard:
    """Dashboard for strategy consolidation and optimization"""
    
    def render(self):
        st.title("Strategy Consolidator & Optimizer")
        
        # Sidebar configuration
        with st.sidebar:
            st.header("Configuration")
            
            # Upload configuration
            config_file = st.file_uploader(
                "Upload Configuration Excel",
                type=['xlsx']
            )
            
            # Select dimensions
            st.subheader("Optimization Dimensions")
            enable_market_regime = st.checkbox("Market Regime", value=True)
            enable_time_zone = st.checkbox("Time Zone", value=True)
            enable_dte = st.checkbox("DTE", value=True)
            enable_dow = st.checkbox("Day of Week", value=False)
            
        # Main content
        tab1, tab2, tab3, tab4 = st.tabs([
            "Upload Strategies",
            "Consolidation Results",
            "Optimization Results",
            "Trading Rules"
        ])
        
        with tab1:
            self._render_upload_section()
            
        with tab2:
            self._render_consolidation_results()
            
        with tab3:
            self._render_optimization_results()
            
        with tab4:
            self._render_trading_rules()
```

## Phase 7: Testing and Deployment

### Task 7.1: Comprehensive Test Suite

```python
# tests/test_consolidator_optimizer.py
import pytest
from backtester_v2.consolidator import EnhancedConsolidator
from backtester_v2.optimizer import StrategyOptimizer

class TestConsolidatorOptimizer:
    """Test suite for consolidator and optimizer"""
    
    @pytest.fixture
    def sample_strategies(self):
        """Generate sample strategy data"""
        return [
            generate_tbs_strategy_data(),
            generate_tv_strategy_data(),
            generate_orb_strategy_data()
        ]
        
    def test_consolidation_performance(self, sample_strategies):
        """Test consolidation can handle 10000+ strategies"""
        # Generate 10000 strategies
        large_dataset = sample_strategies * 3334
        
        consolidator = EnhancedConsolidator()
        start_time = time.time()
        
        result = consolidator.consolidate(large_dataset)
        
        elapsed_time = time.time() - start_time
        
        # Should complete within 5 minutes for 10000 strategies
        assert elapsed_time < 300
        assert len(result) == 10000
        
    def test_optimization_accuracy(self, consolidated_data):
        """Test optimization produces valid results"""
        optimizer = StrategyOptimizer()
        
        results = optimizer.optimize(consolidated_data)
        
        # Verify all dimensions optimized
        assert 'market_regime' in results['optimal_conditions']
        assert 'time_zone' in results['optimal_conditions']
        
        # Verify performance improvement
        baseline_sharpe = calculate_sharpe(consolidated_data)
        optimized_sharpe = results['performance_metrics']['sharpe_ratio']
        
        assert optimized_sharpe > baseline_sharpe
```

### Task 7.2: Deployment Configuration

```yaml
# docker-compose.consolidator.yml
version: '3.8'

services:
  consolidator-api:
    build:
      context: .
      dockerfile: Dockerfile.consolidator
    ports:
      - "8001:8001"
    environment:
      - HEAVYDB_HOST=heavydb
      - REDIS_HOST=redis
      - MAX_WORKERS=16
      - GPU_ENABLED=true
    volumes:
      - ./data:/app/data
      - ./configs:/app/configs
    deploy:
      resources:
        limits:
          memory: 64G
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
              
  consolidator-worker:
    build:
      context: .
      dockerfile: Dockerfile.consolidator
    command: celery -A backtester_v2.consolidator worker
    environment:
      - CELERY_BROKER=redis://redis:6379
    deploy:
      replicas: 4
      
  optimizer-service:
    build:
      context: .
      dockerfile: Dockerfile.optimizer
    environment:
      - OPTIMIZATION_ENGINE=ensemble
      - MAX_ITERATIONS=10000
    deploy:
      resources:
        limits:
          memory: 32G
```

## Implementation Timeline

### Week 1-2: Foundation
- Refactor existing consolidator code
- Create base classes and interfaces
- Setup directory structure

### Week 3-4: Parser Development
- Implement all strategy parsers
- Create Excel configuration parser
- Add validation layer

### Week 5-6: Optimization Engine
- Implement dimension-based optimization
- Add optimization algorithms
- Create metrics calculators

### Week 7-8: Integration
- Integrate with backtester
- Add market regime integration
- Create output generators

### Week 9-10: API and Dashboard
- Develop API endpoints
- Create dashboard components
- Add real-time monitoring

### Week 11-12: Testing and Deployment
- Complete test suite
- Performance optimization
- Production deployment

## Key Features Summary

1. **Pure Data-Driven Approach**
   - NO pre-judgmental assumptions about strategies
   - ALL decisions based on historical data analysis
   - Statistical significance required for all conclusions
   - Complete transparency in decision process

2. **Automatic Discovery**
   - System discovers optimal dimensions from data
   - Natural strategy groupings emerge from analysis
   - Correlation structures revealed by historical performance
   - Hidden patterns uncovered through statistical methods

3. **Statistical Rigor**
   - Confidence intervals for all metrics
   - Hypothesis testing for significance
   - Multiple testing correction applied
   - Bootstrap validation for robustness

4. **Adaptive Learning**
   - System learns from new data continuously
   - No fixed thresholds or rules
   - Performance patterns evolve with market
   - Regime detection adapts to structural changes

5. **Complete Auditability**
   - Full data lineage tracking
   - Statistical evidence for every decision
   - Reproducible results with same data
   - Confidence levels for all recommendations

6. **Unbiased Strategy Selection**
   - Strategies selected purely on historical merit
   - No favoritism for any strategy type
   - Natural diversification emerges from data
   - Correlation-based groupings, not labels

## Data-Driven Philosophy

This system represents a paradigm shift from rule-based to data-driven strategy optimization:

1. **Let Data Speak**: Instead of imposing our beliefs about what makes a good strategy, we let historical performance data reveal the truth.

2. **Statistical Evidence**: Every recommendation backed by statistical tests and confidence levels.

3. **No Sacred Cows**: All strategies treated equally - performance history is the only judge.

4. **Continuous Learning**: System adapts as new data arrives, never stuck with outdated assumptions.

5. **Transparent Process**: Complete visibility into why each decision was made, backed by data.