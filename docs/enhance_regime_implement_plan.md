# Enhanced Market Regime System Implementation Plan
## With Backtester V2 Integration and Code Refactoring

### Table of Contents
1. [Executive Summary](#executive-summary)
2. [Architecture Overview](#architecture-overview)
3. [Phase 1: Foundation and Refactoring](#phase-1-foundation-and-refactoring-week-1-2)
4. [Phase 2: Database Schema Enhancement](#phase-2-database-schema-enhancement-week-2-3)
5. [Phase 3: Market Regime Core Module](#phase-3-market-regime-core-module-week-3-4)
6. [Phase 4: Indicator Integration](#phase-4-indicator-integration-week-4-6)
7. [Phase 5: Strategy Enhancement](#phase-5-strategy-enhancement-week-6-7)
8. [Phase 6: Performance Tracking](#phase-6-performance-tracking-week-7-8)
9. [Phase 7: GPU Optimization](#phase-7-gpu-optimization-week-8-9)
10. [Phase 8: Integration Testing](#phase-8-integration-testing-week-9-10)
11. [Phase 9: API and UI](#phase-9-api-and-ui-week-10-11)
12. [Phase 10: Deployment](#phase-10-deployment-week-11-12)

---

## Executive Summary

This plan outlines the integration of the Enhanced Market Regime System from `/srv/samba/shared/enhanced-market-regime-optimizer-final-package-updated` into the existing backtester architecture at `/srv/samba/shared/bt/backtester_stable/BTRUN/backtester_v2`. The integration will follow the established architectural patterns while adding comprehensive market regime analysis capabilities.

### Key Objectives:
1. Refactor existing market regime code to fit backtester_v2 architecture
2. Create a new `market_regime` module following existing patterns
3. Enhance all strategies with regime-aware capabilities
4. Implement performance-based adaptive weighting
5. Add comprehensive configuration system via Excel templates
6. Ensure GPU optimization throughout

---

## Architecture Overview

### Proposed Directory Structure:
```
backtester_v2/
├── core/
│   ├── base_strategy.py          # [MODIFY] Add regime awareness
│   ├── market_regime/            # [NEW] Core regime functionality
│   │   ├── __init__.py
│   │   ├── base_regime.py        # Base regime classes
│   │   ├── regime_calculator.py  # Main regime calculation engine
│   │   ├── regime_classifier.py  # Classification logic
│   │   └── regime_models.py      # Pydantic models
│   └── utils/
│       ├── regime_utils.py       # [NEW] Regime utility functions
│       └── weight_manager.py     # [NEW] Adaptive weight management
├── indicators/                   # [NEW] Centralized indicators
│   ├── __init__.py
│   ├── base_indicator.py         # Base indicator class
│   ├── ema_enhanced.py           # Enhanced EMA with multi-instrument
│   ├── vwap_enhanced.py          # Enhanced VWAP with prev day
│   ├── oi_flow.py              # OI and flow analysis
│   ├── greek_sentiment.py       # Greek-based sentiment
│   ├── iv_analysis.py          # IV skew and term structure
│   ├── premium_analysis.py      # [NEW] Straddle premium analysis
│   ├── triple_straddle.py      # [NEW] Triple straddle analysis
│   └── regime_indicators.py     # Regime-specific indicators
├── market_regime/               # [NEW] Market regime strategy module
│   ├── __init__.py
│   ├── strategy.py              # Regime detection strategy
│   ├── parser.py                # Excel config parser
│   ├── processor.py             # Regime result processor
│   ├── query_builder.py         # GPU-optimized queries
│   ├── models.py                # Configuration models
│   ├── constants.py             # Regime constants
│   └── performance/             # Performance tracking
│       ├── tracker.py           # Performance tracking
│       ├── optimizer.py         # Weight optimization
│       └── models.py            # Performance models
├── strategies/
│   ├── [existing strategies]
│   └── regime_enhanced/         # [NEW] Regime-enhanced versions
│       ├── tbs_regime.py
│       ├── tv_regime.py
│       └── orb_regime.py
└── integration/
    └── regime_api.py            # [NEW] Regime-specific APIs
```

---

## Phase 1: Foundation and Refactoring (Week 1-2)

### Task 1.1: Code Analysis and Mapping
**Objective**: Map existing regime code to new architecture

#### Sub-tasks:
1.1.1. **Analyze existing regime codebase**
```bash
# Location: /srv/samba/shared/enhanced-market-regime-optimizer-final-package-updated
# Document all modules and their functionality
# Create mapping document: old_path -> new_path
```

1.1.2. **Create refactoring plan**
```python
# Mapping example:
REFACTOR_MAP = {
    'utils/feature_engineering/ema_indicators/': 'backtester_v2/indicators/ema_enhanced.py',
    'utils/feature_engineering/vwap_indicators/': 'backtester_v2/indicators/vwap_enhanced.py',
    'core/market_regime_generator.py': 'backtester_v2/core/market_regime/regime_calculator.py',
    'Strategy_consolidator_new.py': 'backtester_v2/integration/consolidator_v2.py'
}
```

1.1.3. **Setup development environment**
```bash
# Create feature branch
git checkout -b feature/market-regime-integration

# Create directory structure
mkdir -p backtester_v2/core/market_regime
mkdir -p backtester_v2/indicators
mkdir -p backtester_v2/market_regime
mkdir -p backtester_v2/strategies/regime_enhanced
```

### Task 1.2: Base Infrastructure Setup
**Objective**: Create foundational classes and interfaces

#### Sub-tasks:
1.2.1. **Create base indicator class**
```python
# File: backtester_v2/indicators/base_indicator.py
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import pandas as pd
import numpy as np

class BaseIndicator(ABC):
    """Base class for all indicators following backtester_v2 patterns"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.weight = config.get('weight', 1.0)
        self.performance_multiplier = 1.0
        self.adaptive = config.get('adaptive', True)
        
    @abstractmethod
    def calculate(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """Calculate indicator values"""
        pass
        
    @abstractmethod
    def get_signal(self, values: pd.DataFrame) -> float:
        """Convert indicator values to regime signal (-1 to 1)"""
        pass
        
    def update_weight(self, performance_score: float):
        """Update indicator weight based on performance"""
        if self.adaptive:
            learning_rate = self.config.get('learning_rate', 0.01)
            self.performance_multiplier *= (1 + learning_rate * performance_score)
            self.performance_multiplier = np.clip(
                self.performance_multiplier, 0.5, 2.0
            )
```

1.2.2. **Create base regime class**
```python
# File: backtester_v2/core/market_regime/base_regime.py
from dataclasses import dataclass
from enum import Enum
from typing import Dict, List, Optional
import pandas as pd

class RegimeType(Enum):
    STRONG_BULLISH = "STRONG_BULLISH"
    MODERATE_BULLISH = "MODERATE_BULLISH"
    WEAK_BULLISH = "WEAK_BULLISH"
    NEUTRAL = "NEUTRAL"
    WEAK_BEARISH = "WEAK_BEARISH"
    MODERATE_BEARISH = "MODERATE_BEARISH"
    STRONG_BEARISH = "STRONG_BEARISH"

@dataclass
class RegimeClassification:
    timestamp: pd.Timestamp
    symbol: str
    regime_type: RegimeType
    regime_score: float
    confidence: float
    component_scores: Dict[str, float]
    timeframe_scores: Dict[int, float]
```

1.2.3. **Modify base strategy for regime awareness**
```python
# File: backtester_v2/core/base_strategy.py (MODIFY)
# Add to existing BaseStrategy class:

def set_regime_filter(self, regime_config: Optional[Dict] = None):
    """Enable regime-based filtering for the strategy"""
    self.regime_enabled = regime_config is not None
    self.regime_config = regime_config or {}
    
def apply_regime_adjustment(self, signals: pd.DataFrame, 
                           regime_data: pd.DataFrame) -> pd.DataFrame:
    """Apply regime-based adjustments to strategy signals"""
    if not self.regime_enabled:
        return signals
        
    # Merge regime data
    signals = signals.merge(
        regime_data[['timestamp', 'regime_type', 'confidence']],
        on='timestamp',
        how='left'
    )
    
    # Apply regime-specific logic
    return self._adjust_signals_by_regime(signals)
```

---

## Phase 2: Database Schema Enhancement (Week 2-3)

### Task 2.1: Design Enhanced Schema
**Objective**: Create GPU-optimized schema for regime data

#### Sub-tasks:
2.1.1. **Create schema definition file**
```sql
-- File: backtester_v2/market_regime/schema/market_regime_tables.sql

-- Main regime indicators table
CREATE TABLE market_regime_indicators (
    trade_date TIMESTAMP(0),
    symbol TEXT ENCODING DICT(32),
    timeframe INT,
    
    -- Price-based indicators
    ema_5 FLOAT,
    ema_10 FLOAT,
    ema_20 FLOAT,
    ema_50 FLOAT,
    ema_100 FLOAT,
    ema_200 FLOAT,
    
    -- VWAP indicators
    vwap FLOAT,
    prev_day_vwap FLOAT,
    vwap_upper_1 FLOAT,
    vwap_lower_1 FLOAT,
    vwap_upper_2 FLOAT,
    vwap_lower_2 FLOAT,
    
    -- Premium indicators
    atm_strike FLOAT,
    atm_straddle_premium FLOAT,
    atm_ce_premium FLOAT,
    atm_pe_premium FLOAT,
    itm1_straddle_premium FLOAT,
    otm1_straddle_premium FLOAT,
    triple_straddle_premium FLOAT,
    
    -- Derived signals
    ema_signal FLOAT,
    vwap_signal FLOAT,
    premium_signal FLOAT,
    oi_signal FLOAT,
    greek_signal FLOAT,
    
    PRIMARY KEY (trade_date, symbol, timeframe)
) WITH (
    fragment_size = 32000000,
    sort_column = 'trade_date'
);

-- Regime classifications
CREATE TABLE regime_classifications (
    trade_date TIMESTAMP(0),
    symbol TEXT ENCODING DICT(32),
    regime_id INT,
    regime_name TEXT ENCODING DICT(32),
    regime_score FLOAT,
    confidence FLOAT,
    trend_score FLOAT,
    momentum_score FLOAT,
    volatility_score FLOAT,
    flow_score FLOAT,
    premium_score FLOAT,
    
    PRIMARY KEY (trade_date, symbol)
) WITH (
    fragment_size = 32000000,
    sort_column = 'trade_date'
);

-- Performance tracking
CREATE TABLE regime_performance_metrics (
    eval_date DATE,
    indicator_id TEXT ENCODING DICT(32),
    timeframe INT,
    hit_rate FLOAT,
    sharpe_ratio FLOAT,
    information_ratio FLOAT,
    current_weight FLOAT,
    performance_score FLOAT,
    
    PRIMARY KEY (eval_date, indicator_id, timeframe)
) WITH (fragment_size = 32000000);
```

2.1.2. **Create migration script**
```python
# File: backtester_v2/market_regime/schema/migrate.py
import heavydb
from pathlib import Path

class RegimeSchemaMigrator:
    def __init__(self, conn):
        self.conn = conn
        
    def create_tables(self):
        """Create all regime-related tables"""
        schema_path = Path(__file__).parent / 'market_regime_tables.sql'
        
        with open(schema_path, 'r') as f:
            sql = f.read()
            
        # Execute each CREATE TABLE statement
        for statement in sql.split(';'):
            if statement.strip():
                self.conn.execute(statement)
                
    def create_indexes(self):
        """Create performance indexes"""
        indexes = [
            "CREATE INDEX idx_regime_symbol_date ON regime_classifications(symbol, trade_date)",
            "CREATE INDEX idx_indicators_symbol_tf ON market_regime_indicators(symbol, timeframe, trade_date)"
        ]
        
        for idx in indexes:
            try:
                self.conn.execute(idx)
            except Exception as e:
                print(f"Index may already exist: {e}")
```

### Task 2.2: Implement Data Access Layer
**Objective**: Create efficient data access patterns

#### Sub-tasks:
2.2.1. **Create regime database manager**
```python
# File: backtester_v2/core/market_regime/regime_db_manager.py
from typing import Dict, List, Optional, Tuple
import pandas as pd
import heavydb

class RegimeDBManager:
    def __init__(self, conn: heavydb.Connection):
        self.conn = conn
        
    def bulk_insert_indicators(self, df: pd.DataFrame, table: str = 'market_regime_indicators'):
        """Bulk insert indicators using GPU-optimized COPY FROM"""
        # Implementation following existing patterns in backtester_v2
        pass
        
    def get_regime_data(self, symbol: str, start_date: str, end_date: str) -> pd.DataFrame:
        """Retrieve regime classifications for a symbol and date range"""
        query = f"""
        SELECT /*+ gpu_enable */
            trade_date,
            regime_name,
            regime_score,
            confidence,
            trend_score,
            momentum_score,
            volatility_score,
            flow_score,
            premium_score
        FROM regime_classifications
        WHERE symbol = '{symbol}'
            AND trade_date BETWEEN '{start_date}' AND '{end_date}'
        ORDER BY trade_date
        """
        return pd.read_sql(query, self.conn)
```

---

## Phase 3: Market Regime Core Module (Week 3-4)

### Task 3.1: Implement Regime Calculator
**Objective**: Port and enhance regime calculation engine

#### Sub-tasks:
3.1.1. **Create regime calculator**
```python
# File: backtester_v2/core/market_regime/regime_calculator.py
from typing import Dict, List, Any
import pandas as pd
import numpy as np
from ..base import BaseComponent
from ...indicators import get_indicator

class RegimeCalculator(BaseComponent):
    """Main regime calculation engine following backtester_v2 patterns"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.indicators = self._initialize_indicators()
        self.classifier = RegimeClassifier(config.get('classification', {}))
        
    def _initialize_indicators(self) -> Dict[str, BaseIndicator]:
        """Initialize all configured indicators"""
        indicators = {}
        
        for ind_config in self.config.get('indicators', []):
            ind_type = ind_config['type']
            indicator = get_indicator(ind_type, ind_config)
            indicators[ind_config['id']] = indicator
            
        return indicators
        
    def calculate_regime(self, market_data: pd.DataFrame) -> pd.DataFrame:
        """Calculate market regime for given data"""
        # Calculate all indicators
        indicator_results = {}
        
        for ind_id, indicator in self.indicators.items():
            indicator_results[ind_id] = indicator.calculate(market_data)
            
        # Aggregate signals
        regime_scores = self._aggregate_signals(indicator_results)
        
        # Classify regimes
        classifications = self.classifier.classify(regime_scores)
        
        return classifications
```

3.1.2. **Create regime classifier**
```python
# File: backtester_v2/core/market_regime/regime_classifier.py
from typing import Dict, List, Tuple
import pandas as pd
import numpy as np
from .base_regime import RegimeType, RegimeClassification

class RegimeClassifier:
    """Classify market regimes based on aggregated scores"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.thresholds = self._load_thresholds()
        
    def classify(self, scores: pd.DataFrame) -> List[RegimeClassification]:
        """Classify regimes based on scores"""
        classifications = []
        
        for idx, row in scores.iterrows():
            regime_type = self._determine_regime(row['aggregate_score'])
            confidence = self._calculate_confidence(row)
            
            classification = RegimeClassification(
                timestamp=row['timestamp'],
                symbol=row['symbol'],
                regime_type=regime_type,
                regime_score=row['aggregate_score'],
                confidence=confidence,
                component_scores=row['component_scores'],
                timeframe_scores=row['timeframe_scores']
            )
            
            classifications.append(classification)
            
        return classifications
```

### Task 3.2: Implement Configuration System
**Objective**: Create Excel-based configuration parser

#### Sub-tasks:
3.2.1. **Create configuration models**
```python
# File: backtester_v2/market_regime/models.py
from pydantic import BaseModel, Field, validator
from typing import Dict, List, Optional
from enum import Enum

class IndicatorConfig(BaseModel):
    """Configuration for a single indicator"""
    id: str
    name: str
    type: str
    category: str
    base_weight: float = Field(ge=0.0, le=1.0)
    min_weight: float = Field(ge=0.0, le=1.0)
    max_weight: float = Field(ge=0.0, le=1.0)
    enabled: bool = True
    adaptive: bool = True
    parameters: Dict[str, Any] = {}
    
    @validator('max_weight')
    def validate_weight_range(cls, v, values):
        if v < values.get('min_weight', 0):
            raise ValueError('max_weight must be >= min_weight')
        return v

class RegimeConfig(BaseModel):
    """Complete regime configuration"""
    indicators: List[IndicatorConfig]
    timeframes: List[int] = [1, 5, 15, 30, 60]
    lookback_days: int = 252
    update_frequency: str = "DAILY"
    performance_window: int = 100
    learning_rate: float = 0.01
```

3.2.2. **Create Excel parser**
```python
# File: backtester_v2/market_regime/parser.py
import pandas as pd
from typing import Dict, Any
from .models import RegimeConfig, IndicatorConfig

class RegimeConfigParser:
    """Parse regime configuration from Excel following backtester patterns"""
    
    def parse(self, file_path: str) -> RegimeConfig:
        """Parse Excel configuration file"""
        # Read all sheets
        sheets = pd.read_excel(file_path, sheet_name=None)
        
        # Parse indicator registry
        indicators = self._parse_indicators(sheets['Indicator_Registry'])
        
        # Parse other configuration sheets
        config_dict = {
            'indicators': indicators,
            'timeframes': self._parse_timeframes(sheets['Timeframe_Configuration']),
            'performance_config': self._parse_performance(sheets['Performance_Tracking'])
        }
        
        return RegimeConfig(**config_dict)
        
    def _parse_indicators(self, df: pd.DataFrame) -> List[Dict]:
        """Parse indicator configuration"""
        indicators = []
        
        for _, row in df.iterrows():
            if row['Enabled']:
                ind_config = {
                    'id': row['Indicator_ID'],
                    'name': row['Indicator_Name'],
                    'type': row['Category'],
                    'base_weight': row['Base_Weight'],
                    'min_weight': row['Min_Weight'],
                    'max_weight': row['Max_Weight'],
                    'adaptive': row.get('Adaptive', True)
                }
                indicators.append(ind_config)
                
        return indicators
```

---

## Phase 4: Indicator Integration (Week 4-6)

### Task 4.1: Refactor and Enhance Indicators
**Objective**: Port indicators to new architecture with enhancements

#### Sub-tasks:
4.1.1. **Enhanced EMA indicator**
```python
# File: backtester_v2/indicators/ema_enhanced.py
from .base_indicator import BaseIndicator
import pandas as pd
import numpy as np
from typing import Dict, List

class EnhancedEMAIndicator(BaseIndicator):
    """Enhanced EMA with multi-instrument and multi-period support"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.periods = config.get('periods', [5, 10, 20, 50, 100, 200])
        self.instruments = config.get('instruments', ['underlying', 'atm_straddle'])
        
    def calculate(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """Calculate EMA for all configured periods and instruments"""
        results = pd.DataFrame(index=data.index)
        
        for instrument in self.instruments:
            if instrument == 'underlying':
                price_col = 'close'
            elif instrument == 'atm_straddle':
                price_col = 'atm_straddle_premium'
            else:
                continue
                
            if price_col in data.columns:
                for period in self.periods:
                    col_name = f'ema_{instrument}_{period}'
                    results[col_name] = data[price_col].ewm(span=period).mean()
                    
        # Calculate signals
        results['ema_signal'] = self._calculate_ema_signals(results)
        
        return results
        
    def _calculate_ema_signals(self, ema_df: pd.DataFrame) -> pd.Series:
        """Convert EMA values to regime signals"""
        signal = pd.Series(0.0, index=ema_df.index)
        
        # Implement crossover logic
        for instrument in self.instruments:
            if f'ema_{instrument}_20' in ema_df.columns:
                # Bullish: fast > slow
                fast = ema_df[f'ema_{instrument}_20']
                slow = ema_df[f'ema_{instrument}_50']
                
                instrument_signal = np.where(fast > slow, 1, -1)
                weight = self.config.get(f'{instrument}_weight', 0.25)
                signal += instrument_signal * weight
                
        return np.clip(signal, -1, 1)
```

4.1.2. **Premium analysis indicator**
```python
# File: backtester_v2/indicators/premium_analysis.py
from .base_indicator import BaseIndicator
import pandas as pd
import numpy as np

class PremiumAnalysisIndicator(BaseIndicator):
    """Analyze option premiums for regime detection"""
    
    def calculate(self, data: pd.DataFrame, **kwargs) -> pd.DataFrame:
        """Calculate premium-based indicators"""
        results = pd.DataFrame(index=data.index)
        
        # ATM straddle analysis
        if 'atm_ce_premium' in data.columns and 'atm_pe_premium' in data.columns:
            results['atm_straddle'] = data['atm_ce_premium'] + data['atm_pe_premium']
            results['premium_skew'] = (data['atm_ce_premium'] - data['atm_pe_premium']) / results['atm_straddle']
            
        # ITM/OTM analysis
        if 'itm1_ce_premium' in data.columns:
            results['itm1_straddle'] = data['itm1_ce_premium'] + data['itm1_pe_premium']
            results['otm1_straddle'] = data['otm1_ce_premium'] + data['otm1_pe_premium']
            
        # Triple straddle
        if 'atm_straddle' in results.columns:
            results['triple_straddle'] = (
                results['atm_straddle'] + 
                results.get('itm1_straddle', 0) + 
                results.get('otm1_straddle', 0)
            )
            
        # Technical indicators on premiums
        results['atm_straddle_ema20'] = results['atm_straddle'].ewm(span=20).mean()
        results['premium_momentum'] = results['atm_straddle'].pct_change(periods=5)
        
        # Generate signal
        results['premium_signal'] = self._calculate_premium_signal(results)
        
        return results
```

### Task 4.2: Create Indicator Factory
**Objective**: Centralized indicator management

#### Sub-tasks:
4.2.1. **Implement indicator factory**
```python
# File: backtester_v2/indicators/__init__.py
from typing import Dict, Type
from .base_indicator import BaseIndicator
from .ema_enhanced import EnhancedEMAIndicator
from .vwap_enhanced import EnhancedVWAPIndicator
from .premium_analysis import PremiumAnalysisIndicator
from .oi_flow import OIFlowIndicator
from .greek_sentiment import GreekSentimentIndicator

# Registry of available indicators
INDICATOR_REGISTRY: Dict[str, Type[BaseIndicator]] = {
    'ema': EnhancedEMAIndicator,
    'vwap': EnhancedVWAPIndicator,
    'premium': PremiumAnalysisIndicator,
    'oi_flow': OIFlowIndicator,
    'greek_sentiment': GreekSentimentIndicator,
}

def get_indicator(indicator_type: str, config: Dict) -> BaseIndicator:
    """Factory method to create indicators"""
    if indicator_type not in INDICATOR_REGISTRY:
        raise ValueError(f"Unknown indicator type: {indicator_type}")
        
    indicator_class = INDICATOR_REGISTRY[indicator_type]
    return indicator_class(config)

def register_indicator(name: str, indicator_class: Type[BaseIndicator]):
    """Register a new indicator type"""
    INDICATOR_REGISTRY[name] = indicator_class
```

---

## Phase 5: Strategy Enhancement (Week 6-7)

### Task 5.1: Create Market Regime Strategy
**Objective**: Implement regime detection as a strategy

#### Sub-tasks:
5.1.1. **Implement regime strategy**
```python
# File: backtester_v2/market_regime/strategy.py
from ..core.base_strategy import BaseStrategy
from ..core.market_regime import RegimeCalculator
from .models import RegimeConfig
from typing import Dict, Any, List
import pandas as pd

class MarketRegimeStrategy(BaseStrategy):
    """Market regime detection and classification strategy"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.regime_calculator = RegimeCalculator(config)
        self.performance_tracker = PerformanceTracker(config.get('performance', {}))
        
    def execute(self, start_date: str, end_date: str) -> pd.DataFrame:
        """Execute regime detection for date range"""
        # Get market data
        market_data = self._fetch_market_data(start_date, end_date)
        
        # Calculate regimes
        regime_classifications = self.regime_calculator.calculate_regime(market_data)
        
        # Track performance if historical data available
        if self.config.get('track_performance', True):
            self.performance_tracker.update(regime_classifications, market_data)
            
        return regime_classifications
        
    def _fetch_market_data(self, start_date: str, end_date: str) -> pd.DataFrame:
        """Fetch required market data using query builder"""
        query = self.query_builder.build_regime_data_query(
            self.symbol,
            start_date,
            end_date,
            self.config.get('indicators', [])
        )
        
        return pd.read_sql(query, self.db_connection)
```

5.1.2. **Create regime query builder**
```python
# File: backtester_v2/market_regime/query_builder.py
from ..core.query_builder import BaseQueryBuilder
from typing import List, Dict

class RegimeQueryBuilder(BaseQueryBuilder):
    """Build GPU-optimized queries for regime detection"""
    
    def build_regime_data_query(self, symbol: str, start_date: str, 
                               end_date: str, indicators: List[Dict]) -> str:
        """Build query to fetch all data needed for regime calculation"""
        
        # Base query with price data
        query = f"""
        WITH price_data AS (
            SELECT /*+ gpu_enable */
                trade_date,
                symbol,
                close as underlying_price,
                volume,
                high,
                low
            FROM market_data
            WHERE symbol = '{symbol}'
                AND trade_date BETWEEN '{start_date}' AND '{end_date}'
        ),
        option_data AS (
            SELECT /*+ gpu_enable */
                o.trade_date,
                o.strike,
                o.option_type,
                o.close_price as premium,
                o.open_interest,
                o.volume as option_volume,
                -- Calculate moneyness
                CASE 
                    WHEN ABS(o.strike - p.underlying_price) <= 50 THEN 'ATM'
                    WHEN o.strike < p.underlying_price - 50 THEN 'ITM'
                    WHEN o.strike > p.underlying_price + 50 THEN 'OTM'
                END as moneyness
            FROM nifty_option_chain o
            JOIN price_data p ON o.trade_date = p.trade_date
            WHERE o.symbol = '{symbol}'
        ),
        straddle_premiums AS (
            SELECT 
                trade_date,
                moneyness,
                strike,
                SUM(CASE WHEN option_type = 'CE' THEN premium ELSE 0 END) as ce_premium,
                SUM(CASE WHEN option_type = 'PE' THEN premium ELSE 0 END) as pe_premium,
                SUM(premium) as straddle_premium
            FROM option_data
            GROUP BY trade_date, moneyness, strike
        )
        SELECT /*+ gpu_enable */
            p.*,
            -- ATM premiums
            MAX(CASE WHEN s.moneyness = 'ATM' THEN s.ce_premium END) as atm_ce_premium,
            MAX(CASE WHEN s.moneyness = 'ATM' THEN s.pe_premium END) as atm_pe_premium,
            MAX(CASE WHEN s.moneyness = 'ATM' THEN s.straddle_premium END) as atm_straddle_premium,
            -- Add other required fields
        FROM price_data p
        LEFT JOIN straddle_premiums s ON p.trade_date = s.trade_date
        GROUP BY p.trade_date
        ORDER BY p.trade_date
        """
        
        return query
```

### Task 5.2: Enhance Existing Strategies
**Objective**: Add regime awareness to existing strategies

#### Sub-tasks:
5.2.1. **Create regime-enhanced TBS**
```python
# File: backtester_v2/strategies/regime_enhanced/tbs_regime.py
from ..tbs.strategy import TBSStrategy
from ...core.market_regime import RegimeFilter

class TBSRegimeStrategy(TBSStrategy):
    """TBS strategy with market regime filtering"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.regime_filter = RegimeFilter(config.get('regime_filter', {}))
        
    def execute(self, start_date: str, end_date: str) -> pd.DataFrame:
        """Execute TBS with regime filtering"""
        # Get base TBS signals
        base_results = super().execute(start_date, end_date)
        
        # Get regime data
        regime_data = self._get_regime_data(start_date, end_date)
        
        # Apply regime filter
        filtered_results = self.regime_filter.apply(base_results, regime_data)
        
        return filtered_results
        
    def _get_regime_data(self, start_date: str, end_date: str) -> pd.DataFrame:
        """Fetch regime classifications"""
        query = f"""
        SELECT /*+ gpu_enable */
            trade_date,
            regime_name,
            regime_score,
            confidence
        FROM regime_classifications
        WHERE symbol = '{self.symbol}'
            AND trade_date BETWEEN '{start_date}' AND '{end_date}'
        """
        return pd.read_sql(query, self.db_connection)
```

---

## Phase 6: Performance Tracking (Week 7-8)

### Task 6.1: Implement Performance Tracking System
**Objective**: Track and optimize indicator performance

#### Sub-tasks:
6.1.1. **Create performance tracker**
```python
# File: backtester_v2/market_regime/performance/tracker.py
from typing import Dict, List
import pandas as pd
import numpy as np
from ...core.database import get_connection

class PerformanceTracker:
    """Track performance of regime predictions"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.lookback_window = config.get('lookback_window', 100)
        self.metrics = ['hit_rate', 'sharpe_ratio', 'information_ratio']
        
    def update(self, predictions: pd.DataFrame, actuals: pd.DataFrame):
        """Update performance metrics based on predictions vs actuals"""
        # Calculate accuracy
        accuracy = self._calculate_accuracy(predictions, actuals)
        
        # Calculate other metrics
        metrics = {
            'hit_rate': accuracy,
            'sharpe_ratio': self._calculate_sharpe(predictions, actuals),
            'information_ratio': self._calculate_ir(predictions, actuals)
        }
        
        # Store in database
        self._store_metrics(metrics)
        
        # Update indicator weights if needed
        if self.config.get('adaptive_weights', True):
            self._update_weights(metrics)
            
    def _calculate_accuracy(self, predictions: pd.DataFrame, 
                          actuals: pd.DataFrame) -> float:
        """Calculate prediction accuracy"""
        # Implement accuracy calculation
        correct = (predictions['regime_type'] == actuals['actual_regime']).sum()
        total = len(predictions)
        return correct / total if total > 0 else 0.0
```

6.1.2. **Create weight optimizer**
```python
# File: backtester_v2/market_regime/performance/optimizer.py
import numpy as np
from scipy.optimize import minimize
from typing import Dict, List, Tuple

class WeightOptimizer:
    """Optimize indicator weights based on historical performance"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.method = config.get('optimization_method', 'sharpe')
        
    def optimize(self, performance_data: pd.DataFrame, 
                constraints: Dict) -> Dict[str, float]:
        """Optimize weights using specified method"""
        
        if self.method == 'sharpe':
            return self._optimize_sharpe(performance_data, constraints)
        elif self.method == 'genetic':
            return self._genetic_optimization(performance_data, constraints)
        else:
            raise ValueError(f"Unknown optimization method: {self.method}")
            
    def _optimize_sharpe(self, data: pd.DataFrame, 
                        constraints: Dict) -> Dict[str, float]:
        """Optimize for maximum Sharpe ratio"""
        # Get current weights
        indicators = data['indicator_id'].unique()
        n_indicators = len(indicators)
        
        # Objective function (negative Sharpe for minimization)
        def objective(weights):
            # Calculate portfolio performance with given weights
            portfolio_return = np.sum(weights * data['returns'])
            portfolio_std = np.sqrt(np.dot(weights, np.dot(data['covariance'], weights)))
            return -portfolio_return / portfolio_std if portfolio_std > 0 else 0
            
        # Constraints
        cons = [
            {'type': 'eq', 'fun': lambda x: np.sum(x) - 1},  # Weights sum to 1
        ]
        
        # Bounds
        bounds = [(constraints.get('min_weight', 0.05), 
                  constraints.get('max_weight', 0.5)) for _ in range(n_indicators)]
        
        # Initial guess (equal weights)
        x0 = np.ones(n_indicators) / n_indicators
        
        # Optimize
        result = minimize(objective, x0, method='SLSQP', 
                         bounds=bounds, constraints=cons)
        
        # Return optimized weights
        return dict(zip(indicators, result.x))
```

---

## Phase 7: GPU Optimization (Week 8-9)

### Task 7.1: GPU-Accelerated Calculations
**Objective**: Optimize all calculations for GPU execution

#### Sub-tasks:
7.1.1. **Create GPU calculation module**
```python
# File: backtester_v2/core/gpu/regime_gpu.py
import cupy as cp
import cudf
from typing import Dict, Any
import pandas as pd

class RegimeGPUCalculator:
    """GPU-accelerated regime calculations"""
    
    def __init__(self):
        self.gpu_available = self._check_gpu()
        
    def calculate_indicators_gpu(self, data: pd.DataFrame) -> cudf.DataFrame:
        """Calculate all indicators on GPU"""
        # Convert to GPU DataFrame
        gdf = cudf.from_pandas(data)
        
        # EMA calculations on GPU
        gdf['ema_20'] = self._ema_gpu(gdf['close'], 20)
        gdf['ema_50'] = self._ema_gpu(gdf['close'], 50)
        
        # VWAP on GPU
        gdf['vwap'] = self._vwap_gpu(gdf['close'], gdf['volume'])
        
        # Premium calculations
        if 'atm_ce_premium' in gdf.columns:
            gdf['atm_straddle'] = gdf['atm_ce_premium'] + gdf['atm_pe_premium']
            
        return gdf
        
    def _ema_gpu(self, series: cudf.Series, period: int) -> cudf.Series:
        """Calculate EMA on GPU"""
        alpha = 2 / (period + 1)
        
        # Use cupy for calculations
        values = series.values
        ema = cp.zeros_like(values)
        ema[0] = values[0]
        
        for i in range(1, len(values)):
            ema[i] = alpha * values[i] + (1 - alpha) * ema[i-1]
            
        return cudf.Series(ema, index=series.index)
```

7.1.2. **Optimize HeavyDB queries**
```python
# File: backtester_v2/core/gpu/query_optimizer.py
class RegimeQueryOptimizer:
    """Optimize queries for GPU execution in HeavyDB"""
    
    @staticmethod
    def optimize_query(query: str) -> str:
        """Add GPU optimization hints to query"""
        # Ensure GPU execution
        if '/*+ gpu_enable */' not in query:
            query = query.replace('SELECT', 'SELECT /*+ gpu_enable */', 1)
            
        # Optimize window functions for GPU
        query = query.replace('OVER (', 'OVER /*+ gpu_enable */ (')
        
        # Use GPU-friendly aggregations
        optimizations = {
            'COUNT(*)': 'GPU_COUNT(*)',
            'SUM(': 'GPU_SUM(',
            'AVG(': 'GPU_AVG(',
            'STDDEV(': 'GPU_STDDEV('
        }
        
        for old, new in optimizations.items():
            if old in query:
                query = query.replace(old, new)
                
        return query
```

---

## Phase 8: Integration Testing (Week 9-10)

### Task 8.1: Create Comprehensive Tests
**Objective**: Ensure robust integration with existing system

#### Sub-tasks:
8.1.1. **Unit tests for indicators**
```python
# File: backtester_v2/tests/test_indicators.py
import pytest
import pandas as pd
from ..indicators import EnhancedEMAIndicator, PremiumAnalysisIndicator

class TestIndicators:
    def test_ema_indicator(self):
        """Test EMA indicator calculations"""
        # Create sample data
        data = pd.DataFrame({
            'close': [100, 102, 101, 103, 105],
            'atm_straddle_premium': [10, 11, 10.5, 12, 13]
        })
        
        # Initialize indicator
        config = {
            'periods': [5, 20],
            'instruments': ['underlying', 'atm_straddle']
        }
        indicator = EnhancedEMAIndicator(config)
        
        # Calculate
        result = indicator.calculate(data)
        
        # Verify
        assert 'ema_underlying_5' in result.columns
        assert 'ema_signal' in result.columns
        assert result['ema_signal'].abs().max() <= 1.0
```

8.1.2. **Integration tests**
```python
# File: backtester_v2/tests/test_regime_integration.py
import pytest
from ..market_regime.strategy import MarketRegimeStrategy
from ..strategies.regime_enhanced import TBSRegimeStrategy

class TestRegimeIntegration:
    def test_regime_calculation_pipeline(self, sample_data):
        """Test complete regime calculation pipeline"""
        config = {
            'symbol': 'NIFTY',
            'indicators': [
                {'id': 'ema', 'type': 'ema', 'weight': 0.3},
                {'id': 'vwap', 'type': 'vwap', 'weight': 0.3},
                {'id': 'premium', 'type': 'premium', 'weight': 0.4}
            ]
        }
        
        strategy = MarketRegimeStrategy(config)
        results = strategy.execute('2024-01-01', '2024-01-31')
        
        assert not results.empty
        assert 'regime_type' in results.columns
        assert 'confidence' in results.columns
        
    def test_strategy_enhancement(self):
        """Test regime-enhanced strategy execution"""
        config = {
            'symbol': 'NIFTY',
            'legs': [...],  # TBS configuration
            'regime_filter': {
                'enabled': True,
                'allowed_regimes': ['BULLISH', 'NEUTRAL'],
                'min_confidence': 0.7
            }
        }
        
        strategy = TBSRegimeStrategy(config)
        results = strategy.execute('2024-01-01', '2024-01-31')
        
        # Verify regime filtering applied
        assert 'regime_type' in results.columns
```

---

## Phase 9: API and UI (Week 10-11)

### Task 9.1: API Development
**Objective**: Create comprehensive APIs for regime system

#### Sub-tasks:
9.1.1. **Regime API endpoints**
```python
# File: backtester_v2/integration/regime_api.py
from fastapi import APIRouter, UploadFile, BackgroundTasks
from typing import Optional, List
from ..market_regime.models import RegimeConfig
from ..market_regime.strategy import MarketRegimeStrategy

router = APIRouter(prefix="/api/v2/regime", tags=["regime"])

@router.post("/config/upload")
async def upload_regime_config(file: UploadFile):
    """Upload Excel configuration for market regime"""
    # Parse configuration
    parser = RegimeConfigParser()
    config = parser.parse(file)
    
    # Validate
    validation_results = validate_config(config)
    
    if validation_results.is_valid:
        # Store configuration
        store_config(config)
        return {"status": "success", "message": "Configuration uploaded successfully"}
    else:
        return {"status": "error", "errors": validation_results.errors}

@router.post("/calculate")
async def calculate_regime(
    symbol: str,
    start_date: str,
    end_date: str,
    background_tasks: BackgroundTasks
):
    """Calculate market regime for specified period"""
    task_id = generate_task_id()
    
    # Start background calculation
    background_tasks.add_task(
        run_regime_calculation,
        task_id,
        symbol,
        start_date,
        end_date
    )
    
    return {"task_id": task_id, "status": "processing"}

@router.get("/performance/report")
async def get_performance_report(
    lookback_days: int = 252,
    indicator_id: Optional[str] = None
):
    """Get performance report for regime indicators"""
    tracker = PerformanceTracker({})
    report = tracker.generate_report(lookback_days, indicator_id)
    
    return report

@router.post("/weights/optimize")
async def optimize_weights(
    method: str = "sharpe",
    constraints: Optional[Dict] = None
):
    """Optimize indicator weights based on performance"""
    optimizer = WeightOptimizer({'optimization_method': method})
    
    # Get performance data
    perf_data = get_performance_data()
    
    # Optimize
    optimized_weights = optimizer.optimize(perf_data, constraints or {})
    
    return {
        "optimized_weights": optimized_weights,
        "current_weights": get_current_weights(),
        "expected_improvement": calculate_expected_improvement(optimized_weights)
    }
```

9.1.2. **WebSocket support**
```python
# File: backtester_v2/integration/regime_websocket.py
from fastapi import WebSocket
from typing import Dict
import asyncio

class RegimeWebSocketManager:
    """Manage WebSocket connections for real-time regime updates"""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        
    async def connect(self, websocket: WebSocket, client_id: str):
        await websocket.accept()
        self.active_connections[client_id] = websocket
        
    async def send_regime_update(self, client_id: str, data: Dict):
        """Send regime update to specific client"""
        if client_id in self.active_connections:
            await self.active_connections[client_id].send_json(data)
            
    async def broadcast_regime_change(self, data: Dict):
        """Broadcast regime change to all connected clients"""
        for client_id, websocket in self.active_connections.items():
            try:
                await websocket.send_json(data)
            except:
                # Remove disconnected clients
                del self.active_connections[client_id]
```

### Task 9.2: Dashboard Development
**Objective**: Create UI for regime monitoring

#### Sub-tasks:
9.2.1. **Regime dashboard component**
```javascript
// File: backtester_v2/frontend/components/RegimeDashboard.jsx
import React, { useState, useEffect } from 'react';
import { LineChart, BarChart } from 'recharts';
import { useWebSocket } from '../hooks/useWebSocket';

const RegimeDashboard = () => {
    const [regimeData, setRegimeData] = useState([]);
    const [performance, setPerformance] = useState({});
    const { data: liveData } = useWebSocket('/api/v2/ws/regime');
    
    useEffect(() => {
        // Fetch initial data
        fetchRegimeData();
        fetchPerformanceData();
    }, []);
    
    useEffect(() => {
        // Update with live data
        if (liveData) {
            setRegimeData(prev => [...prev, liveData]);
        }
    }, [liveData]);
    
    return (
        <div className="regime-dashboard">
            <h2>Market Regime Analysis</h2>
            
            <div className="current-regime">
                <h3>Current Regime: {regimeData[0]?.regime_type}</h3>
                <p>Confidence: {regimeData[0]?.confidence}%</p>
            </div>
            
            <div className="charts">
                <RegimeHistoryChart data={regimeData} />
                <IndicatorPerformanceChart data={performance} />
                <WeightEvolutionChart />
            </div>
            
            <div className="controls">
                <ConfigUpload />
                <OptimizationControls />
            </div>
        </div>
    );
};
```

---

## Phase 10: Deployment (Week 11-12)

### Task 10.1: Deployment Preparation
**Objective**: Prepare for production deployment

#### Sub-tasks:
10.1.1. **Create deployment scripts**
```bash
#!/bin/bash
# File: backtester_v2/scripts/deploy_regime.sh

echo "Deploying Enhanced Market Regime System..."

# 1. Database migrations
echo "Running database migrations..."
python -m backtester_v2.market_regime.schema.migrate

# 2. Install dependencies
echo "Installing dependencies..."
pip install -r requirements_regime.txt

# 3. Run tests
echo "Running tests..."
pytest backtester_v2/tests/test_regime_* -v

# 4. Build frontend
echo "Building frontend..."
cd frontend && npm run build

# 5. Deploy services
echo "Deploying services..."
docker-compose -f docker-compose.regime.yml up -d

# 6. Health check
echo "Running health checks..."
python -m backtester_v2.scripts.health_check

echo "Deployment complete!"
```

10.1.2. **Docker configuration**
```yaml
# File: docker-compose.regime.yml
version: '3.8'

services:
  regime-calculator:
    build:
      context: .
      dockerfile: Dockerfile.regime
    environment:
      - HEAVYDB_HOST=heavydb
      - REDIS_HOST=redis
      - GPU_ENABLED=true
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    volumes:
      - ./config:/app/config
      - ./logs:/app/logs

  regime-api:
    build:
      context: .
      dockerfile: Dockerfile.api
    ports:
      - "8001:8000"
    environment:
      - REGIME_SERVICE=regime-calculator:50051
    depends_on:
      - regime-calculator

  regime-monitor:
    build:
      context: .
      dockerfile: Dockerfile.monitor
    environment:
      - PROMETHEUS_PUSHGATEWAY=prometheus:9091
    depends_on:
      - regime-calculator
```

### Task 10.2: Monitoring and Maintenance
**Objective**: Setup monitoring and maintenance procedures

#### Sub-tasks:
10.2.1. **Performance monitoring**
```python
# File: backtester_v2/market_regime/monitoring/metrics.py
from prometheus_client import Counter, Histogram, Gauge
import time

# Define metrics
regime_calculation_time = Histogram(
    'regime_calculation_seconds',
    'Time spent calculating market regime'
)

regime_accuracy = Gauge(
    'regime_prediction_accuracy',
    'Current regime prediction accuracy',
    ['indicator', 'timeframe']
)

indicator_weight = Gauge(
    'indicator_weight',
    'Current weight of indicator',
    ['indicator_id']
)

regime_changes = Counter(
    'regime_changes_total',
    'Total number of regime changes detected',
    ['from_regime', 'to_regime']
)

class RegimeMonitor:
    """Monitor regime system performance"""
    
    def track_calculation(self, func):
        """Decorator to track calculation time"""
        def wrapper(*args, **kwargs):
            start = time.time()
            result = func(*args, **kwargs)
            regime_calculation_time.observe(time.time() - start)
            return result
        return wrapper
        
    def update_accuracy(self, indicator: str, timeframe: int, accuracy: float):
        """Update accuracy metric"""
        regime_accuracy.labels(
            indicator=indicator,
            timeframe=timeframe
        ).set(accuracy)
```

10.2.2. **Maintenance procedures**
```markdown
# File: backtester_v2/docs/regime_maintenance.md

# Market Regime System Maintenance Guide

## Daily Tasks
1. Check performance metrics dashboard
2. Review indicator accuracy reports
3. Verify data pipeline health
4. Monitor GPU utilization

## Weekly Tasks
1. Run weight optimization
2. Review regime classification accuracy
3. Update performance reports
4. Check for configuration drift

## Monthly Tasks
1. Full system performance review
2. Retrain adaptive weights
3. Database optimization
4. Update documentation

## Troubleshooting
### Low Accuracy
1. Check data quality
2. Review indicator configurations
3. Run weight optimization
4. Verify market conditions

### Performance Issues
1. Check GPU memory usage
2. Review query performance
3. Optimize batch sizes
4. Scale horizontally if needed
```

---

## Implementation Timeline Summary

| Phase | Duration | Key Deliverables |
|-------|----------|------------------|
| Phase 1 | Week 1-2 | Code refactoring, base infrastructure |
| Phase 2 | Week 2-3 | Database schema, migration scripts |
| Phase 3 | Week 3-4 | Core regime module, configuration system |
| Phase 4 | Week 4-6 | Enhanced indicators, integration |
| Phase 5 | Week 6-7 | Strategy enhancements, regime filtering |
| Phase 6 | Week 7-8 | Performance tracking, optimization |
| Phase 7 | Week 8-9 | GPU optimization, multi-node support |
| Phase 8 | Week 9-10 | Comprehensive testing, validation |
| Phase 9 | Week 10-11 | API development, UI dashboard |
| Phase 10 | Week 11-12 | Deployment, monitoring, documentation |

## Success Criteria

1. **Performance Metrics**
   - Regime calculation: < 100ms per symbol
   - Indicator accuracy: > 70%
   - GPU utilization: > 60%
   - API response time: < 500ms

2. **Integration Goals**
   - All strategies support regime filtering
   - Excel configuration fully functional
   - Real-time regime updates working
   - Performance tracking automated

3. **Quality Standards**
   - Test coverage: > 80%
   - Documentation complete
   - Code follows backtester_v2 patterns
   - GPU optimization implemented

## Risk Mitigation

1. **Technical Risks**
   - GPU memory overflow: Implement batching
   - Integration conflicts: Extensive testing
   - Performance degradation: Continuous monitoring

2. **Data Risks**
   - Missing indicators: Fallback mechanisms
   - Data quality issues: Validation layers
   - Historical data gaps: Interpolation strategies

3. **Operational Risks**
   - Deployment failures: Rollback procedures
   - Configuration errors: Validation and versioning
   - Performance drift: Automated retraining

This comprehensive plan ensures smooth integration of the Enhanced Market Regime System with the existing backtester_v2 architecture while maintaining code quality and performance standards.