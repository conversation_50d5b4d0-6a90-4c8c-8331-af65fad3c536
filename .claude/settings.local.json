{"permissions": {"allow": ["Bash(ls:*)", "Bash(grep:*)", "<PERSON><PERSON>(python3:*)", "Bash(--portfolio-excel bt/backtester_stable/BTRUN/input_sheets/input_portfolio_2024_4months.xlsx )", "Bash(--output-path bt/backtester_stable/BTRUN/output/tbs_2024_2months_fixed.xlsx )", "Bash(--workers auto )", "Bash(--batch-days 7)", "<PERSON><PERSON>(time python3:*)", "Bash(awk:*)", "<PERSON><PERSON>(python test:*)", "Bash(rm:*)", "Bash(find:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./run_optimized_test.sh)", "Bash(./run_1year_backtest.sh:*)", "Bash(./run_dte_validation_test.sh:*)", "<PERSON><PERSON>(python:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(mv:*)", "Bash(PYTHONPATH=/srv/samba/shared python3 excel_to_yaml_pipeline/cli.py pipeline -i input_tbs_multi_legs.xlsx -o excel_to_yaml_pipeline/output_test/ --metrics)", "Bash(PYTHONPATH=/srv/samba/shared python3 excel_to_yaml_pipeline/cli.py validate -i input_tbs_multi_legs.xlsx -o excel_to_yaml_pipeline/validation_tbs.json)", "<PERSON><PERSON>(touch:*)", "Bash(PYTHONPATH=/srv/samba/shared/bt python3 -m pytest backtester_stable/BTRUN/tests/test_tv_column_mapping.py::TestTvColumnMapper::test_tv_setting_boolean_columns -v)", "Bash(PYTHONPATH=/srv/samba/shared/bt python3 -m pytest backtester_stable/BTRUN/tests/test_tv_column_mapping.py -v)", "Bash(PYTHONPATH=/srv/samba/shared/bt python3 -m pytest backtester_stable/BTRUN/tests/test_tv_all_columns.py -v)", "Bash(./run_tv_comparison.sh:*)", "<PERSON><PERSON>(sed:*)", "Bash(cp:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(tree:*)", "Bash(gh auth:*)", "<PERSON><PERSON>(cat:*)", "<PERSON><PERSON>(sudo apt:*)", "Bash(gh:*)", "Bash(git init:*)", "Bash(git branch:*)", "Bash(git add:*)", "Bash(git config:*)", "Bash(claude mcp add playwright -s user -- npx -y @playwright/mcp@latest)", "Bash(pip3 install:*)", "mcp__playwright__browser_install", "mcp__playwright__browser_navigate", "<PERSON><PERSON>(curl:*)", "mcp__playwright__browser_take_screenshot", "mcp__playwright__browser_type", "mcp__playwright__browser_click", "mcp__playwright__browser_press_key", "mcp__playwright__browser_wait_for", "mcp__playwright__browser_console_messages", "mcp__playwright__browser_network_requests", "Bash(kill:*)", "mcp__playwright__browser_snapshot", "mcp__playwright__browser_close", "mcp__playwright__browser_hover", "mcp__playwright__browser_file_upload", "mcp__playwright__browser_handle_dialog", "mcp__playwright__browser_navigate_forward", "Bash(ss:*)", "Bash(sudo lsof:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "Bash(# Test login flow\necho \"Testing OTP send...\"\ncurl -X POST http://localhost:8000/api/v1/auth/send-otp \\\n  -H \"Content-Type: application/json\" \\\n  -d '{\"phone\": \"9876543210\"}' -s | jq .\n\necho -e \"\\nTesting OTP verify...\"\ncurl -X POST http://localhost:8000/api/v1/auth/verify-otp \\\n  -H \"Content-Type: application/json\" \\\n  -d '{\"phone\": \"9876543210\", \"otp\": \"123456\"}' -s | jq .)", "Bash(pdftotext:*)", "Bash(heavysql:*)", "<PERSON><PERSON>(sudo cat:*)", "Bash(sudo chmod:*)", "Bash(sudo systemctl:*)", "Bash(./run_full_load.sh:*)", "<PERSON><PERSON>(crontab:*)", "Bash(# Find and kill the current batch loader\nps aux | grep batch_load_nifty.py | grep -v grep | awk '{print $2}' | xargs -r kill -9\necho \"Stopped existing batch loader\")", "Bash(./run_multiprocess_load.sh:*)", "<PERSON><PERSON>(top:*)", "Bash(systemctl status:*)", "Ba<PERSON>(strace:*)", "Bash(for f in *.sql)", "Bash(do sed -i '/^--/d' \"$f\")", "Bash(done)", "Bash(# Test COPY with error checking\ncat > /tmp/test_copy.sql << 'EOF'\nCOPY nifty_option_chain FROM '/srv/samba/shared/market_data/nifty/oc_with_futures/IV_2025_feb_nifty_futures.csv' \nWITH (header='true', delimiter=',');\nEOF\n\n/opt/heavyai/bin/heavysql -p HyperInteractive -u admin -d heavyai < /tmp/test_copy.sql 2>&1 | grep -E \"(Exception|Error|error|rows|Loaded)\" | head -10)", "<PERSON><PERSON>(diff:*)", "Bash(ln:*)", "Bash(systemctl stop:*)", "<PERSON><PERSON>(nvidia-smi:*)", "Bash(/opt/heavyai/bin/heavysql:*)", "Bash(export:*)", "Ba<PERSON>(unzip:*)", "<PERSON><PERSON>(cut:*)", "Bash(/srv/samba/shared/market_data_etl/scripts/monitor_etl.sh:*)", "Bash(./setup_enhanced_etl_cron.sh:*)", "Bash(./setup_etl_monitoring.sh:*)", "Bash(./etl_status.sh:*)", "<PERSON><PERSON>(sudo journalctl:*)", "<PERSON><PERSON>(sudo supervisorctl restart:*)", "Bash(pip install:*)", "<PERSON><PERSON>(supervisorctl restart:*)", "<PERSON><PERSON>(journalctl:*)", "Bash(./start_server.sh:*)", "<PERSON><PERSON>(jq:*)", "Bash(./runners/run_comprehensive_tests.sh:*)", "Bash(# Copy TBS file\ncp /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/tbs/input_tbs_portfolio.xlsx \\\n   /srv/samba/shared/bt/archive/backtester_stable/BTRUN/input_tbs_portfolio_phase2.xlsx\n\n# Copy TV file\ncp /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/tv/input_tv.xlsx \\\n   /srv/samba/shared/bt/archive/backtester_stable/BTRUN/input_tv_phase2.xlsx\n\n# Copy ORB file\ncp /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/orb/input_orb.xlsx \\\n   /srv/samba/shared/bt/archive/backtester_stable/BTRUN/input_orb_phase2.xlsx\n\n# Copy OI file\ncp /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/oi/input_maxoi.xlsx \\\n   /srv/samba/shared/bt/archive/backtester_stable/BTRUN/input_maxoi_phase2.xlsx\n\n# Also copy to our phase2 inputs directory for tracking (with proper filenames)\ncp /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/tbs/input_tbs_portfolio.xlsx \\\n   /srv/samba/shared/tests/e2e/phase2_archive_baseline/tbs/inputs/input_tbs_portfolio.xlsx\n\ncp /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/tv/input_tv.xlsx \\\n   /srv/samba/shared/tests/e2e/phase2_archive_baseline/tv/inputs/input_tv.xlsx\n\ncp /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/orb/input_orb.xlsx \\\n   /srv/samba/shared/tests/e2e/phase2_archive_baseline/orb/inputs/input_orb.xlsx\n\ncp /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/oi/input_maxoi.xlsx \\\n   /srv/samba/shared/tests/e2e/phase2_archive_baseline/oi/inputs/input_maxoi.xlsx\n\n# Verify the copies\nls -la /srv/samba/shared/bt/archive/backtester_stable/BTRUN/*_phase2.xlsx)", "Bash(# Check if source files exist\nls -la /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/tbs/input_tbs_portfolio.xlsx\nls -la /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/tv/input_tv.xlsx\nls -la /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/orb/input_orb.xlsx\nls -la /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/oi/input_maxoi.xlsx)", "Bash(# Copy files using sudo to handle permission issues\nsudo cp /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/tbs/input_tbs_portfolio.xlsx \\\n   /srv/samba/shared/bt/archive/backtester_stable/BTRUN/input_tbs_portfolio_phase2.xlsx\n\nsudo cp /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/tv/input_tv.xlsx \\\n   /srv/samba/shared/bt/archive/backtester_stable/BTRUN/input_tv_phase2.xlsx\n\nsudo cp /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/orb/input_orb.xlsx \\\n   /srv/samba/shared/bt/archive/backtester_stable/BTRUN/input_orb_phase2.xlsx\n\nsudo cp /srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/oi/input_maxoi.xlsx \\\n   /srv/samba/shared/bt/archive/backtester_stable/BTRUN/input_maxoi_phase2.xlsx\n\n# Set proper permissions\nsudo chmod 755 /srv/samba/shared/bt/archive/backtester_stable/BTRUN/*_phase2.xlsx\n\n# Verify\nls -la /srv/samba/shared/bt/archive/backtester_stable/BTRUN/*_phase2.xlsx)", "Bash(__NEW_LINE__ cp /srv/samba/shared/bt/archive/backtester_stable/BTRUN/input_tbs_portfolio_phase2.xlsx ./tbs/inputs/)", "Bash(__NEW_LINE__ ls -la */inputs/)", "Bash(./run_archive_backtests.sh:*)", "Bash(# Clean up previous outputs\nrm -f /srv/samba/shared/tests/e2e/phase2_archive_baseline/tbs/outputs/*.xlsx\nrm -f /srv/samba/shared/tests/e2e/phase2_archive_baseline/tv/outputs/*.xlsx\nrm -f /srv/samba/shared/tests/e2e/phase2_archive_baseline/orb/outputs/*.xlsx\nrm -f /srv/samba/shared/tests/e2e/phase2_archive_baseline/oi/outputs/*.xlsx\n\n# Re-run the archive system backtests\ncd /srv/samba/shared/bt/archive/backtester_stable/BTRUN\n\necho \"=== Re-running Archive System with April 2024 dates ===\"\n\n# Run TBS\necho \"Running TBS...\"\npython3 BTRunPortfolio.py > /srv/samba/shared/tests/e2e/phase2_archive_baseline/tbs/logs/tbs_april_run.log 2>&1 &\nTBS_PID=$!\n\n# Wait a bit and check if it's running\nsleep 5\nif ps -p $TBS_PID > /dev/null; then\n    echo \"TBS is running (PID: $TBS_PID)\"\n    wait $TBS_PID\n    echo \"TBS completed\"\nelse\n    echo \"TBS failed to start or completed quickly\"\nfi\n\n# Copy any new outputs\nNEW_FILES=$(find Trades -name \"*.xlsx\" -newer /srv/samba/shared/tests/e2e/phase2_archive_baseline/update_dates_for_april.py 2>/dev/null | wc -l)\necho \"Found $NEW_FILES new output files\"\n\nif [ $NEW_FILES -gt 0 ]; then\n    find Trades -name \"*.xlsx\" -newer /srv/samba/shared/tests/e2e/phase2_archive_baseline/update_dates_for_april.py -exec cp {} /srv/samba/shared/tests/e2e/phase2_archive_baseline/tbs/outputs/ \\; 2>/dev/null\nfi)", "Bash(# Check the original source file structure\npython3 -c \"\nimport openpyxl\n\n# Check original file from new system\norig_file = '/srv/samba/shared/bt/backtester_stable/BTRUN/input_sheets/tbs/input_tbs_portfolio.xlsx'\nwb = openpyxl.load_workbook(orig_file)\nprint('Sheets in original TBS file:', wb.sheetnames)\n\n# Check each sheet for date fields\nfor sheet_name in wb.sheetnames:\n    ws = wb[sheet_name]\n    print(f'\\nChecking {sheet_name}:')\n    for row in range(1, min(20, ws.max_row + 1)):\n        for col in range(1, min(10, ws.max_column + 1)):\n            cell_val = ws.cell(row=row, column=col).value\n            if cell_val and isinstance(cell_val, str) and ('date' in str(cell_val).lower() or 'start' in str(cell_val).lower() or 'end' in str(cell_val).lower()):\n                next_val = ws.cell(row=row, column=col+1).value if col < ws.max_column else None\n                print(f'  Found at ({row},{col}): {cell_val} = {next_val}')\n\")", "Bash(# Check what outputs we have so far\necho \"=== Phase 2 Archive System Outputs ===\"\necho \"\"\n\necho \"TBS Outputs:\"\nls -la /srv/samba/shared/tests/e2e/phase2_archive_baseline/tbs/outputs/ 2>/dev/null | grep -v \"^total\" | grep -v \"^d\" || echo \"  No outputs found\"\n\necho -e \"\\nTV Outputs:\"\nls -la /srv/samba/shared/tests/e2e/phase2_archive_baseline/tv/outputs/ 2>/dev/null | grep -v \"^total\" | grep -v \"^d\" || echo \"  No outputs found\"\n\necho -e \"\\nORB Outputs:\"\nls -la /srv/samba/shared/tests/e2e/phase2_archive_baseline/orb/outputs/ 2>/dev/null | grep -v \"^total\" | grep -v \"^d\" || echo \"  No outputs found\"\n\necho -e \"\\nOI Outputs:\"\nls -la /srv/samba/shared/tests/e2e/phase2_archive_baseline/oi/outputs/ 2>/dev/null | grep -v \"^total\" | grep -v \"^d\" || echo \"  No outputs found\"\n\n# Check logs for errors\necho -e \"\\n=== Checking Logs for Issues ===\"\necho \"TBS Log (last 10 lines):\"\ntail -10 /srv/samba/shared/tests/e2e/phase2_archive_baseline/tbs/logs/tbs_april_final.log 2>/dev/null || echo \"  No log found\"\n\necho -e \"\\nORB Log (last 10 lines):\"\ntail -10 /srv/samba/shared/tests/e2e/phase2_archive_baseline/orb/logs/orb_april_run.log 2>/dev/null || echo \"  No log found\")", "<PERSON><PERSON>(docker compose:*)", "Bash(sudo mysql:*)", "<PERSON><PERSON>(docker-compose ps:*)", "<PERSON><PERSON>(docker-compose restart:*)"]}, "enableAllProjectMcpServers": false}