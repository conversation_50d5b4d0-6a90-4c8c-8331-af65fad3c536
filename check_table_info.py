#!/usr/bin/env python3
"""
Check table information and optimization status
"""
from heavydb import connect
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_table_info():
    """Check table structure and optimization"""
    try:
        # Connect to HeavyDB
        conn = connect(
            host='localhost',
            port=6274,
            user='admin',
            password='HyperInteractive',
            dbname='heavyai',
            protocol='binary'
        )
        logger.info("Connected to HeavyDB")
        
        cursor = conn.cursor()
        
        # Get table info
        logger.info("\n=== Table Structure ===")
        cursor.execute("SHOW CREATE TABLE nifty_option_chain")
        create_stmt = cursor.fetchone()[0]
        logger.info(f"Table definition:\n{create_stmt}")
        
        # Get table statistics
        logger.info("\n=== Table Statistics ===")
        cursor.execute("SELECT COUNT(*) as total_rows FROM nifty_option_chain")
        total_rows = cursor.fetchone()[0]
        logger.info(f"Total rows: {total_rows:,}")
        
        # Check distribution by index
        logger.info("\n=== Data Distribution by Index ===")
        cursor.execute("""
            SELECT index_name, COUNT(*) as row_count
            FROM nifty_option_chain
            GROUP BY index_name
            ORDER BY row_count DESC
        """)
        
        for row in cursor.fetchall():
            logger.info(f"{row[0]}: {row[1]:,} rows")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        logger.error(f"Check failed: {e}")
        raise

if __name__ == "__main__":
    check_table_info()