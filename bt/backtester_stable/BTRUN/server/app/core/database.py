"""
Database connection module for HeavyDB
"""
import os
import logging
from typing import Optional
from heavydb import connect
import asyncio
from concurrent.futures import Thread<PERSON>oolExecutor
from contextlib import asynccontextmanager
import time

logger = logging.getLogger(__name__)

# Thread pool for blocking database operations
db_thread_pool = ThreadPoolExecutor(max_workers=10)

# Connection pool
connection_pool = []
MAX_POOL_SIZE = 5
pool_lock = asyncio.Lock()

class HeavyDBConfig:
    """HeavyDB connection configuration"""
    HOST = os.getenv('HEAVYDB_HOST', 'localhost')
    PORT = int(os.getenv('HEAVYDB_PORT', 6274))
    USER = os.getenv('HEAVYDB_USER', 'admin')
    PASSWORD = os.getenv('HEAVYDB_PASSWORD', 'HyperInteractive')
    DATABASE = os.getenv('HEAVYDB_DATABASE', 'heavyai')
    PROTOCOL = 'binary'

def create_connection():
    """Create a new HeavyDB connection"""
    try:
        conn = connect(
            host=HeavyDBConfig.HOST,
            port=HeavyDBConfig.PORT,
            user=HeavyDBConfig.USER,
            password=HeavyDBConfig.PASSWORD,
            dbname=HeavyDBConfig.DATABASE,
            protocol=HeavyDBConfig.PROTOCOL
        )
        logger.info(f"Connected to HeavyDB at {HeavyDBConfig.HOST}:{HeavyDBConfig.PORT}")
        return conn
    except Exception as e:
        logger.error(f"Failed to connect to HeavyDB: {e}")
        raise

async def get_pooled_connection():
    """Get a connection from the pool or create a new one"""
    async with pool_lock:
        # Try to get from pool
        if connection_pool:
            conn = connection_pool.pop()
            try:
                # Test if connection is still alive
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                cursor.close()
                return conn
            except:
                # Connection is dead, create new one
                pass
        
        # Create new connection if pool is empty or connection was dead
        loop = asyncio.get_event_loop()
        conn = await loop.run_in_executor(db_thread_pool, create_connection)
        return conn

async def return_to_pool(conn):
    """Return connection to pool"""
    async with pool_lock:
        if len(connection_pool) < MAX_POOL_SIZE:
            try:
                # Test if connection is still alive before returning to pool
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                cursor.close()
                connection_pool.append(conn)
            except:
                # Connection is dead, don't return to pool
                try:
                    conn.close()
                except:
                    pass
        else:
            # Pool is full, close connection
            try:
                conn.close()
            except:
                pass

async def get_db_connection():
    """
    Get HeavyDB connection (async wrapper)
    Returns connection object that can be used with cursor()
    """
    # Get connection from pool
    conn = await get_pooled_connection()
    
    # Wrap the connection to make cursor operations async-friendly
    class AsyncConnection:
        def __init__(self, conn):
            self._conn = conn
            self._returned = False
            
        def cursor(self):
            """Get cursor that supports async execute"""
            cursor = self._conn.cursor()
            
            # Wrap cursor to make execute async
            class AsyncCursor:
                def __init__(self, cursor):
                    self._cursor = cursor
                    
                def execute(self, query, params=None):
                    """Execute query synchronously (HeavyDB doesn't support async)"""
                    try:
                        if params:
                            return self._cursor.execute(query, params)
                        else:
                            return self._cursor.execute(query)
                    except Exception as e:
                        logger.error(f"Query execution failed: {e}")
                        raise
                
                def fetchone(self):
                    """Fetch one row"""
                    return self._cursor.fetchone()
                    
                def fetchall(self):
                    """Fetch all rows"""
                    return self._cursor.fetchall()
                    
                def close(self):
                    """Close cursor"""
                    self._cursor.close()
                    
            return AsyncCursor(cursor)
            
        def close(self):
            """Return connection to pool instead of closing"""
            if not self._returned:
                self._returned = True
                asyncio.create_task(return_to_pool(self._conn))
    
    return AsyncConnection(conn)

# For backward compatibility
def get_sync_connection():
    """Get synchronous HeavyDB connection"""
    return create_connection()