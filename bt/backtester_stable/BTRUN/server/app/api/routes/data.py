"""
Data availability API routes
"""
from fastapi import APIRouter, Depends, HTTPException
from typing import Dict, Any
import logging

from ..core.auth import get_current_user
from ..core.database import get_db_connection

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/data", tags=["data"])

@router.get("/index-availability")
async def get_index_availability(
    current_user: dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Get data availability for all supported indices
    """
    try:
        # Get HeavyDB connection
        conn = await get_db_connection()
        cursor = conn.cursor()
        
        # Query all indices at once for better performance
        indices = ['NIFTY', 'BANKNIFTY', 'MIDCAPNIFTY', 'SENSEX', 'FINNIFTY', 'BANKEX']
        
        # Single query to get all index data
        query = """
        SELECT 
            index_name,
            COUNT(*) as row_count,
            COUNT(DISTINCT trade_date) as trading_days,
            MIN(trade_date) as min_date,
            MAX(trade_date) as max_date,
            COUNT(DISTINCT strike) as unique_strikes,
            COUNT(DISTINCT expiry_bucket) as expiry_types
        FROM nifty_option_chain
        WHERE index_name IN ('NIFTY', 'BANKNIFTY', 'MIDCAPNIFTY', 'SENSEX', 'FINNIFTY', 'BANKEX')
        GROUP BY index_name
        """
        
        cursor.execute(query)
        rows = cursor.fetchall()
        
        # Convert results to dictionary
        result = {}
        found_indices = set()
        
        for row in rows:
            index_name = row[0]
            found_indices.add(index_name)
            result[index_name] = {
                'available': True,
                'row_count': row[1],
                'trading_days': row[2],
                'min_date': row[3].strftime('%Y-%m-%d') if row[3] else None,
                'max_date': row[4].strftime('%Y-%m-%d') if row[4] else None,
                'unique_strikes': row[5],
                'expiry_types': row[6]
            }
        
        # Add entries for indices with no data
        for index in indices:
            if index not in found_indices:
                result[index] = {
                    'available': False,
                    'row_count': 0,
                    'trading_days': 0,
                    'min_date': None,
                    'max_date': None,
                    'unique_strikes': 0,
                    'expiry_types': 0
                }
        
        cursor.close()
        conn.close()
        return result
        
    except Exception as e:
        logger.error(f"Error checking index availability: {e}")
        raise HTTPException(status_code=500, detail="Failed to check data availability")

@router.get("/index/{index_name}/summary")
async def get_index_summary(
    index_name: str,
    current_user: dict = Depends(get_current_user)
) -> Dict[str, Any]:
    """
    Get detailed summary for a specific index
    """
    try:
        # Validate index name
        valid_indices = ['NIFTY', 'BANKNIFTY', 'MIDCAPNIFTY', 'SENSEX', 'FINNIFTY', 'BANKEX']
        if index_name.upper() not in valid_indices:
            raise HTTPException(status_code=400, detail=f"Invalid index: {index_name}")
        
        index_name = index_name.upper()
        
        # Get HeavyDB connection
        conn = await get_db_connection()
        cursor = conn.cursor()
        
        # Get summary statistics
        query = """
        SELECT 
            COUNT(*) as total_rows,
            COUNT(DISTINCT trade_date) as trading_days,
            MIN(trade_date) as min_date,
            MAX(trade_date) as max_date,
            COUNT(DISTINCT strike) as unique_strikes,
            MIN(spot) as min_spot,
            MAX(spot) as max_spot,
            AVG(spot) as avg_spot
        FROM nifty_option_chain
        WHERE index_name = %s
        """
        
        cursor.execute(query, (index_name,))
        data = cursor.fetchone()
        
        if not data or data[0] == 0:
            return {
                'index': index_name,
                'available': False,
                'message': f"No data available for {index_name}"
            }
        
        # Get expiry distribution
        expiry_query = """
        SELECT expiry_bucket, COUNT(DISTINCT trade_date) as days, COUNT(*) as rows
        FROM nifty_option_chain
        WHERE index_name = %s
        GROUP BY expiry_bucket
        ORDER BY expiry_bucket
        """
        
        cursor.execute(expiry_query, (index_name,))
        expiry_data = cursor.fetchall()
        
        cursor.close()
        
        return {
            'index': index_name,
            'available': True,
            'total_rows': data[0],
            'trading_days': data[1],
            'date_range': {
                'start': data[2].strftime('%Y-%m-%d') if data[2] else None,
                'end': data[3].strftime('%Y-%m-%d') if data[3] else None
            },
            'unique_strikes': data[4],
            'spot_range': {
                'min': float(data[5]) if data[5] else None,
                'max': float(data[6]) if data[6] else None,
                'avg': float(data[7]) if data[7] else None
            },
            'expiry_distribution': [
                {
                    'type': row[0],
                    'days': row[1],
                    'rows': row[2]
                }
                for row in expiry_data
            ]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting index summary: {e}")
        raise HTTPException(status_code=500, detail="Failed to get index summary")