#!/usr/bin/env python3
"""
Real Data Testing for Dynamic Weightage Selection in Enhanced OI System

This script tests the dynamic weightage functionality with:
- Real HeavyDB data
- Actual input sheets
- MAXOI, COI, Delta, Volume, Vega, Theta factors
- Live market conditions
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import date, datetime, timedelta
import logging
from typing import Dict, List, Any, Optional
import time

# Add project root to path
sys.path.insert(0, '/srv/samba/shared/bt/backtester_stable/BTRUN')

# Import enhanced OI modules
from backtester_v2.strategies.oi.enhanced_models import (
    EnhancedOIConfig, EnhancedLegConfig, DynamicWeightConfig, 
    FactorConfig, PerformanceMetrics
)
from backtester_v2.strategies.oi.enhanced_parser import EnhancedOIParser
from backtester_v2.strategies.oi.dynamic_weight_engine import DynamicWeightEngine
from backtester_v2.strategies.oi.enhanced_processor import EnhancedOIProcessor
from backtester_v2.strategies.oi.unified_oi_interface import UnifiedOIInterface

# Import HeavyDB connection
try:
    from heavydb import connect
    HEAVYDB_AVAILABLE = True
except ImportError:
    HEAVYDB_AVAILABLE = False
    print("Warning: HeavyDB not available. Using mock data.")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class RealDataDynamicWeightageTest:
    """Test dynamic weightage with real HeavyDB data and input sheets."""
    
    def __init__(self):
        """Initialize the test environment."""
        self.base_dir = '/srv/samba/shared/bt/backtester_stable/BTRUN'
        self.input_sheets_dir = os.path.join(self.base_dir, 'input_sheets', 'oi')
        self.output_dir = os.path.join(self.base_dir, 'output', 'dynamic_weightage_tests')
        
        # Create output directory
        os.makedirs(self.output_dir, exist_ok=True)
        
        # HeavyDB connection
        self.db_connection = self._get_heavydb_connection()
        
        # Test results
        self.test_results = {}
        
    def _get_heavydb_connection(self):
        """Get HeavyDB connection."""
        if not HEAVYDB_AVAILABLE:
            logger.warning("HeavyDB not available, using mock connection")
            return None
            
        try:
            conn = connect(
                host='127.0.0.1',
                port=6274,
                user='admin',
                password='HyperInteractive',
                dbname='heavyai'
            )
            logger.info("Connected to HeavyDB successfully")
            return conn
        except Exception as e:
            logger.error(f"Failed to connect to HeavyDB: {e}")
            return None
    
    def test_real_input_sheets_parsing(self) -> Dict[str, Any]:
        """Test parsing of real input sheets."""
        logger.info("Testing real input sheets parsing...")
        
        results = {
            'test_name': 'Real Input Sheets Parsing',
            'status': 'RUNNING',
            'details': {}
        }
        
        try:
            parser = EnhancedOIParser()
            
            # Test files to check
            test_files = {
                'enhanced_config': 'input_enhanced_oi_config.xlsx',
                'portfolio_config': 'input_oi_portfolio.xlsx',
                'legacy_maxoi': 'input_maxoi.xlsx',
                'bt_setting': 'bt_setting.xlsx'
            }
            
            for file_type, filename in test_files.items():
                file_path = os.path.join(self.input_sheets_dir, filename)
                
                if os.path.exists(file_path):
                    format_detected = parser.detect_format(file_path)
                    results['details'][file_type] = {
                        'file_exists': True,
                        'format_detected': format_detected,
                        'file_path': file_path
                    }
                    logger.info(f"✅ {filename}: {format_detected}")
                else:
                    results['details'][file_type] = {
                        'file_exists': False,
                        'format_detected': 'FILE_NOT_FOUND',
                        'file_path': file_path
                    }
                    logger.warning(f"❌ {filename}: File not found")
            
            results['status'] = 'PASSED'
            
        except Exception as e:
            results['status'] = 'FAILED'
            results['error'] = str(e)
            logger.error(f"Input sheets parsing test failed: {e}")
        
        return results
    
    def test_heavydb_data_access(self) -> Dict[str, Any]:
        """Test access to real HeavyDB data."""
        logger.info("Testing HeavyDB data access...")
        
        results = {
            'test_name': 'HeavyDB Data Access',
            'status': 'RUNNING',
            'details': {}
        }
        
        if not self.db_connection:
            results['status'] = 'SKIPPED'
            results['reason'] = 'HeavyDB connection not available'
            return results
        
        try:
            # Test basic connectivity
            test_query = "SELECT COUNT(*) as total_rows FROM nifty_option_chain LIMIT 1"
            df = pd.read_sql(test_query, self.db_connection)
            total_rows = df['total_rows'].iloc[0]
            
            results['details']['total_rows'] = int(total_rows)
            logger.info(f"✅ Total rows in nifty_option_chain: {total_rows:,}")
            
            # Test recent data availability
            recent_data_query = """
            SELECT trade_date, COUNT(*) as daily_rows
            FROM nifty_option_chain
            WHERE trade_date >= '250101'
            GROUP BY trade_date
            ORDER BY trade_date DESC
            LIMIT 10
            """
            recent_df = pd.read_sql(recent_data_query, self.db_connection)
            results['details']['recent_dates'] = recent_df.to_dict('records')
            
            # Test OI data availability
            oi_data_query = """
            SELECT trade_date,
                   AVG(ce_oi) as avg_ce_oi,
                   AVG(pe_oi) as avg_pe_oi,
                   COUNT(*) as records
            FROM nifty_option_chain
            WHERE trade_date >= '250101'
            AND ce_oi > 0 AND pe_oi > 0
            GROUP BY trade_date
            ORDER BY trade_date DESC
            LIMIT 5
            """
            oi_df = pd.read_sql(oi_data_query, self.db_connection)
            results['details']['oi_data'] = oi_df.to_dict('records')
            
            # Test Greeks data availability
            greeks_query = """
            SELECT trade_date,
                   AVG(ce_delta) as avg_ce_delta,
                   AVG(pe_delta) as avg_pe_delta,
                   AVG(ce_vega) as avg_ce_vega,
                   AVG(pe_vega) as avg_pe_vega,
                   AVG(ce_theta) as avg_ce_theta,
                   AVG(pe_theta) as avg_pe_theta
            FROM nifty_option_chain
            WHERE trade_date >= '250101'
            AND ce_delta IS NOT NULL AND pe_delta IS NOT NULL
            GROUP BY trade_date
            ORDER BY trade_date DESC
            LIMIT 3
            """
            greeks_df = pd.read_sql(greeks_query, self.db_connection)
            results['details']['greeks_data'] = greeks_df.to_dict('records')
            
            # Test Volume data
            volume_query = """
            SELECT trade_date,
                   AVG(ce_volume) as avg_ce_volume,
                   AVG(pe_volume) as avg_pe_volume,
                   SUM(ce_volume + pe_volume) as total_volume
            FROM nifty_option_chain
            WHERE trade_date >= '250101'
            AND ce_volume > 0 AND pe_volume > 0
            GROUP BY trade_date
            ORDER BY trade_date DESC
            LIMIT 3
            """
            volume_df = pd.read_sql(volume_query, self.db_connection)
            results['details']['volume_data'] = volume_df.to_dict('records')
            
            results['status'] = 'PASSED'
            logger.info("✅ HeavyDB data access test passed")
            
        except Exception as e:
            results['status'] = 'FAILED'
            results['error'] = str(e)
            logger.error(f"HeavyDB data access test failed: {e}")
        
        return results
    
    def test_dynamic_weight_factors(self) -> Dict[str, Any]:
        """Test dynamic weight calculation for all factors."""
        logger.info("Testing dynamic weight factors...")
        
        results = {
            'test_name': 'Dynamic Weight Factors',
            'status': 'RUNNING',
            'details': {}
        }
        
        try:
            # Create comprehensive factor configuration
            factor_configs = [
                # OI Factors
                FactorConfig(factor_name='maxoi_factor', factor_type='OI', base_weight=0.25),
                FactorConfig(factor_name='coi_factor', factor_type='COI', base_weight=0.20),
                
                # Greeks Factors
                FactorConfig(factor_name='delta_factor', factor_type='GREEK', base_weight=0.15),
                FactorConfig(factor_name='vega_factor', factor_type='GREEK', base_weight=0.12),
                FactorConfig(factor_name='theta_factor', factor_type='GREEK', base_weight=0.10),
                
                # Market Factors
                FactorConfig(factor_name='volume_factor', factor_type='MARKET', base_weight=0.18)
            ]
            
            # Create dynamic weight configuration
            weight_config = DynamicWeightConfig(
                oi_factor_weight=0.45,  # MAXOI + COI
                greek_factor_weight=0.37,  # Delta + Vega + Theta
                market_factor_weight=0.18,  # Volume
                weight_learning_rate=0.02,
                performance_threshold=0.65
            )
            
            # Initialize weight engine
            engine = DynamicWeightEngine(weight_config, factor_configs)
            
            # Test initial weights
            initial_weights = engine.get_current_weights()
            results['details']['initial_weights'] = initial_weights
            
            # Verify weights sum to 1.0
            total_weight = sum(initial_weights.values())
            results['details']['initial_weight_sum'] = total_weight
            
            logger.info(f"Initial weights: {initial_weights}")
            logger.info(f"Total weight sum: {total_weight}")
            
            # Test weight updates with simulated performance data
            performance_scenarios = [
                {
                    'name': 'High OI Performance',
                    'performance_data': {
                        'maxoi_factor': 0.85,
                        'coi_factor': 0.75,
                        'delta_factor': 0.45,
                        'vega_factor': 0.50,
                        'theta_factor': 0.40,
                        'volume_factor': 0.60
                    },
                    'market_conditions': {
                        'volatility': 0.3,
                        'trend_strength': 0.1,
                        'liquidity': 0.8,
                        'regime': 'sideways'
                    }
                },
                {
                    'name': 'High Greeks Performance',
                    'performance_data': {
                        'maxoi_factor': 0.45,
                        'coi_factor': 0.40,
                        'delta_factor': 0.85,
                        'vega_factor': 0.80,
                        'theta_factor': 0.75,
                        'volume_factor': 0.55
                    },
                    'market_conditions': {
                        'volatility': 0.8,
                        'trend_strength': 0.6,
                        'liquidity': 0.6,
                        'regime': 'high_volatility'
                    }
                },
                {
                    'name': 'High Volume Performance',
                    'performance_data': {
                        'maxoi_factor': 0.50,
                        'coi_factor': 0.45,
                        'delta_factor': 0.40,
                        'vega_factor': 0.35,
                        'theta_factor': 0.30,
                        'volume_factor': 0.90
                    },
                    'market_conditions': {
                        'volatility': 0.5,
                        'trend_strength': 0.8,
                        'liquidity': 0.9,
                        'regime': 'trending'
                    }
                }
            ]
            
            scenario_results = {}
            
            for scenario in performance_scenarios:
                logger.info(f"Testing scenario: {scenario['name']}")
                
                updated_weights = engine.update_weights(
                    scenario['performance_data'],
                    scenario['market_conditions']
                )
                
                scenario_results[scenario['name']] = {
                    'updated_weights': updated_weights,
                    'weight_sum': sum(updated_weights.values()),
                    'performance_data': scenario['performance_data'],
                    'market_conditions': scenario['market_conditions']
                }
                
                logger.info(f"Updated weights for {scenario['name']}: {updated_weights}")
            
            results['details']['scenarios'] = scenario_results
            results['status'] = 'PASSED'
            
        except Exception as e:
            results['status'] = 'FAILED'
            results['error'] = str(e)
            logger.error(f"Dynamic weight factors test failed: {e}")
        
        return results

    def test_unified_interface_with_real_data(self) -> Dict[str, Any]:
        """Test unified interface with real input sheets and data."""
        logger.info("Testing unified interface with real data...")

        results = {
            'test_name': 'Unified Interface Real Data',
            'status': 'RUNNING',
            'details': {}
        }

        if not self.db_connection:
            results['status'] = 'SKIPPED'
            results['reason'] = 'HeavyDB connection not available'
            return results

        try:
            # Initialize unified interface
            interface = UnifiedOIInterface(self.db_connection)

            # Test with enhanced format files
            enhanced_portfolio = os.path.join(self.input_sheets_dir, 'input_oi_portfolio.xlsx')
            enhanced_strategy = os.path.join(self.input_sheets_dir, 'input_enhanced_oi_config.xlsx')

            # Test with legacy format files
            legacy_bt_setting = os.path.join(self.input_sheets_dir, 'bt_setting.xlsx')
            legacy_maxoi = os.path.join(self.input_sheets_dir, 'input_maxoi.xlsx')

            # Test file validation
            validation_results = interface.validate_input_files(
                portfolio_file=enhanced_portfolio if os.path.exists(enhanced_portfolio) else None,
                strategy_file=enhanced_strategy if os.path.exists(enhanced_strategy) else None,
                bt_setting_file=legacy_bt_setting if os.path.exists(legacy_bt_setting) else None,
                maxoi_file=legacy_maxoi if os.path.exists(legacy_maxoi) else None
            )

            results['details']['validation_results'] = validation_results

            # Test format detection and processing
            if validation_results['valid_combinations']:
                logger.info(f"Valid combinations found: {validation_results['valid_combinations']}")

                # Test processing with available format
                start_date = date(2025, 1, 2)
                end_date = date(2025, 1, 2)  # Single day test

                if 'enhanced' in validation_results['valid_combinations']:
                    logger.info("Testing enhanced format processing...")
                    enhanced_results = interface.process_oi_strategy(
                        portfolio_file=enhanced_portfolio,
                        strategy_file=enhanced_strategy,
                        start_date=start_date,
                        end_date=end_date,
                        output_format='enhanced'
                    )
                    results['details']['enhanced_processing'] = {
                        'format_info': enhanced_results.get('format_info', {}),
                        'processing_type': enhanced_results.get('processing_type', ''),
                        'capabilities_used': enhanced_results.get('capabilities_used', [])
                    }

                elif 'legacy' in validation_results['valid_combinations']:
                    logger.info("Testing legacy format processing...")
                    legacy_results = interface.process_oi_strategy(
                        bt_setting_file=legacy_bt_setting,
                        maxoi_file=legacy_maxoi,
                        start_date=start_date,
                        end_date=end_date,
                        output_format='golden'
                    )
                    results['details']['legacy_processing'] = {
                        'format_info': legacy_results.get('format_info', {}),
                        'processing_type': legacy_results.get('processing_type', ''),
                        'migration_notes': legacy_results.get('migration_notes', {})
                    }

            results['status'] = 'PASSED'
            logger.info("✅ Unified interface test passed")

        except Exception as e:
            results['status'] = 'FAILED'
            results['error'] = str(e)
            logger.error(f"Unified interface test failed: {e}")

        return results

    def run_all_tests(self) -> Dict[str, Any]:
        """Run all dynamic weightage tests."""
        logger.info("🚀 Starting comprehensive dynamic weightage testing with real data...")

        start_time = time.time()

        # Run all tests
        test_methods = [
            self.test_real_input_sheets_parsing,
            self.test_heavydb_data_access,
            self.test_dynamic_weight_factors,
            self.test_unified_interface_with_real_data
        ]

        all_results = {
            'test_suite': 'Dynamic Weightage Real Data Tests',
            'start_time': datetime.now().isoformat(),
            'tests': {},
            'summary': {}
        }

        passed_tests = 0
        failed_tests = 0
        skipped_tests = 0

        for test_method in test_methods:
            try:
                test_result = test_method()
                test_name = test_result['test_name']
                all_results['tests'][test_name] = test_result

                if test_result['status'] == 'PASSED':
                    passed_tests += 1
                    logger.info(f"✅ {test_name}: PASSED")
                elif test_result['status'] == 'FAILED':
                    failed_tests += 1
                    logger.error(f"❌ {test_name}: FAILED - {test_result.get('error', 'Unknown error')}")
                elif test_result['status'] == 'SKIPPED':
                    skipped_tests += 1
                    logger.warning(f"⏭️  {test_name}: SKIPPED - {test_result.get('reason', 'Unknown reason')}")

            except Exception as e:
                failed_tests += 1
                logger.error(f"❌ {test_method.__name__}: EXCEPTION - {e}")
                all_results['tests'][test_method.__name__] = {
                    'test_name': test_method.__name__,
                    'status': 'EXCEPTION',
                    'error': str(e)
                }

        end_time = time.time()
        execution_time = end_time - start_time

        # Summary
        all_results['summary'] = {
            'total_tests': len(test_methods),
            'passed': passed_tests,
            'failed': failed_tests,
            'skipped': skipped_tests,
            'execution_time_seconds': execution_time,
            'success_rate': (passed_tests / len(test_methods)) * 100 if len(test_methods) > 0 else 0
        }

        all_results['end_time'] = datetime.now().isoformat()

        # Save results
        results_file = os.path.join(self.output_dir, f'dynamic_weightage_test_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
        import json
        with open(results_file, 'w') as f:
            json.dump(all_results, f, indent=2, default=str)

        # Print summary
        logger.info("=" * 80)
        logger.info("🏁 DYNAMIC WEIGHTAGE REAL DATA TESTING SUMMARY")
        logger.info("=" * 80)
        logger.info(f"Total Tests: {all_results['summary']['total_tests']}")
        logger.info(f"✅ Passed: {all_results['summary']['passed']}")
        logger.info(f"❌ Failed: {all_results['summary']['failed']}")
        logger.info(f"⏭️  Skipped: {all_results['summary']['skipped']}")
        logger.info(f"⏱️  Execution Time: {execution_time:.2f} seconds")
        logger.info(f"📊 Success Rate: {all_results['summary']['success_rate']:.1f}%")
        logger.info(f"📁 Results saved to: {results_file}")
        logger.info("=" * 80)

        return all_results


if __name__ == "__main__":
    """Run the dynamic weightage tests."""
    print("🚀 Dynamic Weightage Real Data Testing")
    print("=" * 50)

    # Initialize test suite
    test_suite = RealDataDynamicWeightageTest()

    # Run all tests
    results = test_suite.run_all_tests()

    # Exit with appropriate code
    if results['summary']['failed'] > 0:
        print("❌ Some tests failed!")
        sys.exit(1)
    elif results['summary']['passed'] == 0:
        print("⚠️  No tests passed!")
        sys.exit(1)
    else:
        print("✅ All tests completed successfully!")
        sys.exit(0)
