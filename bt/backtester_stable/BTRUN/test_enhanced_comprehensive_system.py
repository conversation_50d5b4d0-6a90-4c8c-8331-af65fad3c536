#!/usr/bin/env python3
"""
Enhanced Comprehensive System Test Suite

This script tests the complete enhanced modular trading system including:
- 18 Market regime detection with Excel configuration
- Integration with all 6 strategy types (TBS, TV, OI, ORB, POS, ML_INDICATOR)
- Parallel backtesting capabilities
- Live streaming integration
- Strategy consolidation with regime analysis
- Algobaba integration with regime-aware order management

Usage:
    python3 test_enhanced_comprehensive_system.py [--verbose] [--duration SECONDS]
"""

import sys
import logging
import time
import argparse
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Any
import numpy as np
import pandas as pd
from pathlib import Path

# Import enhanced system components
from backtester_v2.market_regime.enhanced_regime_detector import Enhanced18RegimeDetector, Enhanced18RegimeType
from backtester_v2.market_regime.excel_config_manager import MarketRegimeExcelManager
from backtester_v2.live_streaming.kite_streamer import KiteStreamer
from backtester_v2.strategy_consolidator.base_consolidator import DataDrivenConsolidator
from backtester_v2.algobaba_integration.regime_order_manager import RegimeOrderManager
from backtester_v2.integration.system_orchestrator import EnhancedSystemOrchestrator

logger = logging.getLogger(__name__)

class EnhancedComprehensiveSystemTest:
    """
    Enhanced comprehensive test suite for the complete modular trading system
    """
    
    def __init__(self, test_duration: int = 180, verbose: bool = False):
        """
        Initialize enhanced test suite
        
        Args:
            test_duration (int): Test duration in seconds
            verbose (bool): Enable verbose logging
        """
        self.test_duration = test_duration
        self.verbose = verbose
        
        # Test results tracking
        self.test_results = {
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'test_details': [],
            'start_time': None,
            'end_time': None
        }
        
        # System components
        self.regime_config_manager = None
        self.regime_detector = None
        self.live_streamer = None
        self.strategy_consolidator = None
        self.order_manager = None
        self.system_orchestrator = None
        
        # Test configuration paths
        self.test_config_dir = Path('/tmp/enhanced_system_test')
        self.test_config_dir.mkdir(exist_ok=True)
        
        logger.info("EnhancedComprehensiveSystemTest initialized")
    
    def run_all_tests(self):
        """Run all enhanced comprehensive system tests"""
        try:
            self.test_results['start_time'] = datetime.now()
            logger.info("🚀 Starting Enhanced Comprehensive System Tests")
            logger.info("=" * 80)
            
            # Test 1: Excel Configuration Management
            self._test_excel_configuration_management()
            
            # Test 2: Enhanced 18 Regime Detection with Excel Config
            self._test_enhanced_regime_detection_with_excel()
            
            # Test 3: All Strategy Types Integration
            self._test_all_strategy_types_integration()
            
            # Test 4: Parallel Backtesting System
            self._test_parallel_backtesting_system()
            
            # Test 5: Live Streaming with Regime Integration
            self._test_live_streaming_regime_integration()
            
            # Test 6: Strategy Consolidation with Regime Analysis
            self._test_strategy_consolidation_regime_analysis()
            
            # Test 7: Enhanced Algobaba Integration
            self._test_enhanced_algobaba_integration()
            
            # Test 8: Complete System Orchestration
            self._test_complete_system_orchestration()
            
            # Test 9: Performance and Scalability
            self._test_performance_scalability()
            
            # Test 10: Configuration Validation and Management
            self._test_configuration_validation()
            
            # Test 11: Regime-Based Strategy Optimization
            self._test_regime_based_optimization()
            
            # Test 12: End-to-End Workflow
            self._test_end_to_end_workflow()
            
            self.test_results['end_time'] = datetime.now()
            self._print_enhanced_summary()
            
        except Exception as e:
            logger.error(f"Fatal error in enhanced test suite: {e}")
            self.test_results['end_time'] = datetime.now()
            self._print_enhanced_summary()
            raise
    
    def _test_excel_configuration_management(self):
        """Test 1: Excel Configuration Management"""
        test_name = "Excel Configuration Management"
        logger.info(f"📊 Test 1: {test_name}")
        
        try:
            # Initialize Excel configuration manager
            self.regime_config_manager = MarketRegimeExcelManager()
            
            # Generate Excel template
            template_path = self.test_config_dir / 'market_regime_config.xlsx'
            generated_path = self.regime_config_manager.generate_excel_template(str(template_path))
            
            assert Path(generated_path).exists(), "Excel template not generated"
            
            # Load configuration
            self.regime_config_manager.load_configuration(generated_path)
            
            # Test parameter retrieval
            detection_params = self.regime_config_manager.get_detection_parameters()
            regime_adjustments = self.regime_config_manager.get_regime_adjustments()
            strategy_mappings = self.regime_config_manager.get_strategy_mappings()
            live_config = self.regime_config_manager.get_live_trading_config()
            
            # Validate configuration
            is_valid, errors = self.regime_config_manager.validate_configuration()
            
            assert is_valid, f"Configuration validation failed: {errors}"
            assert len(detection_params) > 0, "No detection parameters loaded"
            assert len(regime_adjustments) > 0, "No regime adjustments loaded"
            assert len(strategy_mappings) > 0, "No strategy mappings loaded"
            
            logger.info(f"   ✓ Excel template generated: {template_path}")
            logger.info(f"   ✓ Configuration loaded: {len(detection_params)} parameters")
            logger.info(f"   ✓ Regime adjustments: {len(regime_adjustments)} regimes")
            logger.info(f"   ✓ Strategy mappings: {len(strategy_mappings)} strategies")
            logger.info(f"   ✓ Configuration validation: {is_valid}")
            
            self._record_test_result(test_name, True, 
                f"Excel configuration management successful: {len(detection_params)} parameters loaded")
            
        except Exception as e:
            self._record_test_result(test_name, False, f"Excel configuration failed: {e}")
    
    def _test_enhanced_regime_detection_with_excel(self):
        """Test 2: Enhanced 18 Regime Detection with Excel Config"""
        test_name = "Enhanced 18 Regime Detection with Excel Config"
        logger.info(f"🧠 Test 2: {test_name}")
        
        try:
            # Get detection parameters from Excel
            if self.regime_config_manager:
                detection_params = self.regime_config_manager.get_detection_parameters()
            else:
                detection_params = {}
            
            # Initialize enhanced regime detector
            self.regime_detector = Enhanced18RegimeDetector(config=detection_params)
            
            # Test regime detection with various scenarios
            regime_types_detected = set()
            confidence_scores = []
            
            for i in range(50):
                mock_data = self._generate_varied_market_data(i)
                regime_result = self.regime_detector.detect_regime(mock_data)
                
                regime_types_detected.add(regime_result['regime_type'])
                confidence_scores.append(regime_result['confidence'])
            
            # Test regime adjustments from Excel
            if self.regime_config_manager:
                for regime_type in regime_types_detected:
                    adjustments = self.regime_config_manager.get_regime_adjustments(regime_type.value)
                    assert isinstance(adjustments, dict), f"Invalid adjustments for {regime_type}"
            
            avg_confidence = np.mean(confidence_scores)
            
            logger.info(f"   ✓ Regime types detected: {len(regime_types_detected)}/18")
            logger.info(f"   ✓ Average confidence: {avg_confidence:.2f}")
            logger.info(f"   ✓ Excel configuration applied successfully")
            
            self._record_test_result(test_name, True, 
                f"Enhanced regime detection successful: {len(regime_types_detected)} regimes, {avg_confidence:.2f} avg confidence")
            
        except Exception as e:
            self._record_test_result(test_name, False, f"Enhanced regime detection failed: {e}")
    
    def _test_all_strategy_types_integration(self):
        """Test 3: All Strategy Types Integration"""
        test_name = "All Strategy Types Integration"
        logger.info(f"📈 Test 3: {test_name}")
        
        try:
            # Initialize system orchestrator
            template_path = self.test_config_dir / 'market_regime_config.xlsx'
            self.system_orchestrator = EnhancedSystemOrchestrator(
                regime_config_path=str(template_path)
            )
            
            # Initialize system
            self.system_orchestrator.initialize_system()
            
            # Test strategy executor availability
            strategy_types = ['TBS', 'TV', 'OI', 'ORB', 'POS', 'ML_INDICATOR']
            available_executors = list(self.system_orchestrator.strategy_executors.keys())
            
            # Test strategy mappings
            if self.regime_config_manager:
                strategy_mappings = self.regime_config_manager.get_strategy_mappings()
                mapped_strategies = list(strategy_mappings.keys())
            else:
                mapped_strategies = []
            
            logger.info(f"   ✓ Strategy executors available: {len(available_executors)}")
            logger.info(f"   ✓ Strategy types: {available_executors}")
            logger.info(f"   ✓ Regime mappings: {len(mapped_strategies)} strategies")
            
            self._record_test_result(test_name, True, 
                f"Strategy integration successful: {len(available_executors)} executors available")
            
        except Exception as e:
            self._record_test_result(test_name, False, f"Strategy integration failed: {e}")
    
    def _test_parallel_backtesting_system(self):
        """Test 4: Parallel Backtesting System"""
        test_name = "Parallel Backtesting System"
        logger.info(f"⚡ Test 4: {test_name}")
        
        try:
            if not self.system_orchestrator:
                raise ValueError("System orchestrator not initialized")
            
            # Create multiple backtest configurations
            backtest_configs = []
            strategy_types = ['TBS', 'TV', 'OI', 'ORB', 'POS', 'ML_INDICATOR']
            
            for i, strategy_type in enumerate(strategy_types):
                config = {
                    'strategy_type': strategy_type,
                    'input_data': {
                        'start_date': '2024-01-01',
                        'end_date': '2024-12-31',
                        'parameters': {'test_param': i}
                    },
                    'test_id': f'test_{strategy_type}_{i}'
                }
                backtest_configs.append(config)
            
            # Run parallel backtests
            start_time = time.time()
            
            # Use asyncio to run parallel backtests
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                results = loop.run_until_complete(
                    self.system_orchestrator.run_parallel_backtests(backtest_configs)
                )
            finally:
                loop.close()
            
            execution_time = time.time() - start_time
            
            # Validate results
            assert isinstance(results, dict), "Invalid results format"
            assert 'total_backtests' in results, "Missing total_backtests"
            assert 'successful_backtests' in results, "Missing successful_backtests"
            
            successful_count = results['successful_backtests']
            total_count = results['total_backtests']
            
            logger.info(f"   ✓ Parallel backtests completed: {successful_count}/{total_count}")
            logger.info(f"   ✓ Execution time: {execution_time:.2f} seconds")
            logger.info(f"   ✓ Average time per backtest: {execution_time/total_count:.2f} seconds")
            
            self._record_test_result(test_name, True, 
                f"Parallel backtesting successful: {successful_count}/{total_count} completed in {execution_time:.1f}s")
            
        except Exception as e:
            self._record_test_result(test_name, False, f"Parallel backtesting failed: {e}")
    
    def _test_live_streaming_regime_integration(self):
        """Test 5: Live Streaming with Regime Integration"""
        test_name = "Live Streaming with Regime Integration"
        logger.info(f"📡 Test 5: {test_name}")
        
        try:
            if not self.regime_detector:
                raise ValueError("Regime detector not initialized")
            
            # Initialize live streamer
            self.live_streamer = KiteStreamer(
                regime_detector=self.regime_detector,
                config={'stream_interval_ms': 50, 'regime_update_freq_sec': 2}
            )
            
            # Start streaming
            self.live_streamer.start_streaming()
            
            # Wait for data processing
            time.sleep(3)
            
            # Check streaming statistics
            stats = self.live_streamer.get_streaming_statistics()
            current_regime = self.live_streamer.get_current_regime()
            regime_history = self.live_streamer.get_regime_history(limit=10)
            
            # Stop streaming
            self.live_streamer.stop_streaming()
            
            logger.info(f"   ✓ Live streaming functional: {stats.get('total_ticks', 0)} ticks")
            logger.info(f"   ✓ Current regime: {current_regime['regime_type'].value if current_regime else 'None'}")
            logger.info(f"   ✓ Regime history: {len(regime_history)} records")
            
            self._record_test_result(test_name, True, 
                f"Live streaming integration successful: {stats.get('total_ticks', 0)} ticks processed")
            
        except Exception as e:
            self._record_test_result(test_name, False, f"Live streaming integration failed: {e}")
    
    def _test_strategy_consolidation_regime_analysis(self):
        """Test 6: Strategy Consolidation with Regime Analysis"""
        test_name = "Strategy Consolidation with Regime Analysis"
        logger.info(f"🔄 Test 6: {test_name}")
        
        try:
            # Initialize strategy consolidator
            self.strategy_consolidator = DataDrivenConsolidator()
            
            # Create mock strategy sources with regime data
            mock_sources = self._create_enhanced_mock_strategy_sources()
            
            # Test consolidation
            consolidated_strategies = self.strategy_consolidator.consolidate_strategies(mock_sources)
            
            # Get consolidation summary
            summary = self.strategy_consolidator.get_consolidation_summary()
            
            logger.info(f"   ✓ Strategy consolidation completed")
            logger.info(f"   ✓ Consolidated strategies: {len(consolidated_strategies)}")
            logger.info(f"   ✓ Summary: {summary}")
            
            self._record_test_result(test_name, True, 
                f"Strategy consolidation successful: {len(consolidated_strategies)} strategies consolidated")
            
        except Exception as e:
            self._record_test_result(test_name, False, f"Strategy consolidation failed: {e}")
    
    def _test_enhanced_algobaba_integration(self):
        """Test 7: Enhanced Algobaba Integration"""
        test_name = "Enhanced Algobaba Integration"
        logger.info(f"🔗 Test 7: {test_name}")
        
        try:
            # Get regime adjustments from Excel config
            if self.regime_config_manager:
                regime_adjustments = self.regime_config_manager.get_regime_adjustments()
                algobaba_config = {'regime_adjustments': regime_adjustments}
            else:
                algobaba_config = {}
            
            # Initialize enhanced order manager
            self.order_manager = RegimeOrderManager(config=algobaba_config)
            
            # Test order execution with multiple regimes
            test_regimes = [
                Enhanced18RegimeType.HIGH_VOLATILE_STRONG_BULLISH,
                Enhanced18RegimeType.NORMAL_VOLATILE_NEUTRAL,
                Enhanced18RegimeType.LOW_VOLATILE_SIDEWAYS
            ]
            
            execution_results = []
            
            for regime_type in test_regimes:
                mock_strategy = type('MockStrategy', (), {'instance_id': f'test_strategy_{regime_type.value}'})()
                
                mock_regime_data = {
                    'regime_type': regime_type,
                    'confidence': 0.8,
                    'timestamp': datetime.now()
                }
                
                mock_order_signal = {
                    'action': 'ENTRY',
                    'quantity': 10,
                    'stop_loss': 100,
                    'take_profit': 200
                }
                
                result = self.order_manager.execute_regime_order(
                    strategy=mock_strategy,
                    regime_data=mock_regime_data,
                    order_signal=mock_order_signal
                )
                
                execution_results.append(result)
            
            # Get performance metrics
            performance = self.order_manager.get_regime_performance()
            
            successful_orders = sum(1 for r in execution_results if r.get('success', False))
            
            logger.info(f"   ✓ Enhanced Algobaba integration functional")
            logger.info(f"   ✓ Orders executed: {successful_orders}/{len(test_regimes)}")
            logger.info(f"   ✓ Regime performance tracking: {len(performance.get('regime_performance', {}))}")
            
            self._record_test_result(test_name, True, 
                f"Enhanced Algobaba integration successful: {successful_orders} orders executed")
            
        except Exception as e:
            self._record_test_result(test_name, False, f"Enhanced Algobaba integration failed: {e}")
    
    def _test_complete_system_orchestration(self):
        """Test 8: Complete System Orchestration"""
        test_name = "Complete System Orchestration"
        logger.info(f"🎯 Test 8: {test_name}")
        
        try:
            if not self.system_orchestrator:
                raise ValueError("System orchestrator not initialized")
            
            # Start system
            self.system_orchestrator.start_system()
            
            # Wait for system to stabilize
            time.sleep(2)
            
            # Get comprehensive system status
            status = self.system_orchestrator.get_system_status()
            
            # Test system capabilities
            mock_sources = self._create_enhanced_mock_strategy_sources()
            consolidated = self.system_orchestrator.consolidate_strategies(mock_sources)
            
            # Test order execution
            mock_order = {
                'strategy_id': 'test_strategy',
                'action': 'ENTRY',
                'quantity': 5
            }
            
            order_result = self.system_orchestrator.execute_strategy_order(mock_order)
            
            # Stop system
            self.system_orchestrator.stop_system()
            
            # Validate system status
            assert status['system_running'], "System not running"
            assert len(consolidated) >= 0, "Strategy consolidation failed"
            
            logger.info(f"   ✓ Complete system orchestration successful")
            logger.info(f"   ✓ System running: {status['system_running']}")
            logger.info(f"   ✓ Strategies consolidated: {len(consolidated)}")
            logger.info(f"   ✓ Order executed: {order_result.get('success', False)}")
            
            self._record_test_result(test_name, True, 
                f"Complete system orchestration successful: All components functional")
            
        except Exception as e:
            self._record_test_result(test_name, False, f"Complete system orchestration failed: {e}")
    
    def _generate_varied_market_data(self, seed: int) -> Dict[str, Any]:
        """Generate varied market data for different scenarios"""
        np.random.seed(seed)
        
        scenarios = ['bullish', 'bearish', 'neutral', 'volatile', 'calm']
        scenario = scenarios[seed % len(scenarios)]
        
        if scenario == 'bullish':
            trend = 1.0
            volatility = 0.1
        elif scenario == 'bearish':
            trend = -1.0
            volatility = 0.1
        elif scenario == 'volatile':
            trend = 0.0
            volatility = 0.3
        elif scenario == 'calm':
            trend = 0.0
            volatility = 0.05
        else:  # neutral
            trend = 0.0
            volatility = 0.15
        
        base_price = 100
        price_data = []
        for i in range(20):
            price = base_price + (trend * i) + np.random.normal(0, volatility * base_price)
            price_data.append(price)
        
        return {
            'price_data': price_data,
            'oi_data': {
                'call_oi': np.random.randint(500000, 2000000),
                'put_oi': np.random.randint(500000, 2000000),
                'call_volume': np.random.randint(20000, 100000),
                'put_volume': np.random.randint(20000, 100000)
            },
            'greek_sentiment': {
                'delta': trend * 0.5 + np.random.normal(0, 0.1),
                'gamma': abs(np.random.normal(0.1, 0.05)),
                'theta': np.random.normal(-0.05, 0.02),
                'vega': volatility + np.random.normal(0, 0.05)
            },
            'technical_indicators': {
                'rsi': 50 + trend * 20 + np.random.normal(0, 10),
                'macd': trend * 0.5 + np.random.normal(0, 0.2),
                'macd_signal': trend * 0.3 + np.random.normal(0, 0.1),
                'ma_signal': trend * 0.2 + np.random.normal(0, 0.1)
            },
            'atr': volatility * base_price,
            'implied_volatility': volatility
        }
    
    def _create_enhanced_mock_strategy_sources(self) -> List[Dict[str, Any]]:
        """Create enhanced mock strategy sources with regime data"""
        sources = []
        
        strategy_types = ['TBS', 'TV', 'OI', 'ORB', 'POS', 'ML_INDICATOR']
        
        for strategy_type in strategy_types:
            # Create mock data with regime performance
            mock_data = pd.DataFrame({
                'date': pd.date_range('2024-01-01', periods=100),
                'pnl': np.random.normal(50, 200, 100),
                'trades': range(100),
                'regime': np.random.choice(['BULLISH', 'BEARISH', 'NEUTRAL'], 100)
            })
            
            sources.append({
                'type': strategy_type,
                'path': f'/tmp/mock_{strategy_type.lower()}_strategy.csv',
                'data': mock_data
            })
        
        return sources
    
    def _record_test_result(self, test_name: str, passed: bool, details: str):
        """Record test result"""
        self.test_results['total_tests'] += 1
        
        if passed:
            self.test_results['passed_tests'] += 1
            status = "✅ PASSED"
        else:
            self.test_results['failed_tests'] += 1
            status = "❌ FAILED"
        
        self.test_results['test_details'].append({
            'test_name': test_name,
            'status': status,
            'passed': passed,
            'details': details,
            'timestamp': datetime.now().isoformat()
        })
        
        logger.info(f"   {status}: {details}")
    
    def _print_enhanced_summary(self):
        """Print enhanced test summary"""
        logger.info("\n" + "=" * 80)
        logger.info("🎯 ENHANCED COMPREHENSIVE SYSTEM TEST SUMMARY")
        logger.info("=" * 80)
        
        # Overall results
        total = self.test_results['total_tests']
        passed = self.test_results['passed_tests']
        failed = self.test_results['failed_tests']
        success_rate = (passed / total * 100) if total > 0 else 0
        
        logger.info(f"Total Tests: {total}")
        logger.info(f"Passed: {passed}")
        logger.info(f"Failed: {failed}")
        logger.info(f"Success Rate: {success_rate:.1f}%")
        
        # Timing
        if self.test_results['start_time'] and self.test_results['end_time']:
            duration = self.test_results['end_time'] - self.test_results['start_time']
            logger.info(f"Duration: {duration.total_seconds():.1f} seconds")
        
        # Detailed results
        logger.info("\nDetailed Results:")
        for test in self.test_results['test_details']:
            logger.info(f"  {test['status']} {test['test_name']}: {test['details']}")
        
        # Final status
        if failed == 0:
            logger.info("\n🎉 ALL TESTS PASSED! Enhanced Comprehensive System is production-ready.")
            logger.info("✅ Excel Configuration Management")
            logger.info("✅ 18-Regime Detection with Excel Config")
            logger.info("✅ All 6 Strategy Types Integration")
            logger.info("✅ Parallel Backtesting System")
            logger.info("✅ Live Streaming with Regime Intelligence")
            logger.info("✅ Strategy Consolidation with Regime Analysis")
            logger.info("✅ Enhanced Algobaba Integration")
            logger.info("✅ Complete System Orchestration")
        else:
            logger.warning(f"\n⚠️  {failed} test(s) failed. Please review and fix issues.")
        
        logger.info("=" * 80)
    
    def cleanup(self):
        """Cleanup test resources"""
        try:
            if self.system_orchestrator:
                self.system_orchestrator.stop_system()
            
            if self.live_streamer:
                self.live_streamer.stop_streaming()
            
            # Clean up test files
            import shutil
            if self.test_config_dir.exists():
                shutil.rmtree(self.test_config_dir)
            
            logger.info("Enhanced test cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

def main():
    """Main test execution"""
    parser = argparse.ArgumentParser(description='Enhanced Comprehensive System Test Suite')
    parser.add_argument('--duration', type=int, default=180, help='Test duration in seconds')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose logging')
    
    args = parser.parse_args()
    
    # Configure logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S'
    )
    
    # Run enhanced tests
    test_suite = EnhancedComprehensiveSystemTest(
        test_duration=args.duration,
        verbose=args.verbose
    )
    
    try:
        test_suite.run_all_tests()
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
    except Exception as e:
        logger.error(f"Enhanced test suite failed: {e}")
    finally:
        test_suite.cleanup()

if __name__ == "__main__":
    main()
