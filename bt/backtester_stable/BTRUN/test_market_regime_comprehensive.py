#!/usr/bin/env python3
"""
Comprehensive Test Suite for Market Regime Detection System

This script tests the complete market regime detection system with:
- Configuration parsing and validation
- Indicator calculation and aggregation
- Regime classification and smoothing
- Performance tracking and adaptive weights
- Golden file format output validation
- Integration with backtester_v2 architecture
- Complete test-validate-analyze-fix cycle
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import date, datetime, timedelta
import logging
from typing import Dict, List, Any, Optional
import time
import json

# Add project root to path
sys.path.insert(0, '/srv/samba/shared/bt/backtester_stable/BTRUN')

# Import market regime modules
from backtester_v2.market_regime.models import (
    RegimeConfig, IndicatorConfig, RegimeType, IndicatorCategory, TimeframeConfig
)
from backtester_v2.market_regime.parser import RegimeConfigParser
from backtester_v2.market_regime.calculator import RegimeCalculator
from backtester_v2.market_regime.classifier import RegimeClassifier
from backtester_v2.market_regime.performance import PerformanceTracker
from backtester_v2.market_regime.processor import RegimeProcessor
from backtester_v2.market_regime.strategy import MarketRegimeStrategy

# Import HeavyDB connection
try:
    from heavydb import connect
    HEAVYDB_AVAILABLE = True
except ImportError:
    HEAVYDB_AVAILABLE = False

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MarketRegimeTestSuite:
    """Comprehensive test suite for market regime detection system."""
    
    def __init__(self):
        """Initialize the test suite."""
        self.base_dir = '/srv/samba/shared/bt/backtester_stable/BTRUN'
        self.output_dir = os.path.join(self.base_dir, 'output', 'market_regime_tests')
        self.config_dir = os.path.join(self.output_dir, 'configs')
        
        # Create output directories
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.config_dir, exist_ok=True)
        
        # HeavyDB connection
        self.db_connection = self._get_heavydb_connection()
        
        # Test results tracking
        self.test_results = {}
        self.test_cycle_count = 0
        
    def _get_heavydb_connection(self):
        """Get HeavyDB connection."""
        if not HEAVYDB_AVAILABLE:
            logger.warning("HeavyDB not available")
            return None
            
        try:
            conn = connect(
                host='127.0.0.1',
                port=6274,
                user='admin',
                password='HyperInteractive',
                dbname='heavyai'
            )
            logger.info("Connected to HeavyDB successfully")
            return conn
        except Exception as e:
            logger.error(f"Failed to connect to HeavyDB: {e}")
            return None
    
    def test_configuration_parsing(self) -> Dict[str, Any]:
        """Test 1: Configuration parsing and validation."""
        logger.info("🔧 Test 1: Configuration Parsing and Validation")
        
        results = {
            'test_name': 'Configuration Parsing and Validation',
            'status': 'RUNNING',
            'details': {}
        }
        
        try:
            # Test 1a: Create configuration template
            parser = RegimeConfigParser()
            template_path = os.path.join(self.config_dir, 'regime_config_template.xlsx')
            parser.create_template(template_path)
            
            template_created = os.path.exists(template_path)
            
            # Test 1b: Parse the template
            if template_created:
                config = parser.parse(template_path)
                
                # Validate configuration
                validation_results = parser.validate_config(config)
                
                results['details']['template_creation'] = {
                    'template_created': template_created,
                    'template_path': template_path,
                    'file_size': os.path.getsize(template_path) if template_created else 0
                }
                
                results['details']['configuration_parsing'] = {
                    'config_loaded': config is not None,
                    'strategy_name': config.strategy_name if config else None,
                    'symbol': config.symbol if config else None,
                    'indicator_count': len(config.indicators) if config else 0,
                    'timeframe_count': len(config.timeframes) if config else 0
                }
                
                results['details']['validation_results'] = validation_results
                
                # Test indicator configurations
                if config and config.indicators:
                    indicator_details = {}
                    for ind in config.indicators:
                        indicator_details[ind.id] = {
                            'name': ind.name,
                            'category': ind.category.value,
                            'base_weight': ind.base_weight,
                            'enabled': ind.enabled,
                            'adaptive': ind.adaptive
                        }
                    results['details']['indicator_configurations'] = indicator_details
                
                # Test passes if config is loaded and has no errors
                test_passed = (
                    config is not None and
                    len(config.indicators) > 0 and
                    len(validation_results.get('errors', [])) == 0
                )
                
                results['status'] = 'PASSED' if test_passed else 'FAILED'
                
            else:
                results['status'] = 'FAILED'
                results['error'] = 'Template creation failed'
            
            if results['status'] == 'PASSED':
                logger.info("  ✅ Configuration parsing test passed")
            else:
                logger.error("  ❌ Configuration parsing test failed")
            
        except Exception as e:
            results['status'] = 'FAILED'
            results['error'] = str(e)
            logger.error(f"  ❌ Configuration parsing test failed: {e}")
        
        return results
    
    def test_regime_calculator(self) -> Dict[str, Any]:
        """Test 2: Regime calculator and indicator aggregation."""
        logger.info("📊 Test 2: Regime Calculator and Indicator Aggregation")
        
        results = {
            'test_name': 'Regime Calculator and Indicator Aggregation',
            'status': 'RUNNING',
            'details': {}
        }
        
        try:
            # Create test configuration
            test_config = self._create_test_config()
            
            # Initialize calculator
            calculator = RegimeCalculator(test_config)
            
            # Generate test market data
            market_data = self._generate_test_market_data()
            
            # Calculate regimes
            regime_classifications = calculator.calculate_regime(market_data)
            
            # Validate results
            results['details']['calculator_initialization'] = {
                'config_loaded': test_config is not None,
                'calculator_created': calculator is not None,
                'indicator_count': len(calculator.indicators),
                'weight_count': len(calculator.indicator_weights)
            }
            
            results['details']['market_data'] = {
                'data_generated': not market_data.empty,
                'data_points': len(market_data),
                'columns': list(market_data.columns),
                'date_range': f"{market_data.index[0]} to {market_data.index[-1]}" if not market_data.empty else None
            }
            
            results['details']['regime_calculation'] = {
                'classifications_generated': len(regime_classifications) > 0,
                'classification_count': len(regime_classifications),
                'unique_regimes': len(set(c.regime_type for c in regime_classifications)) if regime_classifications else 0,
                'avg_confidence': np.mean([c.confidence for c in regime_classifications]) if regime_classifications else 0.0
            }
            
            # Test individual classifications
            if regime_classifications:
                sample_classification = regime_classifications[0]
                results['details']['sample_classification'] = {
                    'timestamp': sample_classification.timestamp.isoformat(),
                    'regime_type': sample_classification.regime_type.value,
                    'regime_score': sample_classification.regime_score,
                    'confidence': sample_classification.confidence,
                    'component_count': len(sample_classification.component_scores)
                }
            
            # Test passes if classifications are generated with reasonable confidence
            test_passed = (
                len(regime_classifications) > 0 and
                all(0.0 <= c.confidence <= 1.0 for c in regime_classifications) and
                all(-2.0 <= c.regime_score <= 2.0 for c in regime_classifications)
            )
            
            results['status'] = 'PASSED' if test_passed else 'FAILED'
            
            if results['status'] == 'PASSED':
                logger.info("  ✅ Regime calculator test passed")
            else:
                logger.error("  ❌ Regime calculator test failed")
            
        except Exception as e:
            results['status'] = 'FAILED'
            results['error'] = str(e)
            logger.error(f"  ❌ Regime calculator test failed: {e}")
        
        return results
    
    def test_regime_classifier(self) -> Dict[str, Any]:
        """Test 3: Regime classifier and smoothing."""
        logger.info("🎯 Test 3: Regime Classifier and Smoothing")
        
        results = {
            'test_name': 'Regime Classifier and Smoothing',
            'status': 'RUNNING',
            'details': {}
        }
        
        try:
            # Create test configuration
            test_config = self._create_test_config()
            
            # Initialize classifier
            classifier = RegimeClassifier(test_config)
            
            # Generate test aggregated signals
            aggregated_signals = self._generate_test_aggregated_signals()
            
            # Classify regimes
            classifications = classifier.classify(aggregated_signals)
            
            # Test classification results
            results['details']['classifier_initialization'] = {
                'config_loaded': test_config is not None,
                'classifier_created': classifier is not None,
                'thresholds_set': len(classifier.thresholds) > 0
            }
            
            results['details']['signal_data'] = {
                'signals_generated': not aggregated_signals.empty,
                'signal_points': len(aggregated_signals),
                'signal_columns': list(aggregated_signals.columns)
            }
            
            results['details']['classification_results'] = {
                'classifications_generated': len(classifications) > 0,
                'classification_count': len(classifications),
                'regime_types': list(set(c.regime_type.value for c in classifications)) if classifications else [],
                'avg_confidence': np.mean([c.confidence for c in classifications]) if classifications else 0.0
            }
            
            # Test regime type distribution
            if classifications:
                regime_distribution = {}
                for c in classifications:
                    regime = c.regime_type.value
                    regime_distribution[regime] = regime_distribution.get(regime, 0) + 1
                
                results['details']['regime_distribution'] = regime_distribution
            
            # Test smoothing functionality
            if len(classifications) >= test_config.regime_smoothing:
                # Check for smoothing effects
                regime_changes = 0
                for i in range(1, len(classifications)):
                    if classifications[i].regime_type != classifications[i-1].regime_type:
                        regime_changes += 1
                
                results['details']['smoothing_analysis'] = {
                    'regime_changes': regime_changes,
                    'change_rate': regime_changes / len(classifications) if classifications else 0.0,
                    'smoothing_window': test_config.regime_smoothing
                }
            
            # Test passes if classifications are reasonable
            test_passed = (
                len(classifications) > 0 and
                len(set(c.regime_type for c in classifications)) >= 1 and
                all(c.confidence >= 0.0 for c in classifications)
            )
            
            results['status'] = 'PASSED' if test_passed else 'FAILED'
            
            if results['status'] == 'PASSED':
                logger.info("  ✅ Regime classifier test passed")
            else:
                logger.error("  ❌ Regime classifier test failed")
            
        except Exception as e:
            results['status'] = 'FAILED'
            results['error'] = str(e)
            logger.error(f"  ❌ Regime classifier test failed: {e}")
        
        return results
    
    def test_performance_tracking(self) -> Dict[str, Any]:
        """Test 4: Performance tracking and adaptive weights."""
        logger.info("📈 Test 4: Performance Tracking and Adaptive Weights")
        
        results = {
            'test_name': 'Performance Tracking and Adaptive Weights',
            'status': 'RUNNING',
            'details': {}
        }
        
        try:
            # Create test configuration
            test_config = self._create_test_config()
            
            # Initialize performance tracker
            performance_tracker = PerformanceTracker(test_config)
            
            # Generate test data
            regime_results = self._generate_test_regime_results()
            market_data = self._generate_test_market_data()
            
            # Update performance
            performance_tracker.update_performance(regime_results, market_data)
            
            # Get performance summary
            performance_summary = performance_tracker.get_performance_summary()
            
            # Get current weights
            current_weights = performance_tracker.get_current_weights()
            
            results['details']['tracker_initialization'] = {
                'config_loaded': test_config is not None,
                'tracker_created': performance_tracker is not None
            }
            
            results['details']['test_data'] = {
                'regime_results_generated': not regime_results.empty,
                'market_data_generated': not market_data.empty,
                'regime_data_points': len(regime_results),
                'market_data_points': len(market_data)
            }
            
            results['details']['performance_tracking'] = {
                'performance_summary_generated': len(performance_summary) > 0,
                'indicators_tracked': list(performance_summary.keys()),
                'current_weights_available': len(current_weights) > 0,
                'weight_indicators': list(current_weights.keys())
            }
            
            # Test individual performance metrics
            if performance_summary:
                sample_indicator = list(performance_summary.keys())[0]
                sample_metrics = performance_summary[sample_indicator]
                
                results['details']['sample_performance_metrics'] = {
                    'indicator_id': sample_indicator,
                    'hit_rate': sample_metrics.get('hit_rate', 0.0),
                    'sharpe_ratio': sample_metrics.get('sharpe_ratio', 0.0),
                    'performance_score': sample_metrics.get('performance_score', 0.0),
                    'current_weight': sample_metrics.get('current_weight', 0.0)
                }
            
            # Test passes if performance tracking is working
            test_passed = (
                len(performance_summary) > 0 and
                all(0.0 <= metrics.get('hit_rate', 0.0) <= 1.0 for metrics in performance_summary.values()) and
                all(0.0 <= metrics.get('performance_score', 0.0) <= 1.0 for metrics in performance_summary.values())
            )
            
            results['status'] = 'PASSED' if test_passed else 'FAILED'
            
            if results['status'] == 'PASSED':
                logger.info("  ✅ Performance tracking test passed")
            else:
                logger.error("  ❌ Performance tracking test failed")
            
        except Exception as e:
            results['status'] = 'FAILED'
            results['error'] = str(e)
            logger.error(f"  ❌ Performance tracking test failed: {e}")
        
        return results

    def test_regime_processor_integration(self) -> Dict[str, Any]:
        """Test 5: Complete regime processor integration."""
        logger.info("🔗 Test 5: Complete Regime Processor Integration")

        results = {
            'test_name': 'Complete Regime Processor Integration',
            'status': 'RUNNING',
            'details': {}
        }

        try:
            # Create test configuration
            test_config = self._create_test_config()

            # Initialize processor
            processor = RegimeProcessor(self.db_connection, regime_config=test_config)

            # Process regime analysis
            start_date = '2025-01-01'
            end_date = '2025-01-02'

            analysis_results = processor.process_regime_analysis(start_date, end_date)

            # Validate results structure
            expected_keys = ['processing_summary', 'regime_classifications', 'regime_summary',
                           'performance_metrics', 'alerts', 'golden_file_data']

            results['details']['processor_initialization'] = {
                'config_loaded': test_config is not None,
                'processor_created': processor is not None,
                'db_connection_available': self.db_connection is not None
            }

            results['details']['analysis_execution'] = {
                'analysis_completed': len(analysis_results) > 0,
                'expected_keys_present': all(key in analysis_results for key in expected_keys),
                'processing_summary': analysis_results.get('processing_summary', {}),
                'classification_count': len(analysis_results.get('regime_classifications', [])),
                'alert_count': len(analysis_results.get('alerts', []))
            }

            # Test golden file data
            golden_data = analysis_results.get('golden_file_data', {})
            results['details']['golden_file_data'] = {
                'golden_data_generated': len(golden_data) > 0,
                'sheets_generated': list(golden_data.keys()),
                'portfolio_params': len(golden_data.get('PortfolioParameter', pd.DataFrame())),
                'general_params': len(golden_data.get('GeneralParameter', pd.DataFrame())),
                'indicator_params': len(golden_data.get('IndicatorParameter', pd.DataFrame()))
            }

            # Test current regime functionality
            current_regime = processor.get_current_regime()
            results['details']['current_regime'] = {
                'current_regime_available': current_regime is not None,
                'regime_info': current_regime if current_regime else {}
            }

            # Test passes if all components work together
            test_passed = (
                len(analysis_results) > 0 and
                all(key in analysis_results for key in expected_keys) and
                len(golden_data) > 0
            )

            results['status'] = 'PASSED' if test_passed else 'FAILED'

            if results['status'] == 'PASSED':
                logger.info("  ✅ Regime processor integration test passed")
            else:
                logger.error("  ❌ Regime processor integration test failed")

        except Exception as e:
            results['status'] = 'FAILED'
            results['error'] = str(e)
            logger.error(f"  ❌ Regime processor integration test failed: {e}")

        return results

    def test_golden_file_output(self) -> Dict[str, Any]:
        """Test 6: Golden file format output generation."""
        logger.info("📄 Test 6: Golden File Format Output Generation")

        results = {
            'test_name': 'Golden File Format Output Generation',
            'status': 'RUNNING',
            'details': {}
        }

        try:
            # Create test configuration
            test_config = self._create_test_config()

            # Initialize processor
            processor = RegimeProcessor(self.db_connection, regime_config=test_config)

            # Process regime analysis
            start_date = '2025-01-01'
            end_date = '2025-01-02'

            analysis_results = processor.process_regime_analysis(start_date, end_date)
            golden_data = analysis_results.get('golden_file_data', {})

            # Generate golden file
            golden_file_path = os.path.join(self.output_dir, 'Market_Regime_Golden_Output.xlsx')

            if golden_data:
                with pd.ExcelWriter(golden_file_path, engine='openpyxl') as writer:
                    for sheet_name, df in golden_data.items():
                        df.to_excel(writer, sheet_name=sheet_name, index=False)

                golden_file_created = os.path.exists(golden_file_path)
                file_size = os.path.getsize(golden_file_path) if golden_file_created else 0
            else:
                golden_file_created = False
                file_size = 0

            # Validate golden file structure
            expected_sheets = ['PortfolioParameter', 'GeneralParameter', 'IndicatorParameter']
            sheets_present = all(sheet in golden_data for sheet in expected_sheets)

            results['details']['golden_file_generation'] = {
                'golden_data_available': len(golden_data) > 0,
                'expected_sheets': expected_sheets,
                'sheets_generated': list(golden_data.keys()),
                'all_expected_sheets_present': sheets_present,
                'golden_file_created': golden_file_created,
                'golden_file_path': golden_file_path,
                'file_size_bytes': file_size
            }

            # Test individual sheet contents
            if golden_data:
                sheet_details = {}
                for sheet_name, df in golden_data.items():
                    sheet_details[sheet_name] = {
                        'row_count': len(df),
                        'column_count': len(df.columns),
                        'columns': list(df.columns)
                    }
                results['details']['sheet_details'] = sheet_details

            # Test passes if golden file is created with expected structure
            test_passed = (
                golden_file_created and
                file_size > 0 and
                sheets_present
            )

            results['status'] = 'PASSED' if test_passed else 'FAILED'

            if results['status'] == 'PASSED':
                logger.info(f"  ✅ Golden file output test passed: {golden_file_path}")
            else:
                logger.error("  ❌ Golden file output test failed")

        except Exception as e:
            results['status'] = 'FAILED'
            results['error'] = str(e)
            logger.error(f"  ❌ Golden file output test failed: {e}")

        return results

    def _create_test_config(self) -> RegimeConfig:
        """Create test configuration"""
        # Create test indicators
        test_indicators = [
            IndicatorConfig(
                id='test_ema',
                name='Test EMA',
                category=IndicatorCategory.PRICE_TREND,
                indicator_type='ema',
                base_weight=0.3,
                parameters={'periods': [5, 10]}
            ),
            IndicatorConfig(
                id='test_vwap',
                name='Test VWAP',
                category=IndicatorCategory.PRICE_TREND,
                indicator_type='vwap',
                base_weight=0.25
            ),
            IndicatorConfig(
                id='test_greek',
                name='Test Greek',
                category=IndicatorCategory.GREEK_SENTIMENT,
                indicator_type='greek',
                base_weight=0.25
            ),
            IndicatorConfig(
                id='test_iv',
                name='Test IV',
                category=IndicatorCategory.IV_ANALYSIS,
                indicator_type='iv_skew',
                base_weight=0.2
            )
        ]

        # Create test timeframes
        test_timeframes = [
            TimeframeConfig(timeframe_minutes=1, weight=0.2),
            TimeframeConfig(timeframe_minutes=5, weight=0.3),
            TimeframeConfig(timeframe_minutes=15, weight=0.5)
        ]

        return RegimeConfig(
            strategy_name='TestMarketRegime',
            symbol='NIFTY',
            indicators=test_indicators,
            timeframes=test_timeframes,
            lookback_days=30,
            confidence_threshold=0.5,
            regime_smoothing=3,
            performance_window=50,
            learning_rate=0.02
        )

    def _generate_test_market_data(self) -> pd.DataFrame:
        """Generate test market data"""
        # Generate 2 days of minute data
        date_range = pd.date_range(start='2025-01-01 09:15:00', end='2025-01-02 15:30:00', freq='1min')

        np.random.seed(42)
        n_points = len(date_range)

        # Generate realistic price data
        base_price = 23000
        returns = np.random.normal(0, 0.001, n_points)  # 0.1% volatility
        prices = base_price * np.exp(np.cumsum(returns))

        market_data = pd.DataFrame({
            'open': prices + np.random.normal(0, 5, n_points),
            'high': prices + np.abs(np.random.normal(0, 10, n_points)),
            'low': prices - np.abs(np.random.normal(0, 10, n_points)),
            'close': prices,
            'volume': np.random.randint(1000, 10000, n_points),
            'atm_ce_premium': np.random.uniform(100, 300, n_points),
            'atm_pe_premium': np.random.uniform(100, 300, n_points),
            'atm_straddle_premium': np.random.uniform(200, 600, n_points),
            'atm_total_oi': np.random.randint(100000, 1000000, n_points)
        }, index=date_range)

        return market_data

    def _generate_test_aggregated_signals(self) -> pd.DataFrame:
        """Generate test aggregated signals"""
        date_range = pd.date_range(start='2025-01-01 09:15:00', end='2025-01-01 15:30:00', freq='5min')

        np.random.seed(42)
        n_points = len(date_range)

        # Generate realistic signals
        signals = pd.DataFrame({
            'weighted_signal': np.random.normal(0, 0.5, n_points),
            'confidence': np.random.uniform(0.5, 0.9, n_points),
            'total_weight': np.random.uniform(0.8, 1.2, n_points),
            'test_ema_signal': np.random.normal(0, 0.3, n_points),
            'test_vwap_signal': np.random.normal(0, 0.3, n_points),
            'test_greek_signal': np.random.normal(0, 0.3, n_points),
            'test_iv_signal': np.random.normal(0, 0.3, n_points),
            'test_ema_confidence': np.random.uniform(0.6, 0.9, n_points),
            'test_vwap_confidence': np.random.uniform(0.6, 0.9, n_points),
            'test_greek_confidence': np.random.uniform(0.6, 0.9, n_points),
            'test_iv_confidence': np.random.uniform(0.6, 0.9, n_points)
        }, index=date_range)

        return signals

    def _generate_test_regime_results(self) -> pd.DataFrame:
        """Generate test regime results"""
        date_range = pd.date_range(start='2025-01-01 09:15:00', end='2025-01-01 15:30:00', freq='5min')

        np.random.seed(42)
        n_points = len(date_range)

        # Generate regime classifications
        regime_types = ['STRONG_BULLISH', 'MODERATE_BULLISH', 'NEUTRAL', 'WEAK_BEARISH', 'MODERATE_BEARISH']

        regime_results = pd.DataFrame({
            'regime_type': np.random.choice(regime_types, n_points),
            'regime_score': np.random.uniform(-1.5, 1.5, n_points),
            'confidence': np.random.uniform(0.5, 0.9, n_points),
            'test_ema_signal': np.random.normal(0, 0.3, n_points),
            'test_vwap_signal': np.random.normal(0, 0.3, n_points),
            'test_greek_signal': np.random.normal(0, 0.3, n_points),
            'test_iv_signal': np.random.normal(0, 0.3, n_points)
        }, index=date_range)

        return regime_results

    def run_test_cycle(self) -> Dict[str, Any]:
        """Run complete test cycle: Test → Validate → Analyze → Fix → Retest."""
        self.test_cycle_count += 1
        logger.info(f"🔄 Starting Market Regime Test Cycle #{self.test_cycle_count}")
        logger.info("=" * 80)

        start_time = time.time()

        # Define test methods
        test_methods = [
            self.test_configuration_parsing,
            self.test_regime_calculator,
            self.test_regime_classifier,
            self.test_performance_tracking,
            self.test_regime_processor_integration,
            self.test_golden_file_output
        ]

        cycle_results = {
            'cycle_number': self.test_cycle_count,
            'start_time': datetime.now().isoformat(),
            'tests': {},
            'summary': {}
        }

        passed_tests = 0
        failed_tests = 0
        skipped_tests = 0

        # Execute tests
        for test_method in test_methods:
            try:
                test_result = test_method()
                test_name = test_result['test_name']
                cycle_results['tests'][test_name] = test_result

                if test_result['status'] == 'PASSED':
                    passed_tests += 1
                    logger.info(f"✅ {test_name}: PASSED")
                elif test_result['status'] == 'FAILED':
                    failed_tests += 1
                    logger.error(f"❌ {test_name}: FAILED - {test_result.get('error', 'Unknown error')}")
                elif test_result['status'] == 'SKIPPED':
                    skipped_tests += 1
                    logger.warning(f"⏭️  {test_name}: SKIPPED - {test_result.get('reason', 'Unknown reason')}")

            except Exception as e:
                failed_tests += 1
                logger.error(f"❌ {test_method.__name__}: EXCEPTION - {e}")
                cycle_results['tests'][test_method.__name__] = {
                    'test_name': test_method.__name__,
                    'status': 'EXCEPTION',
                    'error': str(e)
                }

        end_time = time.time()
        execution_time = end_time - start_time

        # Calculate summary
        total_tests = len(test_methods)
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0

        cycle_results['summary'] = {
            'total_tests': total_tests,
            'passed': passed_tests,
            'failed': failed_tests,
            'skipped': skipped_tests,
            'execution_time_seconds': execution_time,
            'success_rate': success_rate,
            'cycle_complete': failed_tests == 0
        }

        cycle_results['end_time'] = datetime.now().isoformat()

        # Save cycle results
        cycle_file = os.path.join(self.output_dir, f'market_regime_test_cycle_{self.test_cycle_count}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
        with open(cycle_file, 'w') as f:
            json.dump(cycle_results, f, indent=2, default=str)

        # Print cycle summary
        logger.info("=" * 80)
        logger.info(f"🏁 MARKET REGIME TEST CYCLE #{self.test_cycle_count} SUMMARY")
        logger.info("=" * 80)
        logger.info(f"📊 Total Tests: {total_tests}")
        logger.info(f"✅ Passed: {passed_tests}")
        logger.info(f"❌ Failed: {failed_tests}")
        logger.info(f"⏭️  Skipped: {skipped_tests}")
        logger.info(f"⏱️  Execution Time: {execution_time:.2f} seconds")
        logger.info(f"📈 Success Rate: {success_rate:.1f}%")
        logger.info(f"🔄 Cycle Complete: {'✅ YES' if cycle_results['summary']['cycle_complete'] else '❌ NO'}")
        logger.info(f"📁 Results saved to: {cycle_file}")
        logger.info("=" * 80)

        return cycle_results

    def run_complete_testing_cycle(self) -> Dict[str, Any]:
        """Run complete testing cycle until all tests pass or max cycles reached."""
        logger.info("🚀 Starting Complete Market Regime Testing Cycle")
        logger.info("Testing: Configuration + Calculator + Classifier + Performance + Integration + Golden Output")
        logger.info("=" * 100)

        max_cycles = 3
        all_cycles = []

        for cycle in range(1, max_cycles + 1):
            cycle_result = self.run_test_cycle()
            all_cycles.append(cycle_result)

            if cycle_result['summary']['cycle_complete']:
                logger.info(f"🎉 ALL TESTS PASSED in cycle {cycle}!")
                break
            elif cycle < max_cycles:
                logger.info(f"🔄 Cycle {cycle} incomplete. Running next cycle...")
                time.sleep(1)  # Brief pause between cycles
            else:
                logger.warning(f"⚠️  Maximum cycles ({max_cycles}) reached. Some tests may still be failing.")

        # Final summary
        final_summary = {
            'test_suite': 'Market Regime Comprehensive Tests',
            'total_cycles': len(all_cycles),
            'max_cycles': max_cycles,
            'final_success': all_cycles[-1]['summary']['cycle_complete'] if all_cycles else False,
            'cycles': all_cycles,
            'golden_file_generated': os.path.exists(os.path.join(self.output_dir, 'Market_Regime_Golden_Output.xlsx')),
            'config_template_generated': os.path.exists(os.path.join(self.config_dir, 'regime_config_template.xlsx'))
        }

        # Save final summary
        final_file = os.path.join(self.output_dir, f'market_regime_final_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
        with open(final_file, 'w') as f:
            json.dump(final_summary, f, indent=2, default=str)

        logger.info("🏆 FINAL MARKET REGIME TEST SUMMARY")
        logger.info("=" * 100)
        logger.info(f"📊 Total Cycles Run: {final_summary['total_cycles']}")
        logger.info(f"🎯 Final Success: {'✅ YES' if final_summary['final_success'] else '❌ NO'}")
        logger.info(f"📄 Golden File Generated: {'✅ YES' if final_summary['golden_file_generated'] else '❌ NO'}")
        logger.info(f"📋 Config Template Generated: {'✅ YES' if final_summary['config_template_generated'] else '❌ NO'}")
        logger.info(f"📁 Final Results: {final_file}")
        logger.info("=" * 100)

        return final_summary


if __name__ == "__main__":
    """Run the complete market regime test suite."""
    print("🎯 Market Regime Comprehensive Test Suite")
    print("Testing: Configuration + Calculator + Classifier + Performance + Integration + Golden Output")
    print("=" * 80)

    # Initialize test suite
    test_suite = MarketRegimeTestSuite()

    # Run complete testing cycle
    results = test_suite.run_complete_testing_cycle()

    # Exit with appropriate code
    if results['final_success']:
        print("✅ All market regime tests completed successfully!")
        sys.exit(0)
    else:
        print("❌ Some market regime tests failed!")
        sys.exit(1)
