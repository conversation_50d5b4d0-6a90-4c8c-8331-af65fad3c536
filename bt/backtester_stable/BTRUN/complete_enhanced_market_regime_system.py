#!/usr/bin/env python3
"""
Complete Enhanced Market Regime Formation System

This script provides the complete implementation of the highly configurable
market regime formation system with:
- Comprehensive indicator configuration
- Dynamic weightage systems with historical performance
- Custom regime definitions and thresholds
- Individual user regime configurations
- Time-series regime storage and analysis
- Strategy consolidation and optimization

Usage:
    python3 complete_enhanced_market_regime_system.py [--mode MODE] [--config CONFIG_PATH]
"""

import sys
import logging
import argparse
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any
import json

# Import enhanced market regime components
from backtester_v2.market_regime.enhanced_configurable_excel_manager import EnhancedConfigurableExcelManager
from backtester_v2.market_regime.enhanced_regime_formation_engine import EnhancedRegimeFormationEngine
from backtester_v2.market_regime.time_series_regime_storage import TimeSeriesRegimeStorage

logger = logging.getLogger(__name__)

class CompleteEnhancedMarketRegimeSystem:
    """
    Complete Enhanced Market Regime Formation System
    
    This class provides the complete implementation of a highly configurable
    market regime formation system designed for regime analysis rather than
    direct trading, with comprehensive configuration management and individual
    user customization capabilities.
    """
    
    def __init__(self, config_path: str = None, storage_path: str = None):
        """
        Initialize Complete Enhanced Market Regime System
        
        Args:
            config_path (str, optional): Path to Excel configuration file
            storage_path (str, optional): Path to time-series storage database
        """
        self.config_path = config_path
        self.storage_path = storage_path or "enhanced_regime_formation.db"
        
        # Core components
        self.config_manager = None
        self.formation_engine = None
        self.storage = None
        
        # System state
        self.system_initialized = False
        self.current_mode = None
        self.active_users = {}
        self.regime_history = []
        
        logger.info("CompleteEnhancedMarketRegimeSystem initialized")
    
    def initialize_system(self, mode: str = 'formation') -> bool:
        """
        Initialize the complete enhanced market regime system
        
        Args:
            mode (str): System mode ('formation', 'analysis', 'template', 'optimization')
            
        Returns:
            bool: True if initialization successful
        """
        try:
            logger.info(f"🚀 Initializing Enhanced Market Regime Formation System in {mode} mode")
            
            self.current_mode = mode
            
            # Initialize core components
            self._initialize_core_components()
            
            # Mode-specific initialization
            if mode == 'formation':
                self._initialize_formation_mode()
            elif mode == 'analysis':
                self._initialize_analysis_mode()
            elif mode == 'template':
                self._initialize_template_mode()
            elif mode == 'optimization':
                self._initialize_optimization_mode()
            else:
                raise ValueError(f"Unknown mode: {mode}")
            
            self.system_initialized = True
            logger.info(f"✅ Enhanced Market Regime System initialized in {mode} mode")
            
            return True
            
        except Exception as e:
            logger.error(f"Error initializing system: {e}")
            return False
    
    def _initialize_core_components(self):
        """Initialize core system components"""
        try:
            # Initialize enhanced configuration manager
            self.config_manager = EnhancedConfigurableExcelManager(self.config_path)
            logger.info("✅ Enhanced configuration manager initialized")
            
            # Initialize time-series storage
            self.storage = TimeSeriesRegimeStorage(self.storage_path)
            logger.info("✅ Time-series storage initialized")
            
            # Initialize formation engine
            self.formation_engine = EnhancedRegimeFormationEngine(
                config_path=self.config_path,
                storage_path=self.storage_path
            )
            logger.info("✅ Enhanced regime formation engine initialized")
            
        except Exception as e:
            logger.error(f"Error initializing core components: {e}")
            raise
    
    def _initialize_formation_mode(self):
        """Initialize components for regime formation mode"""
        try:
            # Load and validate configuration
            if self.config_path and Path(self.config_path).exists():
                is_valid, errors = self.config_manager.validate_configuration()
                if not is_valid:
                    logger.warning(f"Configuration validation warnings: {errors}")
            
            logger.info("✅ Formation mode initialized")
            
        except Exception as e:
            logger.error(f"Error initializing formation mode: {e}")
            raise
    
    def _initialize_analysis_mode(self):
        """Initialize components for analysis mode"""
        try:
            # Analysis mode focuses on historical data analysis
            logger.info("✅ Analysis mode initialized")
            
        except Exception as e:
            logger.error(f"Error initializing analysis mode: {e}")
            raise
    
    def _initialize_template_mode(self):
        """Initialize components for template generation mode"""
        try:
            # Template mode for generating configuration templates
            logger.info("✅ Template mode initialized")
            
        except Exception as e:
            logger.error(f"Error initializing template mode: {e}")
            raise
    
    def _initialize_optimization_mode(self):
        """Initialize components for optimization mode"""
        try:
            # Optimization mode for performance-based weight adjustment
            logger.info("✅ Optimization mode initialized")
            
        except Exception as e:
            logger.error(f"Error initializing optimization mode: {e}")
            raise
    
    def generate_enhanced_templates(self, output_dir: str) -> Dict[str, str]:
        """
        Generate enhanced Excel templates for market regime configuration
        
        Args:
            output_dir (str): Output directory for templates
            
        Returns:
            Dict: Generated template paths
        """
        try:
            logger.info(f"📊 Generating enhanced market regime templates in: {output_dir}")
            
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            generated_templates = {}
            
            # Generate comprehensive configuration template
            comprehensive_template_path = output_path / 'enhanced_market_regime_config.xlsx'
            template_path = self.config_manager.generate_enhanced_excel_template(
                str(comprehensive_template_path)
            )
            generated_templates['comprehensive'] = template_path
            
            # Generate user-specific templates for different profiles
            user_templates = {
                'conservative': {
                    'regime_preferences': 'LOW_VOLATILITY_PREFERRED',
                    'risk_tolerance': 'LOW',
                    'custom_weights': {'VOLATILITY_MEASURES': 0.4, 'TECHNICAL_INDICATORS': 0.3, 'PRICE_ACTION': 0.3}
                },
                'aggressive': {
                    'regime_preferences': 'HIGH_VOLATILITY_PREFERRED',
                    'risk_tolerance': 'HIGH',
                    'custom_weights': {'MOMENTUM_INDICATORS': 0.5, 'VOLATILITY_MEASURES': 0.3, 'GREEK_SENTIMENT': 0.2}
                },
                'balanced': {
                    'regime_preferences': 'ALL_REGIMES',
                    'risk_tolerance': 'MEDIUM',
                    'custom_weights': {}  # Equal weights
                }
            }
            
            for profile_name, profile_config in user_templates.items():
                profile_template_path = output_path / f'regime_config_{profile_name}_profile.xlsx'
                
                # Create customized template
                profile_manager = EnhancedConfigurableExcelManager()
                template_path = profile_manager.generate_enhanced_excel_template(
                    str(profile_template_path)
                )
                
                # Customize for profile
                profile_manager.load_configuration()
                # Add profile-specific customizations here
                profile_manager.save_configuration(str(profile_template_path))
                
                generated_templates[f'{profile_name}_profile'] = str(profile_template_path)
            
            # Generate documentation template
            doc_template_path = output_path / 'regime_configuration_guide.md'
            self._generate_configuration_documentation(str(doc_template_path))
            generated_templates['documentation'] = str(doc_template_path)
            
            logger.info(f"✅ Generated {len(generated_templates)} enhanced templates")
            
            return generated_templates
            
        except Exception as e:
            logger.error(f"Error generating enhanced templates: {e}")
            return {'error': str(e)}
    
    def form_market_regime(self, market_data: Dict[str, Any], 
                          user_id: str = 'DEFAULT',
                          configuration_id: str = 'DEFAULT') -> Dict[str, Any]:
        """
        Form market regime based on current market data and user configuration
        
        Args:
            market_data (Dict): Current market data including all indicators
            user_id (str): User ID for personalized regime formation
            configuration_id (str): Specific configuration to use
            
        Returns:
            Dict: Complete regime formation result
        """
        try:
            if self.current_mode != 'formation':
                raise ValueError("System not in formation mode")
            
            logger.info(f"🎯 Forming market regime for user: {user_id}")
            
            # Form regime using enhanced engine
            formation_result = self.formation_engine.form_market_regime(
                market_data=market_data,
                user_id=user_id,
                configuration_id=configuration_id
            )
            
            # Track active user
            if user_id not in self.active_users:
                self.active_users[user_id] = {
                    'first_regime_time': formation_result.timestamp,
                    'regime_count': 0,
                    'configurations_used': set()
                }
            
            self.active_users[user_id]['regime_count'] += 1
            self.active_users[user_id]['configurations_used'].add(configuration_id)
            self.active_users[user_id]['last_regime_time'] = formation_result.timestamp
            
            # Add to regime history
            self.regime_history.append({
                'timestamp': formation_result.timestamp,
                'user_id': user_id,
                'regime_id': formation_result.regime_id,
                'regime_name': formation_result.regime_name,
                'confidence_score': formation_result.confidence_score
            })
            
            # Keep only recent history
            if len(self.regime_history) > 1000:
                self.regime_history = self.regime_history[-1000:]
            
            result = {
                'regime_id': formation_result.regime_id,
                'regime_name': formation_result.regime_name,
                'regime_type': formation_result.regime_type,
                'confidence_score': formation_result.confidence_score,
                'directional_component': formation_result.directional_component,
                'volatility_component': formation_result.volatility_component,
                'signal_strength': formation_result.signal_strength,
                'market_condition': formation_result.market_condition,
                'timestamp': formation_result.timestamp.isoformat(),
                'user_id': user_id,
                'configuration_id': configuration_id,
                'indicator_contributions': formation_result.indicator_contributions,
                'metadata': formation_result.metadata
            }
            
            logger.info(f"✅ Regime formed: {formation_result.regime_name} (confidence: {formation_result.confidence_score:.2f})")
            
            return result
            
        except Exception as e:
            logger.error(f"Error forming market regime: {e}")
            return {'error': str(e)}
    
    def analyze_regime_performance(self, user_id: str = None, 
                                 days: int = 30) -> Dict[str, Any]:
        """
        Analyze regime formation performance
        
        Args:
            user_id (str, optional): User ID to analyze
            days (int): Analysis window in days
            
        Returns:
            Dict: Performance analysis results
        """
        try:
            if self.current_mode != 'analysis':
                logger.warning("System not in analysis mode, switching temporarily")
            
            logger.info(f"📈 Analyzing regime performance for user: {user_id}, window: {days} days")
            
            # Get regime history from storage
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            regime_history = self.storage.get_regime_history(
                start_date=start_date,
                end_date=end_date,
                user_id=user_id,
                limit=10000
            )
            
            if regime_history.empty:
                return {'error': 'No regime history found for analysis'}
            
            # Calculate comprehensive performance metrics
            analysis = {
                'analysis_period': f"{start_date.date()} to {end_date.date()}",
                'user_id': user_id or 'ALL_USERS',
                'total_regime_classifications': len(regime_history),
                'unique_regimes_detected': regime_history['regime_id'].nunique(),
                'average_confidence': regime_history['confidence_score'].mean(),
                'confidence_distribution': {
                    'high_confidence': len(regime_history[regime_history['confidence_score'] >= 0.8]),
                    'medium_confidence': len(regime_history[(regime_history['confidence_score'] >= 0.6) & (regime_history['confidence_score'] < 0.8)]),
                    'low_confidence': len(regime_history[regime_history['confidence_score'] < 0.6])
                },
                'regime_distribution': regime_history['regime_name'].value_counts().to_dict(),
                'regime_type_distribution': regime_history['regime_type'].value_counts().to_dict(),
                'timeframe_distribution': regime_history['timeframe'].value_counts().to_dict()
            }
            
            # Calculate regime stability and transitions
            regime_history_sorted = regime_history.sort_values('timestamp')
            transitions = []
            
            for i in range(1, len(regime_history_sorted)):
                prev_regime = regime_history_sorted.iloc[i-1]['regime_id']
                curr_regime = regime_history_sorted.iloc[i]['regime_id']
                
                if prev_regime != curr_regime:
                    transitions.append({
                        'from_regime': prev_regime,
                        'to_regime': curr_regime,
                        'timestamp': regime_history_sorted.iloc[i]['timestamp']
                    })
            
            analysis['regime_transitions'] = {
                'total_transitions': len(transitions),
                'transition_rate': len(transitions) / len(regime_history) if len(regime_history) > 0 else 0,
                'most_common_transitions': {}
            }
            
            # Most common transitions
            if transitions:
                transition_pairs = [f"{t['from_regime']} -> {t['to_regime']}" for t in transitions]
                from collections import Counter
                transition_counts = Counter(transition_pairs)
                analysis['regime_transitions']['most_common_transitions'] = dict(transition_counts.most_common(5))
            
            # Performance by regime
            regime_performance = {}
            for regime_id in regime_history['regime_id'].unique():
                regime_data = regime_history[regime_history['regime_id'] == regime_id]
                regime_performance[regime_id] = {
                    'occurrences': len(regime_data),
                    'average_confidence': regime_data['confidence_score'].mean(),
                    'confidence_std': regime_data['confidence_score'].std(),
                    'percentage_of_total': len(regime_data) / len(regime_history) * 100
                }
            
            analysis['regime_performance'] = regime_performance
            
            logger.info(f"✅ Performance analysis completed: {len(regime_history)} classifications analyzed")
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing regime performance: {e}")
            return {'error': str(e)}
    
    def optimize_indicator_weights(self, performance_data: Dict[str, float]) -> Dict[str, Any]:
        """
        Optimize indicator weights based on performance data
        
        Args:
            performance_data (Dict): Performance scores for each indicator
            
        Returns:
            Dict: Optimization results
        """
        try:
            if self.current_mode != 'optimization':
                logger.warning("System not in optimization mode, switching temporarily")
            
            logger.info("🔧 Optimizing indicator weights based on performance data")
            
            # Update indicator performance in formation engine
            self.formation_engine.update_indicator_performance(performance_data)
            
            # Get updated configuration summary
            summary = self.formation_engine.get_regime_formation_summary()
            
            optimization_result = {
                'optimization_timestamp': datetime.now().isoformat(),
                'indicators_updated': len(performance_data),
                'performance_data': performance_data,
                'updated_configuration': summary,
                'optimization_successful': True
            }
            
            logger.info(f"✅ Indicator weights optimized: {len(performance_data)} indicators updated")
            
            return optimization_result
            
        except Exception as e:
            logger.error(f"Error optimizing indicator weights: {e}")
            return {'error': str(e), 'optimization_successful': False}
    
    def _generate_configuration_documentation(self, output_path: str):
        """Generate comprehensive configuration documentation"""
        try:
            documentation = """# Enhanced Market Regime Configuration Guide

## Overview
This guide explains how to configure the Enhanced Market Regime Formation System for optimal regime detection and analysis.

## Configuration Sheets

### 1. IndicatorRegistry Sheet
Configure all available indicators with their parameters:
- **IndicatorID**: Unique identifier for the indicator
- **IndicatorName**: Human-readable name
- **Category**: Indicator category (GREEK_SENTIMENT, OI_ANALYSIS, etc.)
- **BaseWeight**: Base weight for the indicator (0.0-1.0)
- **Enabled**: Whether the indicator is active

### 2. DynamicWeightageConfig Sheet
Configure dynamic weight adjustment:
- **CurrentWeight**: Current weight of the indicator
- **HistoricalPerformance**: Performance score (0.0-1.0)
- **LearningRate**: How quickly weights adjust (0.01-0.5)
- **AutoAdjust**: Enable automatic weight adjustment

### 3. RegimeDefinitionConfig Sheet
Define custom market regimes:
- **RegimeID**: Unique regime identifier
- **DirectionalThreshold**: Threshold for directional component (-1.0 to 1.0)
- **VolatilityThreshold**: Threshold for volatility component (0.0-1.0)
- **ConfidenceThreshold**: Minimum confidence for regime classification

### 4. ConfidenceScoreConfig Sheet
Configure confidence score calculation:
- **ScoreComponent**: Component of confidence score
- **Weight**: Weight of this component in final score
- **Threshold**: Minimum threshold for this component

### 5. TimeframeConfig Sheet
Configure multi-timeframe analysis:
- **Timeframe**: Timeframe identifier (1min, 5min, etc.)
- **Weight**: Weight in multi-timeframe analysis
- **PrimaryTimeframe**: Whether this is the primary timeframe

### 6. UserRegimeProfiles Sheet
Configure individual user profiles:
- **UserID**: Unique user identifier
- **RegimePreferences**: Preferred regime types
- **CustomWeights**: Custom indicator weights
- **CustomThresholds**: Custom regime thresholds

## Usage Examples

### Basic Regime Formation
```python
# Initialize system
system = CompleteEnhancedMarketRegimeSystem('config.xlsx')
system.initialize_system('formation')

# Form regime
market_data = {
    'price_data': {...},
    'options_data': {...},
    'technical_data': {...}
}

result = system.form_market_regime(market_data, user_id='USER_001')
print(f"Regime: {result['regime_name']} (Confidence: {result['confidence_score']:.2f})")
```

### Performance Analysis
```python
# Analyze performance
analysis = system.analyze_regime_performance(user_id='USER_001', days=30)
print(f"Total classifications: {analysis['total_regime_classifications']}")
print(f"Average confidence: {analysis['average_confidence']:.2f}")
```

### Weight Optimization
```python
# Optimize weights based on performance
performance_data = {
    'DELTA_SENTIMENT': 0.85,
    'OI_MOMENTUM': 0.78,
    'PRICE_MOMENTUM': 0.82
}

result = system.optimize_indicator_weights(performance_data)
print(f"Optimization successful: {result['optimization_successful']}")
```

## Best Practices

1. **Start with default configuration** and gradually customize
2. **Monitor performance regularly** and adjust weights accordingly
3. **Use multiple user profiles** for different trading styles
4. **Validate configuration** before production use
5. **Keep historical data** for performance analysis

## Troubleshooting

- **Low confidence scores**: Check indicator weights and thresholds
- **Frequent regime changes**: Increase regime smoothing or confidence thresholds
- **Poor performance**: Analyze indicator performance and adjust weights
- **Configuration errors**: Use validation function to check setup

For more detailed information, refer to the code documentation and examples.
"""
            
            with open(output_path, 'w') as f:
                f.write(documentation)
            
            logger.info(f"Generated configuration documentation: {output_path}")
            
        except Exception as e:
            logger.error(f"Error generating documentation: {e}")
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        try:
            status = {
                'system_initialized': self.system_initialized,
                'current_mode': self.current_mode,
                'config_path': self.config_path,
                'storage_path': self.storage_path,
                'active_users': len(self.active_users),
                'regime_history_count': len(self.regime_history),
                'components_status': {
                    'config_manager': self.config_manager is not None,
                    'formation_engine': self.formation_engine is not None,
                    'storage': self.storage is not None
                }
            }
            
            # Add detailed component status
            if self.formation_engine:
                status['formation_engine_summary'] = self.formation_engine.get_regime_formation_summary()
            
            if self.config_manager:
                status['configuration_summary'] = self.config_manager.get_configuration_summary()
            
            # Add user activity summary
            if self.active_users:
                status['user_activity'] = {
                    user_id: {
                        'regime_count': user_data['regime_count'],
                        'configurations_used': len(user_data['configurations_used']),
                        'last_activity': user_data.get('last_regime_time', '').isoformat() if user_data.get('last_regime_time') else None
                    }
                    for user_id, user_data in self.active_users.items()
                }
            
            return status
            
        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return {'error': str(e)}
    
    def close_system(self) -> bool:
        """Close system and cleanup resources"""
        try:
            logger.info("🔄 Closing Enhanced Market Regime System")
            
            # Close formation engine
            if self.formation_engine:
                self.formation_engine.close()
            
            # Close storage
            if self.storage:
                self.storage.close_connection()
            
            # Save final configuration
            if self.config_manager:
                self.config_manager.save_configuration()
            
            self.system_initialized = False
            
            logger.info("✅ System closed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error closing system: {e}")
            return False

def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(description='Complete Enhanced Market Regime Formation System')
    parser.add_argument('--mode', choices=['formation', 'analysis', 'template', 'optimization'], 
                       default='template', help='System mode')
    parser.add_argument('--config', help='Path to configuration Excel file')
    parser.add_argument('--storage', help='Path to time-series storage database')
    parser.add_argument('--output-dir', default='input_sheets/enhanced_market_regime', 
                       help='Output directory for templates')
    parser.add_argument('--user-id', default='DEFAULT', help='User ID for regime formation')
    parser.add_argument('--days', type=int, default=30, help='Analysis window in days')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose logging')
    
    args = parser.parse_args()
    
    # Configure logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S'
    )
    
    try:
        logger.info("🚀 Complete Enhanced Market Regime Formation System")
        logger.info(f"Mode: {args.mode.upper()}")
        logger.info("-" * 80)
        
        # Initialize system
        system = CompleteEnhancedMarketRegimeSystem(
            config_path=args.config,
            storage_path=args.storage
        )
        
        if not system.initialize_system(mode=args.mode):
            logger.error("Failed to initialize system")
            return 1
        
        # Execute based on mode
        if args.mode == 'template':
            logger.info("📊 Generating enhanced Excel templates...")
            templates = system.generate_enhanced_templates(args.output_dir)
            
            if 'error' not in templates:
                logger.info("✅ Template generation completed!")
                for template_type, path in templates.items():
                    logger.info(f"   {template_type.upper()}: {path}")
            else:
                logger.error(f"Template generation failed: {templates['error']}")
                return 1
                
        elif args.mode == 'formation':
            logger.info("🎯 Running regime formation example...")
            
            # Example market data
            market_data = {
                'price_data': {
                    'close': 18500,
                    'previous_close': 18450,
                    'volume': 1500000,
                    'avg_volume': 1200000,
                    'returns': [0.001, -0.002, 0.003, -0.001, 0.002]
                },
                'options_data': {
                    'delta': [0.5, 0.3, -0.3, -0.5],
                    'gamma': [0.01, 0.015, 0.015, 0.01],
                    'volume': [50000, 30000, 40000, 35000],
                    'open_interest': [100000, 80000, 90000, 85000],
                    'implied_volatility': [0.18, 0.20, 0.19, 0.21]
                },
                'oi_data': {
                    'total_oi': 5000000,
                    'previous_oi': 4800000,
                    'call_oi': 2800000,
                    'put_oi': 2200000
                },
                'technical_data': {
                    'rsi': 65,
                    'macd': 0.5,
                    'macd_signal': 0.3
                },
                'straddle_data': {
                    'total_premium': 250,
                    'previous_premium': 240
                }
            }
            
            result = system.form_market_regime(market_data, user_id=args.user_id)
            
            if 'error' not in result:
                logger.info("✅ Regime formation completed!")
                logger.info(f"   Regime: {result['regime_name']}")
                logger.info(f"   Confidence: {result['confidence_score']:.2f}")
                logger.info(f"   Directional: {result['directional_component']:.2f}")
                logger.info(f"   Volatility: {result['volatility_component']:.2f}")
            else:
                logger.error(f"Regime formation failed: {result['error']}")
                return 1
                
        elif args.mode == 'analysis':
            logger.info(f"📈 Running performance analysis for {args.days} days...")
            analysis = system.analyze_regime_performance(user_id=args.user_id, days=args.days)
            
            if 'error' not in analysis:
                logger.info("✅ Performance analysis completed!")
                logger.info(f"   Total classifications: {analysis['total_regime_classifications']}")
                logger.info(f"   Unique regimes: {analysis['unique_regimes_detected']}")
                logger.info(f"   Average confidence: {analysis['average_confidence']:.2f}")
                logger.info(f"   Regime transitions: {analysis['regime_transitions']['total_transitions']}")
            else:
                logger.error(f"Performance analysis failed: {analysis['error']}")
                return 1
                
        elif args.mode == 'optimization':
            logger.info("🔧 Running weight optimization example...")
            
            # Example performance data
            performance_data = {
                'DELTA_SENTIMENT': 0.85,
                'OI_MOMENTUM': 0.78,
                'PRICE_MOMENTUM': 0.82,
                'RSI_DIVERGENCE': 0.75,
                'REALIZED_VOL': 0.88
            }
            
            result = system.optimize_indicator_weights(performance_data)
            
            if result.get('optimization_successful', False):
                logger.info("✅ Weight optimization completed!")
                logger.info(f"   Indicators updated: {result['indicators_updated']}")
            else:
                logger.error(f"Weight optimization failed: {result.get('error', 'Unknown error')}")
                return 1
        
        # Print final status
        final_status = system.get_system_status()
        logger.info("\n📊 Final System Status:")
        logger.info(f"   Initialized: {final_status['system_initialized']}")
        logger.info(f"   Mode: {final_status['current_mode']}")
        logger.info(f"   Active users: {final_status['active_users']}")
        logger.info(f"   Regime history: {final_status['regime_history_count']}")
        
        # Close system
        system.close_system()
        
        return 0
        
    except Exception as e:
        logger.error(f"System execution failed: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
