#!/usr/bin/env python3
"""
Market Regime Configuration Template Generator

This script generates Excel configuration templates for the market regime
detection system, following the same patterns as other backtester_v2 systems.

Usage:
    python3 generate_regime_config_template.py [--output OUTPUT_PATH] [--type TEMPLATE_TYPE]
"""

import argparse
import logging
from pathlib import Path
from datetime import datetime

from backtester_v2.market_regime.excel_config_manager import MarketRegimeExcelManager

logger = logging.getLogger(__name__)

def generate_basic_template(output_path: str) -> str:
    """Generate basic market regime configuration template"""
    try:
        logger.info("Generating basic market regime configuration template...")
        
        config_manager = MarketRegimeExcelManager()
        generated_path = config_manager.generate_excel_template(output_path)
        
        logger.info(f"✅ Basic template generated: {generated_path}")
        return generated_path
        
    except Exception as e:
        logger.error(f"Error generating basic template: {e}")
        raise

def generate_advanced_template(output_path: str) -> str:
    """Generate advanced market regime configuration template with examples"""
    try:
        logger.info("Generating advanced market regime configuration template...")
        
        config_manager = MarketRegimeExcelManager()
        
        # Generate basic template first
        generated_path = config_manager.generate_excel_template(output_path)
        
        # Load and enhance with examples
        config_manager.load_configuration(generated_path)
        
        # Update with example values
        config_manager.update_parameter('RegimeDetectionConfig', 'ConfidenceThreshold', 0.75)
        config_manager.update_parameter('RegimeDetectionConfig', 'RegimeSmoothing', 5)
        config_manager.update_parameter('LiveTradingConfig', 'EnableLiveTrading', 'YES')
        config_manager.update_parameter('LiveTradingConfig', 'StreamingIntervalMs', 50)
        
        # Save enhanced configuration
        config_manager.save_configuration(generated_path)
        
        logger.info(f"✅ Advanced template generated: {generated_path}")
        return generated_path
        
    except Exception as e:
        logger.error(f"Error generating advanced template: {e}")
        raise

def generate_production_template(output_path: str) -> str:
    """Generate production-ready market regime configuration template"""
    try:
        logger.info("Generating production-ready market regime configuration template...")
        
        config_manager = MarketRegimeExcelManager()
        
        # Generate basic template
        generated_path = config_manager.generate_excel_template(output_path)
        
        # Load and configure for production
        config_manager.load_configuration(generated_path)
        
        # Production-optimized parameters
        production_params = {
            'RegimeDetectionConfig': {
                'ConfidenceThreshold': 0.8,
                'RegimeSmoothing': 3,
                'IndicatorWeightGreek': 0.40,
                'IndicatorWeightOI': 0.30,
                'IndicatorWeightPrice': 0.20,
                'IndicatorWeightTechnical': 0.10
            },
            'LiveTradingConfig': {
                'EnableLiveTrading': 'YES',
                'StreamingIntervalMs': 100,
                'RegimeUpdateFreqSec': 30,
                'EnableAlgobobaIntegration': 'YES',
                'MaxDailyOrders': 50,
                'MaxRegimeExposure': 0.3,
                'EnableRegimeAlerts': 'YES',
                'EnablePerformanceTracking': 'YES'
            }
        }
        
        # Apply production parameters
        for sheet_name, params in production_params.items():
            for param_name, param_value in params.items():
                config_manager.update_parameter(sheet_name, param_name, param_value)
        
        # Save production configuration
        config_manager.save_configuration(generated_path)
        
        logger.info(f"✅ Production template generated: {generated_path}")
        return generated_path
        
    except Exception as e:
        logger.error(f"Error generating production template: {e}")
        raise

def validate_template(template_path: str) -> bool:
    """Validate generated template"""
    try:
        logger.info(f"Validating template: {template_path}")
        
        config_manager = MarketRegimeExcelManager(template_path)
        is_valid, errors = config_manager.validate_configuration()
        
        if is_valid:
            logger.info("✅ Template validation passed")
            
            # Print configuration summary
            detection_params = config_manager.get_detection_parameters()
            regime_adjustments = config_manager.get_regime_adjustments()
            strategy_mappings = config_manager.get_strategy_mappings()
            live_config = config_manager.get_live_trading_config()
            
            logger.info(f"   📊 Detection parameters: {len(detection_params)}")
            logger.info(f"   🎯 Regime adjustments: {len(regime_adjustments)}")
            logger.info(f"   📈 Strategy mappings: {len(strategy_mappings)}")
            logger.info(f"   🔴 Live trading enabled: {live_config.get('EnableLiveTrading', False)}")
            
        else:
            logger.error("❌ Template validation failed:")
            for error in errors:
                logger.error(f"   - {error}")
        
        return is_valid
        
    except Exception as e:
        logger.error(f"Error validating template: {e}")
        return False

def print_template_info(template_path: str):
    """Print detailed template information"""
    try:
        logger.info(f"\n📋 Template Information: {template_path}")
        logger.info("=" * 60)
        
        config_manager = MarketRegimeExcelManager(template_path)
        
        # Detection parameters
        detection_params = config_manager.get_detection_parameters()
        logger.info("🧠 Detection Parameters:")
        for param, value in detection_params.items():
            logger.info(f"   {param}: {value}")
        
        # Regime adjustments summary
        regime_adjustments = config_manager.get_regime_adjustments()
        logger.info(f"\n🎯 Regime Adjustments: {len(regime_adjustments)} regimes configured")
        
        # Strategy mappings summary
        strategy_mappings = config_manager.get_strategy_mappings()
        logger.info(f"\n📈 Strategy Mappings:")
        for strategy_type, mappings in strategy_mappings.items():
            enabled_regimes = sum(1 for regime_config in mappings.values() if regime_config.get('enable_strategy', False))
            logger.info(f"   {strategy_type}: {enabled_regimes}/{len(mappings)} regimes enabled")
        
        # Live trading configuration
        live_config = config_manager.get_live_trading_config()
        logger.info(f"\n🔴 Live Trading Configuration:")
        key_configs = ['EnableLiveTrading', 'StreamingIntervalMs', 'RegimeUpdateFreqSec', 'EnableAlgobobaIntegration']
        for key in key_configs:
            if key in live_config:
                logger.info(f"   {key}: {live_config[key]}")
        
        logger.info("=" * 60)
        
    except Exception as e:
        logger.error(f"Error printing template info: {e}")

def main():
    """Main template generation"""
    parser = argparse.ArgumentParser(description='Market Regime Configuration Template Generator')
    parser.add_argument('--output', '-o', 
                       default='input_sheets/market_regime/market_regime_config.xlsx',
                       help='Output path for the template')
    parser.add_argument('--type', '-t', 
                       choices=['basic', 'advanced', 'production'],
                       default='basic',
                       help='Template type to generate')
    parser.add_argument('--validate', action='store_true',
                       help='Validate the generated template')
    parser.add_argument('--info', action='store_true',
                       help='Print detailed template information')
    parser.add_argument('--verbose', '-v', action='store_true',
                       help='Enable verbose logging')
    
    args = parser.parse_args()
    
    # Configure logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S'
    )
    
    try:
        # Ensure output directory exists
        output_path = Path(args.output)
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        logger.info("🚀 Market Regime Configuration Template Generator")
        logger.info(f"Template Type: {args.type.upper()}")
        logger.info(f"Output Path: {output_path}")
        logger.info("-" * 60)
        
        # Generate template based on type
        if args.type == 'basic':
            generated_path = generate_basic_template(str(output_path))
        elif args.type == 'advanced':
            generated_path = generate_advanced_template(str(output_path))
        elif args.type == 'production':
            generated_path = generate_production_template(str(output_path))
        else:
            raise ValueError(f"Unknown template type: {args.type}")
        
        # Validate if requested
        if args.validate:
            logger.info("\n🔍 Validating generated template...")
            is_valid = validate_template(generated_path)
            if not is_valid:
                logger.error("Template validation failed!")
                return 1
        
        # Print info if requested
        if args.info:
            print_template_info(generated_path)
        
        # Success summary
        logger.info("\n🎉 Template Generation Complete!")
        logger.info(f"✅ Template saved: {generated_path}")
        logger.info(f"✅ Template type: {args.type.upper()}")
        logger.info(f"✅ File size: {Path(generated_path).stat().st_size} bytes")
        
        # Usage instructions
        logger.info("\n📖 Usage Instructions:")
        logger.info("1. Open the Excel file in your preferred spreadsheet application")
        logger.info("2. Review and modify the configuration parameters as needed")
        logger.info("3. Save the file and use it with the market regime system")
        logger.info("4. The file follows the same pattern as other backtester_v2 configurations")
        
        # Integration instructions
        logger.info("\n🔗 Integration Instructions:")
        logger.info("# Python code to use the configuration:")
        logger.info("from backtester_v2.market_regime.excel_config_manager import MarketRegimeExcelManager")
        logger.info(f"config_manager = MarketRegimeExcelManager('{generated_path}')")
        logger.info("detection_params = config_manager.get_detection_parameters()")
        logger.info("regime_adjustments = config_manager.get_regime_adjustments()")
        
        return 0
        
    except Exception as e:
        logger.error(f"Template generation failed: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
