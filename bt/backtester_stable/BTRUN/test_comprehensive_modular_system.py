#!/usr/bin/env python3
"""
Comprehensive Modular System Test Suite

This script tests the complete modular trading system including:
- 18 Market regime detection
- Live streaming integration
- Strategy consolidation
- Algobaba integration
- System orchestration

Usage:
    python3 test_comprehensive_modular_system.py [--verbose] [--duration SECONDS]
"""

import sys
import logging
import time
import argparse
from datetime import datetime, timedelta
from typing import Dict, List, Any
import numpy as np
import pandas as pd

# Import system components
from backtester_v2.market_regime.enhanced_regime_detector import Enhanced18RegimeDetector, Enhanced18RegimeType
from backtester_v2.live_streaming.kite_streamer import KiteStreamer
from backtester_v2.strategy_consolidator.base_consolidator import DataDrivenConsolidator
from backtester_v2.algobaba_integration.regime_order_manager import RegimeOrderManager
from backtester_v2.integration.system_orchestrator import SystemOrchestrator

logger = logging.getLogger(__name__)

class ComprehensiveSystemTest:
    """
    Comprehensive test suite for the modular trading system
    """
    
    def __init__(self, test_duration: int = 120, verbose: bool = False):
        """
        Initialize test suite
        
        Args:
            test_duration (int): Test duration in seconds
            verbose (bool): Enable verbose logging
        """
        self.test_duration = test_duration
        self.verbose = verbose
        
        # Test results tracking
        self.test_results = {
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'test_details': [],
            'start_time': None,
            'end_time': None
        }
        
        # System components
        self.regime_detector = None
        self.live_streamer = None
        self.strategy_consolidator = None
        self.order_manager = None
        self.system_orchestrator = None
        
        logger.info("ComprehensiveSystemTest initialized")
    
    def run_all_tests(self):
        """Run all comprehensive system tests"""
        try:
            self.test_results['start_time'] = datetime.now()
            logger.info("🚀 Starting Comprehensive Modular System Tests")
            logger.info("=" * 70)
            
            # Test 1: 18 Regime Detection System
            self._test_18_regime_detection()
            
            # Test 2: Live Streaming Integration
            self._test_live_streaming_integration()
            
            # Test 3: Strategy Consolidation
            self._test_strategy_consolidation()
            
            # Test 4: Algobaba Integration
            self._test_algobaba_integration()
            
            # Test 5: System Orchestration
            self._test_system_orchestration()
            
            # Test 6: End-to-End Integration
            self._test_end_to_end_integration()
            
            # Test 7: Performance and Scalability
            self._test_performance_scalability()
            
            # Test 8: Error Handling and Recovery
            self._test_error_handling()
            
            # Test 9: Configuration Management
            self._test_configuration_management()
            
            # Test 10: Data Export and Reporting
            self._test_data_export_reporting()
            
            self.test_results['end_time'] = datetime.now()
            self._print_comprehensive_summary()
            
        except Exception as e:
            logger.error(f"Fatal error in test suite: {e}")
            self.test_results['end_time'] = datetime.now()
            self._print_comprehensive_summary()
            raise
    
    def _test_18_regime_detection(self):
        """Test 1: 18 Regime Detection System"""
        test_name = "18 Regime Detection System"
        logger.info(f"🧠 Test 1: {test_name}")
        
        try:
            # Initialize 18-regime detector
            self.regime_detector = Enhanced18RegimeDetector()
            
            # Test regime detection with mock data
            mock_market_data = self._generate_mock_market_data()
            regime_result = self.regime_detector.detect_regime(mock_market_data)
            
            # Verify regime detection
            assert regime_result is not None, "No regime result returned"
            assert 'regime_type' in regime_result, "No regime type in result"
            assert isinstance(regime_result['regime_type'], Enhanced18RegimeType), "Invalid regime type"
            assert 0 <= regime_result['confidence'] <= 1, "Invalid confidence score"
            
            # Test all 18 regime types
            regime_types_detected = set()
            for i in range(50):  # Generate multiple scenarios
                varied_data = self._generate_varied_market_data(i)
                result = self.regime_detector.detect_regime(varied_data)
                regime_types_detected.add(result['regime_type'])
            
            logger.info(f"   ✓ Detected {len(regime_types_detected)} different regime types")
            logger.info(f"   ✓ Current regime: {regime_result['regime_type'].value}")
            logger.info(f"   ✓ Confidence: {regime_result['confidence']:.2f}")
            
            self._record_test_result(test_name, True, 
                f"18-regime detection working: {len(regime_types_detected)} regimes detected")
            
        except Exception as e:
            self._record_test_result(test_name, False, f"18-regime detection failed: {e}")
    
    def _test_live_streaming_integration(self):
        """Test 2: Live Streaming Integration"""
        test_name = "Live Streaming Integration"
        logger.info(f"📊 Test 2: {test_name}")
        
        try:
            # Initialize live streamer
            self.live_streamer = KiteStreamer(
                regime_detector=self.regime_detector,
                config={'stream_interval_ms': 50, 'regime_update_freq_sec': 5}
            )
            
            # Test streaming initialization
            assert self.live_streamer is not None, "Live streamer not initialized"
            
            # Start streaming
            self.live_streamer.start_streaming()
            
            # Wait for streaming to process data
            time.sleep(3)
            
            # Check streaming statistics
            stats = self.live_streamer.get_streaming_statistics()
            assert stats['total_ticks'] >= 0, "No streaming statistics"
            
            # Test timeframe data
            timeframe_data = self.live_streamer.get_timeframe_data('1min', limit=10)
            
            logger.info(f"   ✓ Streaming started successfully")
            logger.info(f"   ✓ Total ticks processed: {stats['total_ticks']}")
            logger.info(f"   ✓ Timeframe data available: {len(timeframe_data)} candles")
            
            self._record_test_result(test_name, True, 
                f"Live streaming functional: {stats['total_ticks']} ticks processed")
            
        except Exception as e:
            self._record_test_result(test_name, False, f"Live streaming failed: {e}")
    
    def _test_strategy_consolidation(self):
        """Test 3: Strategy Consolidation"""
        test_name = "Strategy Consolidation"
        logger.info(f"📈 Test 3: {test_name}")
        
        try:
            # Initialize strategy consolidator
            self.strategy_consolidator = DataDrivenConsolidator()
            
            # Create mock strategy sources
            mock_sources = self._create_mock_strategy_sources()
            
            # Test consolidation
            consolidated_strategies = self.strategy_consolidator.consolidate_strategies(mock_sources)
            
            # Verify consolidation
            assert isinstance(consolidated_strategies, list), "Invalid consolidation result"
            
            # Get consolidation summary
            summary = self.strategy_consolidator.get_consolidation_summary()
            
            logger.info(f"   ✓ Strategy consolidation completed")
            logger.info(f"   ✓ Consolidated strategies: {len(consolidated_strategies)}")
            logger.info(f"   ✓ Summary: {summary}")
            
            self._record_test_result(test_name, True, 
                f"Strategy consolidation successful: {len(consolidated_strategies)} strategies")
            
        except Exception as e:
            self._record_test_result(test_name, False, f"Strategy consolidation failed: {e}")
    
    def _test_algobaba_integration(self):
        """Test 4: Algobaba Integration"""
        test_name = "Algobaba Integration"
        logger.info(f"🔗 Test 4: {test_name}")
        
        try:
            # Initialize order manager
            self.order_manager = RegimeOrderManager()
            
            # Test order execution with mock data
            mock_strategy = type('MockStrategy', (), {
                'instance_id': 'test_strategy_001'
            })()
            
            mock_regime_data = {
                'regime_type': Enhanced18RegimeType.NORMAL_VOLATILE_MILD_BULLISH,
                'confidence': 0.75,
                'timestamp': datetime.now()
            }
            
            mock_order_signal = {
                'action': 'ENTRY',
                'quantity': 10,
                'stop_loss': 100,
                'take_profit': 200
            }
            
            # Execute test order
            result = self.order_manager.execute_regime_order(
                strategy=mock_strategy,
                regime_data=mock_regime_data,
                order_signal=mock_order_signal
            )
            
            # Verify order execution
            assert isinstance(result, dict), "Invalid order result"
            assert 'success' in result, "No success indicator in result"
            
            # Get performance metrics
            performance = self.order_manager.get_regime_performance()
            
            logger.info(f"   ✓ Algobaba integration functional")
            logger.info(f"   ✓ Order execution result: {result.get('success', False)}")
            logger.info(f"   ✓ Performance tracking: {len(performance.get('regime_performance', {}))}")
            
            self._record_test_result(test_name, True, 
                f"Algobaba integration successful: Order executed")
            
        except Exception as e:
            self._record_test_result(test_name, False, f"Algobaba integration failed: {e}")
    
    def _test_system_orchestration(self):
        """Test 5: System Orchestration"""
        test_name = "System Orchestration"
        logger.info(f"🎯 Test 5: {test_name}")
        
        try:
            # Initialize system orchestrator
            self.system_orchestrator = SystemOrchestrator()
            
            # Initialize system
            self.system_orchestrator.initialize_system()
            
            # Start system
            self.system_orchestrator.start_system()
            
            # Wait for system to stabilize
            time.sleep(2)
            
            # Get system status
            status = self.system_orchestrator.get_system_status()
            
            # Verify system status
            assert status['system_running'], "System not running"
            assert status['components_status']['regime_detector'], "Regime detector not initialized"
            
            logger.info(f"   ✓ System orchestration successful")
            logger.info(f"   ✓ System running: {status['system_running']}")
            logger.info(f"   ✓ Components active: {sum(status['components_status'].values())}")
            
            self._record_test_result(test_name, True, 
                f"System orchestration successful: All components initialized")
            
        except Exception as e:
            self._record_test_result(test_name, False, f"System orchestration failed: {e}")
    
    def _test_end_to_end_integration(self):
        """Test 6: End-to-End Integration"""
        test_name = "End-to-End Integration"
        logger.info(f"🔄 Test 6: {test_name}")
        
        try:
            # Test complete workflow
            if self.system_orchestrator:
                # Test strategy consolidation
                mock_sources = self._create_mock_strategy_sources()
                consolidated = self.system_orchestrator.consolidate_strategies(mock_sources)
                
                # Test order execution
                mock_order = {
                    'strategy_id': 'test_strategy',
                    'action': 'ENTRY',
                    'quantity': 5
                }
                
                order_result = self.system_orchestrator.execute_strategy_order(mock_order)
                
                # Get filtered strategies
                filtered_strategies = self.system_orchestrator.get_regime_filtered_strategies()
                
                logger.info(f"   ✓ End-to-end integration successful")
                logger.info(f"   ✓ Strategies consolidated: {len(consolidated)}")
                logger.info(f"   ✓ Order executed: {order_result.get('success', False)}")
                logger.info(f"   ✓ Filtered strategies: {len(filtered_strategies)}")
                
                self._record_test_result(test_name, True, 
                    f"End-to-end integration successful: Complete workflow tested")
            else:
                self._record_test_result(test_name, False, "System orchestrator not available")
            
        except Exception as e:
            self._record_test_result(test_name, False, f"End-to-end integration failed: {e}")
    
    def _test_performance_scalability(self):
        """Test 7: Performance and Scalability"""
        test_name = "Performance and Scalability"
        logger.info(f"⚡ Test 7: {test_name}")
        
        try:
            # Test regime detection performance
            start_time = time.time()
            
            for i in range(100):
                mock_data = self._generate_varied_market_data(i)
                self.regime_detector.detect_regime(mock_data)
            
            detection_time = time.time() - start_time
            avg_detection_time = detection_time / 100
            
            # Test memory usage (basic check)
            import psutil
            import os
            process = psutil.Process(os.getpid())
            memory_usage = process.memory_info().rss / 1024 / 1024  # MB
            
            logger.info(f"   ✓ Performance test completed")
            logger.info(f"   ✓ Average detection time: {avg_detection_time*1000:.2f}ms")
            logger.info(f"   ✓ Memory usage: {memory_usage:.1f}MB")
            
            # Performance thresholds
            assert avg_detection_time < 0.1, f"Detection too slow: {avg_detection_time:.3f}s"
            assert memory_usage < 500, f"Memory usage too high: {memory_usage:.1f}MB"
            
            self._record_test_result(test_name, True, 
                f"Performance acceptable: {avg_detection_time*1000:.1f}ms avg detection")
            
        except Exception as e:
            self._record_test_result(test_name, False, f"Performance test failed: {e}")
    
    def _test_error_handling(self):
        """Test 8: Error Handling and Recovery"""
        test_name = "Error Handling and Recovery"
        logger.info(f"🛡️ Test 8: {test_name}")
        
        try:
            # Test invalid market data
            invalid_data = {'invalid': 'data'}
            result = self.regime_detector.detect_regime(invalid_data)
            assert result is not None, "Should handle invalid data gracefully"
            
            # Test system recovery
            if self.system_orchestrator:
                original_status = self.system_orchestrator.get_system_status()
                
                # Simulate error and recovery
                try:
                    # This should not crash the system
                    self.system_orchestrator.execute_strategy_order({'invalid': 'order'})
                except:
                    pass
                
                # Check system is still running
                recovery_status = self.system_orchestrator.get_system_status()
                assert recovery_status['system_running'], "System should recover from errors"
            
            logger.info(f"   ✓ Error handling functional")
            logger.info(f"   ✓ System recovery successful")
            
            self._record_test_result(test_name, True, 
                f"Error handling successful: System recovers gracefully")
            
        except Exception as e:
            self._record_test_result(test_name, False, f"Error handling failed: {e}")
    
    def _test_configuration_management(self):
        """Test 9: Configuration Management"""
        test_name = "Configuration Management"
        logger.info(f"⚙️ Test 9: {test_name}")
        
        try:
            if self.system_orchestrator:
                # Test configuration update
                new_config = {
                    'regime_detection': {
                        'confidence_threshold': 0.7
                    }
                }
                
                self.system_orchestrator.update_configuration(new_config)
                
                # Verify configuration
                status = self.system_orchestrator.get_system_status()
                assert status is not None, "Status should be available"
                
                logger.info(f"   ✓ Configuration management functional")
                
                self._record_test_result(test_name, True, 
                    f"Configuration management successful")
            else:
                self._record_test_result(test_name, False, "System orchestrator not available")
            
        except Exception as e:
            self._record_test_result(test_name, False, f"Configuration management failed: {e}")
    
    def _test_data_export_reporting(self):
        """Test 10: Data Export and Reporting"""
        test_name = "Data Export and Reporting"
        logger.info(f"📊 Test 10: {test_name}")
        
        try:
            if self.system_orchestrator:
                # Test data export
                export_data = self.system_orchestrator.export_system_data()
                
                # Verify export data
                assert isinstance(export_data, dict), "Export data should be dictionary"
                assert 'system_status' in export_data, "Should include system status"
                assert 'export_timestamp' in export_data, "Should include timestamp"
                
                logger.info(f"   ✓ Data export functional")
                logger.info(f"   ✓ Export data keys: {list(export_data.keys())}")
                
                self._record_test_result(test_name, True, 
                    f"Data export successful: {len(export_data)} data sections")
            else:
                self._record_test_result(test_name, False, "System orchestrator not available")
            
        except Exception as e:
            self._record_test_result(test_name, False, f"Data export failed: {e}")
    
    def _generate_mock_market_data(self) -> Dict[str, Any]:
        """Generate mock market data for testing"""
        return {
            'price_data': [100 + i + np.random.normal(0, 2) for i in range(20)],
            'oi_data': {
                'call_oi': 1000000,
                'put_oi': 1200000,
                'call_volume': 50000,
                'put_volume': 60000
            },
            'greek_sentiment': {
                'delta': 0.5,
                'gamma': 0.1,
                'theta': -0.05,
                'vega': 0.2
            },
            'technical_indicators': {
                'rsi': 55,
                'macd': 0.5,
                'macd_signal': 0.3,
                'ma_signal': 0.1
            },
            'atr': 2.5,
            'implied_volatility': 0.15
        }
    
    def _generate_varied_market_data(self, seed: int) -> Dict[str, Any]:
        """Generate varied market data for different scenarios"""
        np.random.seed(seed)
        
        # Create different market scenarios
        scenarios = ['bullish', 'bearish', 'neutral', 'volatile', 'calm']
        scenario = scenarios[seed % len(scenarios)]
        
        if scenario == 'bullish':
            trend = 1.0
            volatility = 0.1
        elif scenario == 'bearish':
            trend = -1.0
            volatility = 0.1
        elif scenario == 'volatile':
            trend = 0.0
            volatility = 0.3
        elif scenario == 'calm':
            trend = 0.0
            volatility = 0.05
        else:  # neutral
            trend = 0.0
            volatility = 0.15
        
        base_price = 100
        price_data = []
        for i in range(20):
            price = base_price + (trend * i) + np.random.normal(0, volatility * base_price)
            price_data.append(price)
        
        return {
            'price_data': price_data,
            'oi_data': {
                'call_oi': np.random.randint(500000, 2000000),
                'put_oi': np.random.randint(500000, 2000000),
                'call_volume': np.random.randint(20000, 100000),
                'put_volume': np.random.randint(20000, 100000)
            },
            'greek_sentiment': {
                'delta': trend * 0.5 + np.random.normal(0, 0.1),
                'gamma': abs(np.random.normal(0.1, 0.05)),
                'theta': np.random.normal(-0.05, 0.02),
                'vega': volatility + np.random.normal(0, 0.05)
            },
            'technical_indicators': {
                'rsi': 50 + trend * 20 + np.random.normal(0, 10),
                'macd': trend * 0.5 + np.random.normal(0, 0.2),
                'macd_signal': trend * 0.3 + np.random.normal(0, 0.1),
                'ma_signal': trend * 0.2 + np.random.normal(0, 0.1)
            },
            'atr': volatility * base_price,
            'implied_volatility': volatility
        }
    
    def _create_mock_strategy_sources(self) -> List[Dict[str, Any]]:
        """Create mock strategy sources for testing"""
        # Create mock CSV data
        mock_data = pd.DataFrame({
            'date': pd.date_range('2024-01-01', periods=100),
            'pnl': np.random.normal(10, 50, 100),
            'trades': range(100)
        })
        
        return [
            {
                'type': 'CSV',
                'path': '/tmp/mock_strategy.csv',
                'data': mock_data
            }
        ]
    
    def _record_test_result(self, test_name: str, passed: bool, details: str):
        """Record test result"""
        self.test_results['total_tests'] += 1
        
        if passed:
            self.test_results['passed_tests'] += 1
            status = "✅ PASSED"
        else:
            self.test_results['failed_tests'] += 1
            status = "❌ FAILED"
        
        self.test_results['test_details'].append({
            'test_name': test_name,
            'status': status,
            'passed': passed,
            'details': details,
            'timestamp': datetime.now().isoformat()
        })
        
        logger.info(f"   {status}: {details}")
    
    def _print_comprehensive_summary(self):
        """Print comprehensive test summary"""
        logger.info("\n" + "=" * 70)
        logger.info("🎯 COMPREHENSIVE MODULAR SYSTEM TEST SUMMARY")
        logger.info("=" * 70)
        
        # Overall results
        total = self.test_results['total_tests']
        passed = self.test_results['passed_tests']
        failed = self.test_results['failed_tests']
        success_rate = (passed / total * 100) if total > 0 else 0
        
        logger.info(f"Total Tests: {total}")
        logger.info(f"Passed: {passed}")
        logger.info(f"Failed: {failed}")
        logger.info(f"Success Rate: {success_rate:.1f}%")
        
        # Timing
        if self.test_results['start_time'] and self.test_results['end_time']:
            duration = self.test_results['end_time'] - self.test_results['start_time']
            logger.info(f"Duration: {duration.total_seconds():.1f} seconds")
        
        # Detailed results
        logger.info("\nDetailed Results:")
        for test in self.test_results['test_details']:
            logger.info(f"  {test['status']} {test['test_name']}: {test['details']}")
        
        # Final status
        if failed == 0:
            logger.info("\n🎉 ALL TESTS PASSED! Comprehensive Modular System is ready for production.")
        else:
            logger.warning(f"\n⚠️  {failed} test(s) failed. Please review and fix issues.")
        
        logger.info("=" * 70)
    
    def cleanup(self):
        """Cleanup test resources"""
        try:
            if self.system_orchestrator:
                self.system_orchestrator.stop_system()
            
            if self.live_streamer:
                self.live_streamer.stop_streaming()
            
            logger.info("Test cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

def main():
    """Main test execution"""
    parser = argparse.ArgumentParser(description='Comprehensive Modular System Test Suite')
    parser.add_argument('--duration', type=int, default=120, help='Test duration in seconds')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose logging')
    
    args = parser.parse_args()
    
    # Configure logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S'
    )
    
    # Run tests
    test_suite = ComprehensiveSystemTest(
        test_duration=args.duration,
        verbose=args.verbose
    )
    
    try:
        test_suite.run_all_tests()
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
    except Exception as e:
        logger.error(f"Test suite failed: {e}")
    finally:
        test_suite.cleanup()

if __name__ == "__main__":
    main()
