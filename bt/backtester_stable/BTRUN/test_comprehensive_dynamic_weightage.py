#!/usr/bin/env python3
"""
Comprehensive Dynamic Weightage Testing for MAXOI, COI, Delta, Volume, Vega, Theta

This script performs comprehensive testing of dynamic weightage selection with:
- Real HeavyDB data
- Actual input sheets
- All six factors: MAXOI, COI, Delta, Volume, Vega, Theta
- Live market condition adaptations
- Performance-based weight adjustments
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import date, datetime, timedelta
import logging
from typing import Dict, List, Any, Optional
import time
import json

# Add project root to path
sys.path.insert(0, '/srv/samba/shared/bt/backtester_stable/BTRUN')

# Import enhanced OI modules
from backtester_v2.strategies.oi.enhanced_models import (
    EnhancedOIConfig, EnhancedLegConfig, DynamicWeightConfig, 
    FactorConfig, PerformanceMetrics
)
from backtester_v2.strategies.oi.enhanced_parser import EnhancedOIParser
from backtester_v2.strategies.oi.dynamic_weight_engine import DynamicWeightEngine
from backtester_v2.strategies.oi.enhanced_processor import EnhancedOIProcessor
from backtester_v2.strategies.oi.unified_oi_interface import UnifiedOIInterface

# Import HeavyDB connection
try:
    from heavydb import connect
    HEAVYDB_AVAILABLE = True
except ImportError:
    HEAVYDB_AVAILABLE = False
    print("Warning: HeavyDB not available. Using mock data.")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ComprehensiveDynamicWeightageTest:
    """Comprehensive test for dynamic weightage with all six factors."""
    
    def __init__(self):
        """Initialize the comprehensive test environment."""
        self.base_dir = '/srv/samba/shared/bt/backtester_stable/BTRUN'
        self.output_dir = os.path.join(self.base_dir, 'output', 'comprehensive_dynamic_tests')
        
        # Create output directory
        os.makedirs(self.output_dir, exist_ok=True)
        
        # HeavyDB connection
        self.db_connection = self._get_heavydb_connection()
        
        # Test results
        self.test_results = {}
        
        # Factor definitions
        self.factors = {
            'MAXOI': {'type': 'OI', 'base_weight': 0.25, 'description': 'Maximum Open Interest'},
            'COI': {'type': 'COI', 'base_weight': 0.20, 'description': 'Change in Open Interest'},
            'DELTA': {'type': 'GREEK', 'base_weight': 0.18, 'description': 'Delta exposure'},
            'VOLUME': {'type': 'MARKET', 'base_weight': 0.15, 'description': 'Trading volume'},
            'VEGA': {'type': 'GREEK', 'base_weight': 0.12, 'description': 'Vega exposure'},
            'THETA': {'type': 'GREEK', 'base_weight': 0.10, 'description': 'Theta decay'}
        }
        
    def _get_heavydb_connection(self):
        """Get HeavyDB connection."""
        if not HEAVYDB_AVAILABLE:
            logger.warning("HeavyDB not available, using mock connection")
            return None
            
        try:
            conn = connect(
                host='127.0.0.1',
                port=6274,
                user='admin',
                password='HyperInteractive',
                dbname='heavyai'
            )
            logger.info("Connected to HeavyDB successfully")
            return conn
        except Exception as e:
            logger.error(f"Failed to connect to HeavyDB: {e}")
            return None
    
    def get_real_market_data(self, trade_date: str = '250102') -> pd.DataFrame:
        """Get real market data for factor analysis."""
        if not self.db_connection:
            logger.warning("No HeavyDB connection, returning mock data")
            return self._create_mock_market_data()
        
        try:
            query = f"""
            SELECT 
                trade_date,
                trade_time,
                strike,
                ce_oi,
                pe_oi,
                ce_delta,
                pe_delta,
                ce_vega,
                pe_vega,
                ce_theta,
                pe_theta,
                ce_volume,
                pe_volume,
                ce_close,
                pe_close,
                ce_iv,
                pe_iv
            FROM nifty_option_chain 
            WHERE trade_date = '{trade_date}'
            AND trade_time BETWEEN 91500 AND 152000
            AND ce_oi > 50000 
            AND pe_oi > 50000
            ORDER BY trade_time, strike
            """
            
            df = pd.read_sql(query, self.db_connection)
            logger.info(f"Retrieved {len(df)} real market data points for {trade_date}")
            return df
            
        except Exception as e:
            logger.error(f"Error getting real market data: {e}")
            return self._create_mock_market_data()
    
    def _create_mock_market_data(self) -> pd.DataFrame:
        """Create mock market data for testing when HeavyDB is not available."""
        np.random.seed(42)
        
        strikes = np.arange(23000, 25000, 50)
        times = [91500, 100000, 110000, 120000, 130000, 140000, 150000]
        
        data = []
        for time in times:
            for strike in strikes:
                data.append({
                    'trade_date': '250102',
                    'trade_time': time,
                    'strike': strike,
                    'ce_oi': np.random.randint(100000, 2000000),
                    'pe_oi': np.random.randint(100000, 2000000),
                    'ce_delta': np.random.uniform(0.1, 0.9),
                    'pe_delta': np.random.uniform(-0.9, -0.1),
                    'ce_vega': np.random.uniform(5, 50),
                    'pe_vega': np.random.uniform(5, 50),
                    'ce_theta': np.random.uniform(-10, -1),
                    'pe_theta': np.random.uniform(-10, -1),
                    'ce_volume': np.random.randint(1000, 50000),
                    'pe_volume': np.random.randint(1000, 50000),
                    'ce_close': np.random.uniform(10, 200),
                    'pe_close': np.random.uniform(10, 200),
                    'ce_iv': np.random.uniform(0.15, 0.35),
                    'pe_iv': np.random.uniform(0.15, 0.35)
                })
        
        return pd.DataFrame(data)
    
    def calculate_factor_values(self, market_data: pd.DataFrame) -> Dict[str, pd.Series]:
        """Calculate factor values from market data."""
        factor_data = {}
        
        # MAXOI Factor - Total OI ranking
        market_data['total_oi'] = market_data['ce_oi'] + market_data['pe_oi']
        market_data['maxoi_rank'] = market_data.groupby(['trade_date', 'trade_time'])['total_oi'].rank(ascending=False)
        factor_data['MAXOI'] = market_data['total_oi']
        
        # COI Factor - Change in OI
        market_data['prev_total_oi'] = market_data.groupby('strike')['total_oi'].shift(1)
        market_data['coi'] = market_data['total_oi'] - market_data['prev_total_oi'].fillna(market_data['total_oi'])
        factor_data['COI'] = market_data['coi']
        
        # DELTA Factor - Total delta exposure
        market_data['total_delta'] = abs(market_data['ce_delta'].fillna(0)) + abs(market_data['pe_delta'].fillna(0))
        factor_data['DELTA'] = market_data['total_delta']
        
        # VOLUME Factor - Total volume
        market_data['total_volume'] = market_data['ce_volume'].fillna(0) + market_data['pe_volume'].fillna(0)
        factor_data['VOLUME'] = market_data['total_volume']
        
        # VEGA Factor - Total vega exposure
        market_data['total_vega'] = market_data['ce_vega'].fillna(0) + market_data['pe_vega'].fillna(0)
        factor_data['VEGA'] = market_data['total_vega']
        
        # THETA Factor - Total theta exposure
        market_data['total_theta'] = abs(market_data['ce_theta'].fillna(0)) + abs(market_data['pe_theta'].fillna(0))
        factor_data['THETA'] = market_data['total_theta']
        
        return factor_data
    
    def test_factor_performance_analysis(self) -> Dict[str, Any]:
        """Test factor performance analysis with real data."""
        logger.info("🔍 Testing factor performance analysis...")
        
        results = {
            'test_name': 'Factor Performance Analysis',
            'status': 'RUNNING',
            'details': {}
        }
        
        try:
            # Get real market data
            market_data = self.get_real_market_data()
            
            if market_data.empty:
                results['status'] = 'FAILED'
                results['error'] = 'No market data available'
                return results
            
            # Calculate factor values
            factor_data = self.calculate_factor_values(market_data)
            
            # Analyze each factor
            factor_analysis = {}
            
            for factor_name, factor_series in factor_data.items():
                if len(factor_series.dropna()) > 10:
                    factor_analysis[factor_name] = {
                        'mean': float(factor_series.mean()),
                        'std': float(factor_series.std()),
                        'min': float(factor_series.min()),
                        'max': float(factor_series.max()),
                        'count': len(factor_series.dropna()),
                        'stability': float(1.0 - (factor_series.std() / factor_series.mean())) if factor_series.mean() != 0 else 0.0,
                        'data_quality': float(factor_series.notna().sum() / len(factor_series))
                    }
                    
                    logger.info(f"✅ {factor_name}: Mean={factor_analysis[factor_name]['mean']:.2f}, "
                              f"Stability={factor_analysis[factor_name]['stability']:.3f}, "
                              f"Quality={factor_analysis[factor_name]['data_quality']:.3f}")
            
            results['details']['factor_analysis'] = factor_analysis
            results['details']['data_points'] = len(market_data)
            results['status'] = 'PASSED'
            
        except Exception as e:
            results['status'] = 'FAILED'
            results['error'] = str(e)
            logger.error(f"Factor performance analysis failed: {e}")
        
        return results
    
    def test_dynamic_weight_engine_comprehensive(self) -> Dict[str, Any]:
        """Test comprehensive dynamic weight engine with all six factors."""
        logger.info("⚖️ Testing comprehensive dynamic weight engine...")
        
        results = {
            'test_name': 'Comprehensive Dynamic Weight Engine',
            'status': 'RUNNING',
            'details': {}
        }
        
        try:
            # Create factor configurations for all six factors
            factor_configs = []
            for factor_name, factor_info in self.factors.items():
                factor_configs.append(FactorConfig(
                    factor_name=f'{factor_name.lower()}_factor',
                    factor_type=factor_info['type'],
                    base_weight=factor_info['base_weight'],
                    min_weight=0.05,
                    max_weight=0.40
                ))
            
            # Create dynamic weight configuration
            weight_config = DynamicWeightConfig(
                oi_factor_weight=0.45,  # MAXOI + COI
                greek_factor_weight=0.40,  # DELTA + VEGA + THETA
                market_factor_weight=0.15,  # VOLUME
                weight_learning_rate=0.02,
                performance_threshold=0.65,
                weight_rebalance_freq=300,  # 5 minutes
                correlation_threshold=0.7
            )
            
            # Initialize weight engine
            engine = DynamicWeightEngine(weight_config, factor_configs)
            
            # Test initial weights
            initial_weights = engine.get_current_weights()
            results['details']['initial_weights'] = initial_weights
            results['details']['initial_weight_sum'] = sum(initial_weights.values())
            
            logger.info(f"Initial weights: {initial_weights}")
            logger.info(f"Initial weight sum: {sum(initial_weights.values()):.3f}")
            
            # Test multiple market scenarios
            market_scenarios = [
                {
                    'name': 'High Volatility Market',
                    'performance_data': {
                        'maxoi_factor': 0.60,
                        'coi_factor': 0.55,
                        'delta_factor': 0.85,
                        'volume_factor': 0.70,
                        'vega_factor': 0.90,
                        'theta_factor': 0.45
                    },
                    'market_conditions': {
                        'volatility': 0.85,
                        'trend_strength': 0.30,
                        'liquidity': 0.60,
                        'regime': 'high_volatility'
                    }
                },
                {
                    'name': 'Trending Market',
                    'performance_data': {
                        'maxoi_factor': 0.75,
                        'coi_factor': 0.80,
                        'delta_factor': 0.70,
                        'volume_factor': 0.85,
                        'vega_factor': 0.40,
                        'theta_factor': 0.35
                    },
                    'market_conditions': {
                        'volatility': 0.40,
                        'trend_strength': 0.90,
                        'liquidity': 0.85,
                        'regime': 'trending'
                    }
                },
                {
                    'name': 'Sideways Market',
                    'performance_data': {
                        'maxoi_factor': 0.85,
                        'coi_factor': 0.45,
                        'delta_factor': 0.50,
                        'volume_factor': 0.40,
                        'vega_factor': 0.35,
                        'theta_factor': 0.80
                    },
                    'market_conditions': {
                        'volatility': 0.25,
                        'trend_strength': 0.10,
                        'liquidity': 0.75,
                        'regime': 'sideways'
                    }
                }
            ]
            
            scenario_results = {}
            
            for scenario in market_scenarios:
                logger.info(f"Testing scenario: {scenario['name']}")
                
                # Update weights based on scenario
                updated_weights = engine.update_weights(
                    scenario['performance_data'],
                    scenario['market_conditions']
                )
                
                scenario_results[scenario['name']] = {
                    'updated_weights': updated_weights,
                    'weight_sum': sum(updated_weights.values()),
                    'weight_changes': {
                        factor: updated_weights.get(factor, 0) - initial_weights.get(factor, 0)
                        for factor in initial_weights.keys()
                    },
                    'performance_data': scenario['performance_data'],
                    'market_conditions': scenario['market_conditions']
                }
                
                # Log significant weight changes
                for factor, change in scenario_results[scenario['name']]['weight_changes'].items():
                    if abs(change) > 0.01:
                        logger.info(f"  📊 {factor}: {change:+.4f} ({updated_weights.get(factor, 0):.3f})")
            
            results['details']['scenarios'] = scenario_results
            results['status'] = 'PASSED'
            
            logger.info("✅ Comprehensive dynamic weight engine test passed")
            
        except Exception as e:
            results['status'] = 'FAILED'
            results['error'] = str(e)
            logger.error(f"Comprehensive dynamic weight engine test failed: {e}")
        
        return results

    def run_comprehensive_tests(self) -> Dict[str, Any]:
        """Run all comprehensive dynamic weightage tests."""
        logger.info("🎯 Starting comprehensive dynamic weightage testing...")
        logger.info("=" * 80)
        logger.info("TESTING DYNAMIC WEIGHTAGE FOR:")
        for factor_name, factor_info in self.factors.items():
            logger.info(f"  📊 {factor_name}: {factor_info['description']} (Base: {factor_info['base_weight']:.2f})")
        logger.info("=" * 80)

        start_time = time.time()

        # Run all tests
        test_methods = [
            self.test_factor_performance_analysis,
            self.test_dynamic_weight_engine_comprehensive
        ]

        all_results = {
            'test_suite': 'Comprehensive Dynamic Weightage Tests',
            'factors_tested': self.factors,
            'start_time': datetime.now().isoformat(),
            'tests': {},
            'summary': {}
        }

        passed_tests = 0
        failed_tests = 0

        for test_method in test_methods:
            try:
                test_result = test_method()
                test_name = test_result['test_name']
                all_results['tests'][test_name] = test_result

                if test_result['status'] == 'PASSED':
                    passed_tests += 1
                    logger.info(f"✅ {test_name}: PASSED")
                else:
                    failed_tests += 1
                    logger.error(f"❌ {test_name}: FAILED - {test_result.get('error', 'Unknown error')}")

            except Exception as e:
                failed_tests += 1
                logger.error(f"❌ {test_method.__name__}: EXCEPTION - {e}")
                all_results['tests'][test_method.__name__] = {
                    'test_name': test_method.__name__,
                    'status': 'EXCEPTION',
                    'error': str(e)
                }

        end_time = time.time()
        execution_time = end_time - start_time

        # Summary
        all_results['summary'] = {
            'total_tests': len(test_methods),
            'passed': passed_tests,
            'failed': failed_tests,
            'execution_time_seconds': execution_time,
            'success_rate': (passed_tests / len(test_methods)) * 100 if len(test_methods) > 0 else 0,
            'heavydb_available': self.db_connection is not None
        }

        all_results['end_time'] = datetime.now().isoformat()

        # Save results
        results_file = os.path.join(self.output_dir, f'comprehensive_dynamic_weightage_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
        with open(results_file, 'w') as f:
            json.dump(all_results, f, indent=2, default=str)

        # Print comprehensive summary
        logger.info("=" * 80)
        logger.info("🏁 COMPREHENSIVE DYNAMIC WEIGHTAGE TESTING SUMMARY")
        logger.info("=" * 80)
        logger.info(f"📊 Factors Tested: {', '.join(self.factors.keys())}")
        logger.info(f"🔗 HeavyDB Connection: {'✅ Available' if self.db_connection else '❌ Not Available'}")
        logger.info(f"📈 Total Tests: {all_results['summary']['total_tests']}")
        logger.info(f"✅ Passed: {all_results['summary']['passed']}")
        logger.info(f"❌ Failed: {all_results['summary']['failed']}")
        logger.info(f"⏱️  Execution Time: {execution_time:.2f} seconds")
        logger.info(f"📊 Success Rate: {all_results['summary']['success_rate']:.1f}%")
        logger.info(f"📁 Results saved to: {results_file}")
        logger.info("=" * 80)

        return all_results


if __name__ == "__main__":
    """Run the comprehensive dynamic weightage tests."""
    print("🎯 Comprehensive Dynamic Weightage Testing")
    print("Testing: MAXOI, COI, Delta, Volume, Vega, Theta")
    print("=" * 60)

    # Initialize test suite
    test_suite = ComprehensiveDynamicWeightageTest()

    # Run all tests
    results = test_suite.run_comprehensive_tests()

    # Exit with appropriate code
    if results['summary']['failed'] > 0:
        print("❌ Some tests failed!")
        sys.exit(1)
    else:
        print("✅ All comprehensive tests completed successfully!")
        sys.exit(0)
