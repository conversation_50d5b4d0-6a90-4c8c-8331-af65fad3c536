"""
Market Regime Detection and Classification Module

This module provides comprehensive market regime analysis capabilities
integrated with the backtester_v2 architecture.

Key Features:
- Multi-timeframe regime detection
- Dynamic indicator weighting
- Performance-based adaptation
- GPU-accelerated calculations
- Real-time regime updates
- Excel configuration support
"""

from .models import (
    RegimeConfig,
    IndicatorConfig,
    RegimeClassification,
    RegimeType,
    PerformanceMetrics
)

from .strategy import MarketRegimeStrategy
from .parser import RegimeConfigParser
from .processor import RegimeProcessor
from .calculator import RegimeCalculator
from .classifier import RegimeClassifier

__version__ = "1.0.0"
__author__ = "Backtester V2 Team"

__all__ = [
    'RegimeConfig',
    'IndicatorConfig', 
    'RegimeClassification',
    'RegimeType',
    'PerformanceMetrics',
    'MarketRegimeStrategy',
    'RegimeConfigParser',
    'RegimeProcessor',
    'RegimeCalculator',
    'RegimeClassifier'
]
