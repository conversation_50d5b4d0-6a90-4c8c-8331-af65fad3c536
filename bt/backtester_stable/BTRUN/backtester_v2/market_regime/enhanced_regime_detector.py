"""
Enhanced 18-Regime Market Regime Detector

This module implements the comprehensive 18-regime market detection system
integrating with the existing backtester_v2 architecture and providing
real-time regime classification for live trading.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from enum import Enum
import logging

# Import existing backtester_v2 components
from .models import RegimeClassification, RegimeType
from .processor import RegimeProcessor

logger = logging.getLogger(__name__)

class Enhanced18RegimeType(Enum):
    """18 Enhanced Market Regime Types"""
    
    # Bullish Regimes (6)
    HIGH_VOLATILE_STRONG_BULLISH = "High_Volatile_Strong_Bullish"
    NORMAL_VOLATILE_STRONG_BULLISH = "Normal_Volatile_Strong_Bullish"
    LOW_VOLATILE_STRONG_BULLISH = "Low_Volatile_Strong_Bullish"
    HIGH_VOLATILE_MILD_BULLISH = "High_Volatile_Mild_Bullish"
    NORMAL_VOLATILE_MILD_BULLISH = "Normal_Volatile_Mild_Bullish"
    LOW_VOLATILE_MILD_BULLISH = "Low_Volatile_Mild_Bullish"
    
    # Neutral/Sideways Regimes (6)
    HIGH_VOLATILE_NEUTRAL = "High_Volatile_Neutral"
    NORMAL_VOLATILE_NEUTRAL = "Normal_Volatile_Neutral"
    LOW_VOLATILE_NEUTRAL = "Low_Volatile_Neutral"
    HIGH_VOLATILE_SIDEWAYS = "High_Volatile_Sideways"
    NORMAL_VOLATILE_SIDEWAYS = "Normal_Volatile_Sideways"
    LOW_VOLATILE_SIDEWAYS = "Low_Volatile_Sideways"
    
    # Bearish Regimes (6)
    HIGH_VOLATILE_MILD_BEARISH = "High_Volatile_Mild_Bearish"
    NORMAL_VOLATILE_MILD_BEARISH = "Normal_Volatile_Mild_Bearish"
    LOW_VOLATILE_MILD_BEARISH = "Low_Volatile_Mild_Bearish"
    HIGH_VOLATILE_STRONG_BEARISH = "High_Volatile_Strong_Bearish"
    NORMAL_VOLATILE_STRONG_BEARISH = "Normal_Volatile_Strong_Bearish"
    LOW_VOLATILE_STRONG_BEARISH = "Low_Volatile_Strong_Bearish"

class Enhanced18RegimeDetector:
    """
    Enhanced 18-Regime Market Detector
    
    This class implements comprehensive market regime detection using 18 distinct
    regime types, integrating Greek sentiment, OI analysis, technical indicators,
    and volatility measures for precise market state classification.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize Enhanced 18-Regime Detector
        
        Args:
            config (Dict, optional): Configuration parameters
        """
        self.config = config or {}
        
        # Regime classification thresholds
        self.directional_thresholds = {
            'strong_bullish': 0.50,
            'mild_bullish': 0.20,
            'neutral': 0.10,
            'sideways': 0.05,
            'mild_bearish': -0.20,
            'strong_bearish': -0.50
        }
        
        # Volatility classification thresholds
        self.volatility_thresholds = {
            'high': 0.20,
            'normal_high': 0.15,
            'normal_low': 0.10,
            'low': 0.05
        }
        
        # Indicator weights for regime detection
        self.indicator_weights = {
            'greek_sentiment': 0.35,
            'oi_analysis': 0.25,
            'price_action': 0.20,
            'technical_indicators': 0.15,
            'volatility_measures': 0.05
        }
        
        # Historical regime data for learning
        self.regime_history = []
        self.performance_tracking = {}
        
        # Integration with existing regime processor
        self.base_processor = RegimeProcessor(
            db_connection=None,
            regime_config=None
        )
        
        logger.info("Enhanced 18-Regime Detector initialized")
    
    def detect_regime(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Detect current market regime from 18 possible states
        
        Args:
            market_data (Dict): Market data including OHLC, OI, Greeks, etc.
            
        Returns:
            Dict: Regime detection results
        """
        try:
            # Step 1: Calculate directional component
            directional_component = self._calculate_directional_component(market_data)
            
            # Step 2: Calculate volatility component
            volatility_component = self._calculate_volatility_component(market_data)
            
            # Step 3: Classify into 18 regimes
            regime_classification = self._classify_18_regimes(
                directional_component, volatility_component
            )
            
            # Step 4: Calculate confidence score
            confidence = self._calculate_confidence_score(
                directional_component, volatility_component, market_data
            )
            
            # Step 5: Create regime result
            regime_result = {
                'regime_type': regime_classification,
                'confidence': confidence,
                'timestamp': datetime.now(),
                'components': {
                    'directional': directional_component,
                    'volatility': volatility_component
                },
                'market_data_summary': self._summarize_market_data(market_data),
                'regime_strength': self._calculate_regime_strength(
                    directional_component, volatility_component
                )
            }
            
            # Step 6: Update regime history
            self._update_regime_history(regime_result)
            
            logger.debug(f"Detected regime: {regime_classification} (confidence: {confidence:.2f})")
            
            return regime_result
            
        except Exception as e:
            logger.error(f"Error in regime detection: {e}")
            return self._get_default_regime_result()
    
    def _calculate_directional_component(self, market_data: Dict[str, Any]) -> float:
        """Calculate directional component from market data"""
        try:
            directional_score = 0.0
            total_weight = 0.0
            
            # Greek sentiment component
            if 'greek_sentiment' in market_data:
                greek_score = self._process_greek_sentiment(market_data['greek_sentiment'])
                directional_score += greek_score * self.indicator_weights['greek_sentiment']
                total_weight += self.indicator_weights['greek_sentiment']
            
            # OI analysis component
            if 'oi_data' in market_data:
                oi_score = self._process_oi_analysis(market_data['oi_data'])
                directional_score += oi_score * self.indicator_weights['oi_analysis']
                total_weight += self.indicator_weights['oi_analysis']
            
            # Price action component
            if 'price_data' in market_data:
                price_score = self._process_price_action(market_data['price_data'])
                directional_score += price_score * self.indicator_weights['price_action']
                total_weight += self.indicator_weights['price_action']
            
            # Technical indicators component
            if 'technical_indicators' in market_data:
                tech_score = self._process_technical_indicators(market_data['technical_indicators'])
                directional_score += tech_score * self.indicator_weights['technical_indicators']
                total_weight += self.indicator_weights['technical_indicators']
            
            # Normalize by total weight
            if total_weight > 0:
                directional_score /= total_weight
            
            return np.clip(directional_score, -1.0, 1.0)
            
        except Exception as e:
            logger.error(f"Error calculating directional component: {e}")
            return 0.0
    
    def _calculate_volatility_component(self, market_data: Dict[str, Any]) -> float:
        """Calculate volatility component from market data"""
        try:
            volatility_measures = []
            
            # ATR-based volatility
            if 'atr' in market_data:
                atr_vol = market_data['atr'] / market_data.get('price', 1.0)
                volatility_measures.append(atr_vol)
            
            # IV-based volatility
            if 'implied_volatility' in market_data:
                iv_vol = market_data['implied_volatility']
                volatility_measures.append(iv_vol)
            
            # Price volatility
            if 'price_data' in market_data and len(market_data['price_data']) > 1:
                price_returns = np.diff(market_data['price_data']) / market_data['price_data'][:-1]
                price_vol = np.std(price_returns) * np.sqrt(252)  # Annualized
                volatility_measures.append(price_vol)
            
            # OI volatility
            if 'oi_data' in market_data and len(market_data['oi_data']) > 1:
                oi_changes = np.diff(market_data['oi_data']) / market_data['oi_data'][:-1]
                oi_vol = np.std(oi_changes)
                volatility_measures.append(oi_vol)
            
            # Calculate average volatility
            if volatility_measures:
                avg_volatility = np.mean(volatility_measures)
                return np.clip(avg_volatility, 0.0, 1.0)
            
            return 0.1  # Default normal volatility
            
        except Exception as e:
            logger.error(f"Error calculating volatility component: {e}")
            return 0.1
    
    def _classify_18_regimes(self, directional: float, volatility: float) -> Enhanced18RegimeType:
        """Classify market into one of 18 regime types"""
        try:
            # Determine volatility category
            if volatility >= self.volatility_thresholds['high']:
                vol_category = 'HIGH'
            elif volatility >= self.volatility_thresholds['normal_high']:
                vol_category = 'NORMAL'
            else:
                vol_category = 'LOW'
            
            # Determine directional category
            if directional >= self.directional_thresholds['strong_bullish']:
                dir_category = 'STRONG_BULLISH'
            elif directional >= self.directional_thresholds['mild_bullish']:
                dir_category = 'MILD_BULLISH'
            elif directional >= self.directional_thresholds['neutral']:
                dir_category = 'NEUTRAL'
            elif directional >= self.directional_thresholds['sideways']:
                dir_category = 'SIDEWAYS'
            elif directional >= self.directional_thresholds['mild_bearish']:
                dir_category = 'MILD_BEARISH'
            else:
                dir_category = 'STRONG_BEARISH'
            
            # Combine to get regime type
            regime_name = f"{vol_category}_VOLATILE_{dir_category}"
            
            # Map to enum
            for regime_type in Enhanced18RegimeType:
                if regime_type.value.upper().replace(' ', '_') == regime_name:
                    return regime_type
            
            # Default fallback
            return Enhanced18RegimeType.NORMAL_VOLATILE_NEUTRAL
            
        except Exception as e:
            logger.error(f"Error classifying regime: {e}")
            return Enhanced18RegimeType.NORMAL_VOLATILE_NEUTRAL
    
    def _calculate_confidence_score(self, directional: float, volatility: float, 
                                  market_data: Dict[str, Any]) -> float:
        """Calculate confidence score for regime classification"""
        try:
            confidence_factors = []
            
            # Data completeness factor
            available_indicators = sum([
                'greek_sentiment' in market_data,
                'oi_data' in market_data,
                'price_data' in market_data,
                'technical_indicators' in market_data,
                'implied_volatility' in market_data
            ])
            data_completeness = available_indicators / 5.0
            confidence_factors.append(data_completeness)
            
            # Signal strength factor
            signal_strength = min(abs(directional), 1.0)
            confidence_factors.append(signal_strength)
            
            # Volatility consistency factor
            vol_consistency = 1.0 - abs(volatility - 0.15) / 0.15  # Normalize around 15%
            vol_consistency = max(0.0, vol_consistency)
            confidence_factors.append(vol_consistency)
            
            # Historical consistency factor
            if len(self.regime_history) > 0:
                recent_regimes = [r['regime_type'] for r in self.regime_history[-5:]]
                current_regime = self._classify_18_regimes(directional, volatility)
                consistency = sum(1 for r in recent_regimes if r == current_regime) / len(recent_regimes)
                confidence_factors.append(consistency)
            
            # Calculate weighted average confidence
            confidence = np.mean(confidence_factors)
            return np.clip(confidence, 0.0, 1.0)
            
        except Exception as e:
            logger.error(f"Error calculating confidence: {e}")
            return 0.5
    
    def _process_greek_sentiment(self, greek_data: Dict[str, Any]) -> float:
        """Process Greek sentiment data into directional score"""
        try:
            # Extract key Greeks
            delta = greek_data.get('delta', 0.0)
            gamma = greek_data.get('gamma', 0.0)
            theta = greek_data.get('theta', 0.0)
            vega = greek_data.get('vega', 0.0)
            
            # Calculate sentiment score
            sentiment_score = 0.0
            
            # Delta contribution (directional exposure)
            sentiment_score += delta * 0.4
            
            # Gamma contribution (convexity)
            sentiment_score += np.sign(delta) * gamma * 0.3
            
            # Theta contribution (time decay)
            sentiment_score -= theta * 0.2  # Negative theta is bullish for sellers
            
            # Vega contribution (volatility exposure)
            sentiment_score += vega * 0.1
            
            return np.clip(sentiment_score, -1.0, 1.0)
            
        except Exception as e:
            logger.error(f"Error processing Greek sentiment: {e}")
            return 0.0
    
    def _process_oi_analysis(self, oi_data: Dict[str, Any]) -> float:
        """Process OI analysis data into directional score"""
        try:
            # Extract OI metrics
            call_oi = oi_data.get('call_oi', 0)
            put_oi = oi_data.get('put_oi', 0)
            call_volume = oi_data.get('call_volume', 0)
            put_volume = oi_data.get('put_volume', 0)
            
            # Calculate OI ratios
            if put_oi > 0:
                pcr_oi = call_oi / put_oi
            else:
                pcr_oi = 1.0
            
            if put_volume > 0:
                pcr_volume = call_volume / put_volume
            else:
                pcr_volume = 1.0
            
            # Convert to directional score
            # PCR > 1 = bullish, PCR < 1 = bearish
            oi_score = (pcr_oi - 1.0) * 0.5 + (pcr_volume - 1.0) * 0.5
            
            return np.clip(oi_score, -1.0, 1.0)
            
        except Exception as e:
            logger.error(f"Error processing OI analysis: {e}")
            return 0.0
    
    def _process_price_action(self, price_data: List[float]) -> float:
        """Process price action data into directional score"""
        try:
            if len(price_data) < 2:
                return 0.0
            
            # Calculate price momentum
            recent_prices = price_data[-10:]  # Last 10 periods
            if len(recent_prices) < 2:
                return 0.0
            
            # Simple momentum calculation
            momentum = (recent_prices[-1] - recent_prices[0]) / recent_prices[0]
            
            # Normalize momentum
            momentum_score = np.tanh(momentum * 10)  # Scale and bound
            
            return np.clip(momentum_score, -1.0, 1.0)
            
        except Exception as e:
            logger.error(f"Error processing price action: {e}")
            return 0.0
    
    def _process_technical_indicators(self, tech_data: Dict[str, Any]) -> float:
        """Process technical indicators into directional score"""
        try:
            indicator_scores = []
            
            # RSI
            if 'rsi' in tech_data:
                rsi = tech_data['rsi']
                rsi_score = (rsi - 50) / 50  # Normalize around 50
                indicator_scores.append(rsi_score)
            
            # MACD
            if 'macd' in tech_data and 'macd_signal' in tech_data:
                macd_diff = tech_data['macd'] - tech_data['macd_signal']
                macd_score = np.tanh(macd_diff)  # Normalize
                indicator_scores.append(macd_score)
            
            # Moving Average
            if 'ma_signal' in tech_data:
                ma_score = tech_data['ma_signal']  # Assume already normalized
                indicator_scores.append(ma_score)
            
            # Calculate average
            if indicator_scores:
                avg_score = np.mean(indicator_scores)
                return np.clip(avg_score, -1.0, 1.0)
            
            return 0.0
            
        except Exception as e:
            logger.error(f"Error processing technical indicators: {e}")
            return 0.0
    
    def _calculate_regime_strength(self, directional: float, volatility: float) -> float:
        """Calculate overall regime strength"""
        try:
            # Combine directional and volatility components
            directional_strength = abs(directional)
            volatility_strength = min(volatility / 0.3, 1.0)  # Normalize volatility
            
            # Weighted combination
            regime_strength = directional_strength * 0.7 + volatility_strength * 0.3
            
            return np.clip(regime_strength, 0.0, 1.0)
            
        except Exception as e:
            logger.error(f"Error calculating regime strength: {e}")
            return 0.5
    
    def _summarize_market_data(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create summary of market data for logging"""
        try:
            summary = {
                'data_sources': list(market_data.keys()),
                'timestamp': datetime.now().isoformat(),
                'data_quality': self._assess_data_quality(market_data)
            }
            
            # Add key metrics if available
            if 'price_data' in market_data and market_data['price_data']:
                summary['current_price'] = market_data['price_data'][-1]
            
            if 'implied_volatility' in market_data:
                summary['implied_volatility'] = market_data['implied_volatility']
            
            return summary
            
        except Exception as e:
            logger.error(f"Error summarizing market data: {e}")
            return {'error': str(e)}
    
    def _assess_data_quality(self, market_data: Dict[str, Any]) -> str:
        """Assess quality of input market data"""
        try:
            required_fields = ['price_data', 'oi_data', 'greek_sentiment']
            available_fields = sum(1 for field in required_fields if field in market_data)
            
            quality_ratio = available_fields / len(required_fields)
            
            if quality_ratio >= 0.8:
                return 'HIGH'
            elif quality_ratio >= 0.6:
                return 'MEDIUM'
            else:
                return 'LOW'
                
        except Exception as e:
            logger.error(f"Error assessing data quality: {e}")
            return 'UNKNOWN'
    
    def _update_regime_history(self, regime_result: Dict[str, Any]):
        """Update regime history for learning"""
        try:
            self.regime_history.append(regime_result)
            
            # Keep only recent history (last 1000 entries)
            if len(self.regime_history) > 1000:
                self.regime_history = self.regime_history[-1000:]
                
        except Exception as e:
            logger.error(f"Error updating regime history: {e}")
    
    def _get_default_regime_result(self) -> Dict[str, Any]:
        """Get default regime result for error cases"""
        return {
            'regime_type': Enhanced18RegimeType.NORMAL_VOLATILE_NEUTRAL,
            'confidence': 0.0,
            'timestamp': datetime.now(),
            'components': {
                'directional': 0.0,
                'volatility': 0.1
            },
            'market_data_summary': {'error': 'Failed to process market data'},
            'regime_strength': 0.0
        }
    
    def get_regime_statistics(self) -> Dict[str, Any]:
        """Get regime detection statistics"""
        try:
            if not self.regime_history:
                return {'message': 'No regime history available'}
            
            # Calculate regime distribution
            regime_counts = {}
            for regime_data in self.regime_history:
                regime_type = regime_data['regime_type'].value
                regime_counts[regime_type] = regime_counts.get(regime_type, 0) + 1
            
            # Calculate average confidence
            avg_confidence = np.mean([r['confidence'] for r in self.regime_history])
            
            # Calculate regime transitions
            transitions = 0
            for i in range(1, len(self.regime_history)):
                if self.regime_history[i]['regime_type'] != self.regime_history[i-1]['regime_type']:
                    transitions += 1
            
            return {
                'total_classifications': len(self.regime_history),
                'regime_distribution': regime_counts,
                'average_confidence': avg_confidence,
                'regime_transitions': transitions,
                'transition_rate': transitions / len(self.regime_history) if self.regime_history else 0
            }
            
        except Exception as e:
            logger.error(f"Error getting regime statistics: {e}")
            return {'error': str(e)}
