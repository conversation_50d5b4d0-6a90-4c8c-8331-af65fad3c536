# Excel-Based Market Regime System Integration

## Overview

This system provides Excel-based configuration for the **actual existing market regime system** located at `/srv/samba/shared/enhanced-market-regime-optimizer-final-package-updated/`. It creates a bridge between user-friendly Excel configuration and the comprehensive indicator engine that's already implemented.

## Key Features

### 1. **Integration with Actual Existing System**
- Connects to the real `ComprehensiveIndicatorEngine` and `DynamicWeightageIntegrator`
- Uses existing indicator systems: Greek Sentiment, Trending OI PA, EMA, VWAP, IV Skew, ATR
- Maintains all existing functionality while adding Excel configuration

### 2. **ATM Straddle Analysis with Multi-Timeframe Support**
- **ATM/ITM/OTM Straddle Analysis**: ATM, ITM1, ITM2, ITM3, OTM1, OTM2, OTM3
- **EMA Integration**: 20, 50, 200 period EMAs for straddle prices
- **VWAP Integration**: Current day, previous day, weekly VWAP
- **Multi-Timeframe**: 3min, 5min, 10min, 15min analysis
- **Previous Day VWAP**: Integrated across all timeframes

### 3. **Dynamic Weightage System**
- Performance-based weight adjustment
- Historical performance tracking
- Learning rate configuration
- Min/max weight bounds
- Auto-normalization

### 4. **Comprehensive Excel Configuration**
- **6 Configuration Sheets**: Indicators, Straddles, Dynamic Weights, Timeframes, Greek Sentiment, Regime Formation
- **Template Generation**: Auto-generates Excel templates
- **Validation**: Built-in configuration validation
- **Hot Reload**: Configuration can be reloaded without restart

## File Structure

```
bt/backtester_stable/BTRUN/backtester_v2/market_regime/
├── actual_system_excel_manager.py      # Excel configuration management
├── actual_system_integrator.py         # Integration with existing system
├── excel_based_regime_engine.py        # Main engine
├── test_actual_system_integration.py   # Comprehensive tests
└── README_Excel_Integration.md         # This documentation
```

## Excel Configuration Sheets

### 1. IndicatorConfiguration
Configures all existing indicator systems:

| Column | Description | Example |
|--------|-------------|---------|
| IndicatorSystem | System name | greek_sentiment |
| Enabled | Enable/disable | True |
| BaseWeight | Base weight | 0.20 |
| PerformanceTracking | Track performance | True |
| AdaptiveWeight | Allow weight adaptation | True |
| ConfigSection | INI section name | greek_sentiment |
| Parameters | System parameters | lookback_period=20,gamma_threshold=0.7 |
| Description | System description | Greek sentiment analysis |

### 2. StraddleAnalysisConfig
Configures straddle analysis with EMA/VWAP integration:

| Column | Description | Example |
|--------|-------------|---------|
| StraddleType | Straddle type | ATM_STRADDLE |
| Enabled | Enable/disable | True |
| Weight | Straddle weight | 0.4 |
| EMAEnabled | Enable EMA analysis | True |
| EMAPeriods | EMA periods | 20,50,200 |
| VWAPEnabled | Enable VWAP analysis | True |
| VWAPTypes | VWAP types | current_day,previous_day,weekly |
| PreviousDayVWAP | Include previous day VWAP | True |
| Timeframes | Analysis timeframes | 3m,5m,10m,15m |
| Description | Description | ATM straddle with EMA and VWAP |

### 3. DynamicWeightageConfig
Configures dynamic weight adjustment:

| Column | Description | Example |
|--------|-------------|---------|
| SystemName | System name | greek_sentiment |
| CurrentWeight | Current weight | 0.20 |
| HistoricalPerformance | Historical performance | 0.85 |
| LearningRate | Learning rate | 0.01 |
| MinWeight | Minimum weight | 0.05 |
| MaxWeight | Maximum weight | 0.50 |
| PerformanceWindow | Performance window | 30 |
| UpdateFrequency | Update frequency | daily |
| AutoAdjust | Auto-adjust weights | True |

### 4. MultiTimeframeConfig
Configures multi-timeframe analysis:

| Column | Description | Example |
|--------|-------------|---------|
| Timeframe | Timeframe | 5min |
| Enabled | Enable/disable | True |
| Weight | Timeframe weight | 0.35 |
| Primary | Primary timeframe | True |
| ConfirmationRequired | Require confirmation | True |
| RegimeStability | Regime stability factor | 0.4 |
| TransitionSensitivity | Transition sensitivity | 0.6 |
| Description | Description | Primary timeframe for regime analysis |

### 5. GreekSentimentConfig
Configures Greek sentiment analysis parameters:

| Column | Description | Example |
|--------|-------------|---------|
| Parameter | Parameter name | lookback_period |
| Value | Parameter value | 20 |
| Description | Parameter description | Lookback period for Greek analysis |
| Type | Value type | int |
| MinValue | Minimum value | 5 |
| MaxValue | Maximum value | 50 |

### 6. RegimeFormationConfig
Configures regime formation rules:

| Column | Description | Example |
|--------|-------------|---------|
| RegimeType | Regime type | STRONG_BULLISH |
| DirectionalThreshold | Directional threshold | 0.70 |
| VolatilityThreshold | Volatility threshold | 0.25 |
| ConfidenceThreshold | Confidence threshold | 0.80 |
| MinDuration | Minimum duration | 5 |
| Enabled | Enable/disable | True |
| Description | Description | Strong bullish market regime |

## Usage Examples

### 1. Basic Usage

```python
from excel_based_regime_engine import ExcelBasedRegimeEngine

# Initialize engine (auto-generates template if needed)
engine = ExcelBasedRegimeEngine()

# Or with existing configuration
engine = ExcelBasedRegimeEngine("my_config.xlsx")

# Calculate market regime
market_regime = engine.calculate_market_regime(market_data)
```

### 2. Generate Configuration Template

```python
from actual_system_excel_manager import ActualSystemExcelManager

# Generate template
manager = ActualSystemExcelManager()
template_path = manager.generate_excel_template("market_regime_config.xlsx")
print(f"Template generated: {template_path}")
```

### 3. Load and Validate Configuration

```python
# Load configuration
manager = ActualSystemExcelManager("market_regime_config.xlsx")
manager.load_configuration()

# Validate configuration
is_valid, errors = manager.validate_configuration()
if not is_valid:
    print("Configuration errors:", errors)
```

### 4. Update Weights Based on Performance

```python
# Performance data (0.0 to 1.0, where 0.5 is neutral)
performance_data = {
    'greek_sentiment': 0.75,
    'trending_oi_pa': 0.68,
    'ema_indicators': 0.82,
    'straddle_analysis': 0.85
}

# Update weights
engine.update_weights_from_performance(performance_data)
```

### 5. Get Engine Status

```python
status = engine.get_engine_status()
print("Engine Status:")
for key, value in status.items():
    print(f"  {key}: {value}")
```

## Integration with Backtester V2

### 1. Import in Backtester

```python
from backtester_v2.market_regime.excel_based_regime_engine import ExcelBasedRegimeEngine

# Initialize in backtester
regime_engine = ExcelBasedRegimeEngine("config/market_regime_config.xlsx")
```

### 2. Calculate Regime in Strategy

```python
def calculate_market_regime(self, market_data):
    """Calculate market regime using Excel configuration"""
    regime_results = self.regime_engine.calculate_market_regime(market_data)
    
    if not regime_results.empty:
        current_regime = regime_results['Market_Regime_Label'].iloc[-1]
        regime_confidence = regime_results['Market_Regime_Confidence'].iloc[-1]
        
        return {
            'regime': current_regime,
            'confidence': regime_confidence,
            'straddle_score': regime_results.get('Straddle_Composite_Score', pd.Series([0])).iloc[-1],
            'timeframe_consensus': regime_results.get('Timeframe_Consensus', pd.Series([0])).iloc[-1]
        }
    
    return None
```

## Testing

### Run Comprehensive Tests

```bash
cd bt/backtester_stable/BTRUN/backtester_v2/market_regime/
python test_actual_system_integration.py
```

### Expected Test Output

```
🚀 Starting Comprehensive Actual System Integration Test
========================================
=== Testing Excel Manager ===
✅ Excel Manager test completed successfully

========================================
=== Testing System Integrator ===
✅ System Integrator test completed successfully

========================================
=== Testing Straddle Analysis Integration ===
✅ Straddle Analysis Integration test completed successfully

🏁 TEST SUMMARY
========================================
Excel Manager: ✅ PASSED
System Integrator: ✅ PASSED
Straddle Analysis: ✅ PASSED

Overall Result: 3/3 tests passed
🎉 ALL TESTS PASSED!
```

## Configuration Best Practices

### 1. Weight Configuration
- Ensure dynamic weights sum to 1.0
- Set appropriate min/max bounds
- Use conservative learning rates (0.01-0.05)

### 2. Straddle Analysis
- Enable ATM straddle with highest weight (0.4)
- Include EMA analysis for trend detection
- Use previous day VWAP for reference levels

### 3. Timeframe Configuration
- Set 5min as primary timeframe
- Use confirmation for longer timeframes
- Balance weights across timeframes

### 4. Performance Tracking
- Monitor historical performance regularly
- Update weights based on recent performance
- Keep performance window reasonable (20-30 periods)

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure the actual system path is accessible
2. **Configuration Validation Fails**: Check weight sums and required columns
3. **No Regime Results**: Verify input data has required columns
4. **Performance Issues**: Reduce timeframe analysis or data size

### Debug Mode

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# Enable detailed logging
engine = ExcelBasedRegimeEngine("config.xlsx")
```

## Next Steps

1. **Generate Excel Template**: Use the template generation feature
2. **Configure Parameters**: Customize the Excel configuration for your needs
3. **Test Integration**: Run the comprehensive test suite
4. **Deploy in Backtester**: Integrate with your backtesting system
5. **Monitor Performance**: Track and adjust weights based on performance

## Support

For issues or questions:
1. Check the test output for specific errors
2. Review the logs for detailed error messages
3. Validate your Excel configuration
4. Ensure the actual system path is accessible
