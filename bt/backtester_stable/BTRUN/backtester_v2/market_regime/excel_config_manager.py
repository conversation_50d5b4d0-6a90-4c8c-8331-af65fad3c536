"""
Market Regime Excel Configuration Manager

This module provides Excel-based configuration management for the market regime
detection system, following the same patterns as other backtester_v2 systems.
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import logging
from enum import Enum

from .enhanced_regime_detector import Enhanced18RegimeType

logger = logging.getLogger(__name__)

class RegimeConfigType(Enum):
    """Types of regime configuration"""
    DETECTION_PARAMETERS = "detection_parameters"
    REGIME_ADJUSTMENTS = "regime_adjustments"
    STRATEGY_MAPPINGS = "strategy_mappings"
    LIVE_SETTINGS = "live_settings"

class MarketRegimeExcelManager:
    """
    Excel-based configuration manager for market regime system
    
    Provides Excel configuration management following backtester_v2 patterns:
    - Multiple sheet configuration files
    - Template generation
    - Validation and parsing
    - Dynamic parameter updates
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize Excel configuration manager
        
        Args:
            config_path (str, optional): Path to configuration Excel file
        """
        self.config_path = config_path
        self.config_data = {}
        self.template_structure = self._define_template_structure()
        
        if config_path and Path(config_path).exists():
            self.load_configuration()
        
        logger.info("MarketRegimeExcelManager initialized")
    
    def _define_template_structure(self) -> Dict[str, Dict[str, Any]]:
        """Define the Excel template structure"""
        return {
            'RegimeDetectionConfig': {
                'description': 'Core regime detection parameters',
                'columns': [
                    'Parameter', 'Value', 'Description', 'DataType', 'MinValue', 'MaxValue'
                ],
                'default_data': [
                    ['ConfidenceThreshold', 0.6, 'Minimum confidence for regime classification', 'float', 0.0, 1.0],
                    ['RegimeSmoothing', 3, 'Number of periods for regime smoothing', 'int', 1, 10],
                    ['IndicatorWeightGreek', 0.35, 'Weight for Greek sentiment indicators', 'float', 0.0, 1.0],
                    ['IndicatorWeightOI', 0.25, 'Weight for OI analysis indicators', 'float', 0.0, 1.0],
                    ['IndicatorWeightPrice', 0.20, 'Weight for price action indicators', 'float', 0.0, 1.0],
                    ['IndicatorWeightTechnical', 0.15, 'Weight for technical indicators', 'float', 0.0, 1.0],
                    ['IndicatorWeightVolatility', 0.05, 'Weight for volatility measures', 'float', 0.0, 1.0],
                    ['DirectionalThresholdStrongBullish', 0.50, 'Threshold for strong bullish classification', 'float', 0.0, 1.0],
                    ['DirectionalThresholdMildBullish', 0.20, 'Threshold for mild bullish classification', 'float', 0.0, 1.0],
                    ['DirectionalThresholdNeutral', 0.10, 'Threshold for neutral classification', 'float', -0.5, 0.5],
                    ['DirectionalThresholdSideways', 0.05, 'Threshold for sideways classification', 'float', -0.2, 0.2],
                    ['DirectionalThresholdMildBearish', -0.20, 'Threshold for mild bearish classification', 'float', -1.0, 0.0],
                    ['DirectionalThresholdStrongBearish', -0.50, 'Threshold for strong bearish classification', 'float', -1.0, 0.0],
                    ['VolatilityThresholdHigh', 0.20, 'Threshold for high volatility classification', 'float', 0.0, 1.0],
                    ['VolatilityThresholdNormalHigh', 0.15, 'Threshold for normal-high volatility', 'float', 0.0, 1.0],
                    ['VolatilityThresholdNormalLow', 0.10, 'Threshold for normal-low volatility', 'float', 0.0, 1.0],
                    ['VolatilityThresholdLow', 0.05, 'Threshold for low volatility classification', 'float', 0.0, 1.0]
                ]
            },
            'RegimeAdjustments': {
                'description': 'Strategy adjustments for each regime type',
                'columns': [
                    'RegimeType', 'EnableRegimeFilter', 'PositionSizeMultiplier', 'StopLossMultiplier', 
                    'TakeProfitMultiplier', 'UrgencyFactor', 'RiskTolerance', 'Description'
                ],
                'default_data': self._generate_regime_adjustments_data()
            },
            'StrategyMappings': {
                'description': 'Strategy-specific regime configurations',
                'columns': [
                    'StrategyType', 'RegimeType', 'EnableStrategy', 'WeightMultiplier', 
                    'CustomParameters', 'Notes'
                ],
                'default_data': self._generate_strategy_mappings_data()
            },
            'LiveTradingConfig': {
                'description': 'Live trading and streaming configuration',
                'columns': [
                    'Parameter', 'Value', 'Description', 'DataType', 'Category'
                ],
                'default_data': [
                    ['EnableLiveTrading', 'YES', 'Enable live trading integration', 'bool', 'Trading'],
                    ['StreamingIntervalMs', 100, 'Market data streaming interval in milliseconds', 'int', 'Streaming'],
                    ['RegimeUpdateFreqSec', 60, 'Regime detection update frequency in seconds', 'int', 'Detection'],
                    ['EnableAlgobobaIntegration', 'YES', 'Enable Algobaba order management', 'bool', 'Trading'],
                    ['MaxDailyOrders', 100, 'Maximum orders per day', 'int', 'Risk'],
                    ['MaxRegimeExposure', 0.5, 'Maximum exposure per regime (0.0-1.0)', 'float', 'Risk'],
                    ['EnableRegimeAlerts', 'YES', 'Enable regime change alerts', 'bool', 'Alerts'],
                    ['AlertChannels', 'EMAIL,TELEGRAM', 'Alert delivery channels', 'str', 'Alerts'],
                    ['RegimeHistoryLimit', 1000, 'Number of regime records to keep in memory', 'int', 'Memory'],
                    ['EnablePerformanceTracking', 'YES', 'Enable regime performance tracking', 'bool', 'Analytics'],
                    ['PerformanceWindowDays', 30, 'Performance analysis window in days', 'int', 'Analytics'],
                    ['EnableAutoRestart', 'YES', 'Enable automatic component restart on failure', 'bool', 'System'],
                    ['HealthCheckIntervalSec', 30, 'System health check interval in seconds', 'int', 'System']
                ]
            }
        }
    
    def _generate_regime_adjustments_data(self) -> List[List[Any]]:
        """Generate default regime adjustment data"""
        adjustments = []
        
        # Define adjustment patterns for each regime type
        regime_configs = {
            # Strong Bullish Regimes
            'HIGH_VOLATILE_STRONG_BULLISH': [1.5, 0.8, 1.3, 0.7, 'HIGH'],
            'NORMAL_VOLATILE_STRONG_BULLISH': [1.3, 0.9, 1.2, 0.8, 'MEDIUM_HIGH'],
            'LOW_VOLATILE_STRONG_BULLISH': [1.4, 1.0, 1.1, 0.9, 'MEDIUM'],
            
            # Mild Bullish Regimes
            'HIGH_VOLATILE_MILD_BULLISH': [1.1, 0.8, 1.1, 0.8, 'MEDIUM'],
            'NORMAL_VOLATILE_MILD_BULLISH': [1.0, 1.0, 1.0, 1.0, 'MEDIUM'],
            'LOW_VOLATILE_MILD_BULLISH': [1.2, 1.1, 1.0, 1.1, 'LOW'],
            
            # Neutral Regimes
            'HIGH_VOLATILE_NEUTRAL': [0.8, 0.7, 0.9, 0.9, 'MEDIUM'],
            'NORMAL_VOLATILE_NEUTRAL': [0.9, 1.0, 1.0, 1.0, 'MEDIUM'],
            'LOW_VOLATILE_NEUTRAL': [1.0, 1.2, 0.9, 1.2, 'LOW'],
            
            # Sideways Regimes
            'HIGH_VOLATILE_SIDEWAYS': [0.6, 0.6, 0.8, 0.8, 'HIGH'],
            'NORMAL_VOLATILE_SIDEWAYS': [0.7, 0.8, 0.9, 1.0, 'MEDIUM'],
            'LOW_VOLATILE_SIDEWAYS': [0.8, 1.0, 0.8, 1.3, 'LOW'],
            
            # Bearish Regimes
            'HIGH_VOLATILE_MILD_BEARISH': [1.1, 0.8, 1.1, 0.8, 'MEDIUM'],
            'NORMAL_VOLATILE_MILD_BEARISH': [1.0, 1.0, 1.0, 1.0, 'MEDIUM'],
            'LOW_VOLATILE_MILD_BEARISH': [1.2, 1.1, 1.0, 1.1, 'LOW'],
            'HIGH_VOLATILE_STRONG_BEARISH': [1.5, 0.8, 1.3, 0.7, 'HIGH'],
            'NORMAL_VOLATILE_STRONG_BEARISH': [1.3, 0.9, 1.2, 0.8, 'MEDIUM_HIGH'],
            'LOW_VOLATILE_STRONG_BEARISH': [1.4, 1.0, 1.1, 0.9, 'MEDIUM']
        }
        
        for regime_type, config in regime_configs.items():
            pos_mult, sl_mult, tp_mult, urgency, risk_tol = config
            adjustments.append([
                regime_type, 'YES', pos_mult, sl_mult, tp_mult, urgency, risk_tol,
                f'Optimized parameters for {regime_type.replace("_", " ").title()} market conditions'
            ])
        
        return adjustments
    
    def _generate_strategy_mappings_data(self) -> List[List[Any]]:
        """Generate default strategy mapping data"""
        mappings = []
        
        strategy_types = ['TBS', 'TV', 'OI', 'ORB', 'POS', 'ML_INDICATOR']
        regime_types = [regime.value for regime in Enhanced18RegimeType]
        
        # Define strategy preferences for different regimes
        strategy_preferences = {
            'TBS': {
                'HIGH_VOLATILE': 0.8,  # TBS works well in high volatility
                'STRONG_BULLISH': 1.2,
                'STRONG_BEARISH': 1.2,
                'SIDEWAYS': 1.5  # TBS excels in sideways markets
            },
            'TV': {
                'HIGH_VOLATILE': 1.3,  # TV signals work well in volatile markets
                'STRONG_BULLISH': 1.4,
                'STRONG_BEARISH': 1.4,
                'NEUTRAL': 0.7
            },
            'OI': {
                'HIGH_VOLATILE': 1.1,
                'STRONG_BULLISH': 1.3,
                'STRONG_BEARISH': 1.3,
                'SIDEWAYS': 0.8
            },
            'ORB': {
                'HIGH_VOLATILE': 1.4,  # ORB thrives in volatile breakouts
                'STRONG_BULLISH': 1.2,
                'STRONG_BEARISH': 1.2,
                'LOW_VOLATILE': 0.6
            },
            'POS': {
                'LOW_VOLATILE': 1.3,  # Positional strategies prefer stable markets
                'SIDEWAYS': 1.2,
                'HIGH_VOLATILE': 0.7
            },
            'ML_INDICATOR': {
                'HIGH_VOLATILE': 1.1,
                'NORMAL_VOLATILE': 1.2,
                'LOW_VOLATILE': 1.0
            }
        }
        
        for strategy_type in strategy_types:
            for regime_type in regime_types:
                # Calculate weight multiplier based on preferences
                weight_mult = 1.0
                prefs = strategy_preferences.get(strategy_type, {})
                
                for pattern, multiplier in prefs.items():
                    if pattern in regime_type:
                        weight_mult *= multiplier
                
                # Normalize weight multiplier
                weight_mult = max(0.1, min(2.0, weight_mult))
                
                # Determine if strategy should be enabled for this regime
                enable_strategy = 'YES' if weight_mult >= 0.5 else 'NO'
                
                mappings.append([
                    strategy_type, regime_type, enable_strategy, round(weight_mult, 2),
                    f'Auto-generated for {strategy_type}', 
                    f'Weight based on {strategy_type} performance in {regime_type} conditions'
                ])
        
        return mappings
    
    def generate_excel_template(self, output_path: str) -> str:
        """
        Generate Excel configuration template
        
        Args:
            output_path (str): Path where to save the template
            
        Returns:
            str: Path to generated template file
        """
        try:
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                for sheet_name, sheet_config in self.template_structure.items():
                    # Create DataFrame from template data
                    df = pd.DataFrame(
                        sheet_config['default_data'],
                        columns=sheet_config['columns']
                    )
                    
                    # Write to Excel
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
                    
                    # Add description as a comment (if supported)
                    worksheet = writer.sheets[sheet_name]
                    worksheet.cell(1, 1).comment = sheet_config['description']
            
            logger.info(f"Excel template generated: {output_path}")
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Error generating Excel template: {e}")
            raise
    
    def load_configuration(self, config_path: Optional[str] = None) -> Dict[str, Any]:
        """
        Load configuration from Excel file
        
        Args:
            config_path (str, optional): Path to configuration file
            
        Returns:
            Dict: Loaded configuration data
        """
        try:
            if config_path:
                self.config_path = config_path
            
            if not self.config_path or not Path(self.config_path).exists():
                raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
            
            # Load all sheets
            excel_file = pd.ExcelFile(self.config_path)
            
            for sheet_name in excel_file.sheet_names:
                if sheet_name in self.template_structure:
                    df = pd.read_excel(self.config_path, sheet_name=sheet_name)
                    self.config_data[sheet_name] = df
            
            logger.info(f"Configuration loaded from: {self.config_path}")
            return self.config_data
            
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            raise
    
    def get_detection_parameters(self) -> Dict[str, Any]:
        """Get regime detection parameters"""
        try:
            if 'RegimeDetectionConfig' not in self.config_data:
                return self._get_default_detection_parameters()
            
            df = self.config_data['RegimeDetectionConfig']
            params = {}
            
            for _, row in df.iterrows():
                param_name = row['Parameter']
                param_value = row['Value']
                data_type = row.get('DataType', 'str')
                
                # Convert to appropriate data type
                if data_type == 'float':
                    param_value = float(param_value)
                elif data_type == 'int':
                    param_value = int(param_value)
                elif data_type == 'bool':
                    param_value = str(param_value).upper() in ['YES', 'TRUE', '1']
                
                params[param_name] = param_value
            
            return params
            
        except Exception as e:
            logger.error(f"Error getting detection parameters: {e}")
            return self._get_default_detection_parameters()
    
    def get_regime_adjustments(self, regime_type: Optional[str] = None) -> Dict[str, Any]:
        """Get regime-specific adjustments"""
        try:
            if 'RegimeAdjustments' not in self.config_data:
                return self._get_default_regime_adjustments()
            
            df = self.config_data['RegimeAdjustments']
            
            if regime_type:
                # Get adjustments for specific regime
                regime_df = df[df['RegimeType'] == regime_type]
                if regime_df.empty:
                    logger.warning(f"No adjustments found for regime: {regime_type}")
                    return {}
                
                row = regime_df.iloc[0]
                return {
                    'enable_regime_filter': row.get('EnableRegimeFilter', 'YES') == 'YES',
                    'position_size_multiplier': float(row.get('PositionSizeMultiplier', 1.0)),
                    'stop_loss_multiplier': float(row.get('StopLossMultiplier', 1.0)),
                    'take_profit_multiplier': float(row.get('TakeProfitMultiplier', 1.0)),
                    'urgency_factor': float(row.get('UrgencyFactor', 1.0)),
                    'risk_tolerance': row.get('RiskTolerance', 'MEDIUM')
                }
            else:
                # Get all regime adjustments
                adjustments = {}
                for _, row in df.iterrows():
                    regime = row['RegimeType']
                    adjustments[regime] = {
                        'enable_regime_filter': row.get('EnableRegimeFilter', 'YES') == 'YES',
                        'position_size_multiplier': float(row.get('PositionSizeMultiplier', 1.0)),
                        'stop_loss_multiplier': float(row.get('StopLossMultiplier', 1.0)),
                        'take_profit_multiplier': float(row.get('TakeProfitMultiplier', 1.0)),
                        'urgency_factor': float(row.get('UrgencyFactor', 1.0)),
                        'risk_tolerance': row.get('RiskTolerance', 'MEDIUM')
                    }
                
                return adjustments
                
        except Exception as e:
            logger.error(f"Error getting regime adjustments: {e}")
            return self._get_default_regime_adjustments()
    
    def get_strategy_mappings(self, strategy_type: Optional[str] = None) -> Dict[str, Any]:
        """Get strategy-regime mappings"""
        try:
            if 'StrategyMappings' not in self.config_data:
                return {}
            
            df = self.config_data['StrategyMappings']
            
            if strategy_type:
                # Get mappings for specific strategy
                strategy_df = df[df['StrategyType'] == strategy_type]
                mappings = {}
                
                for _, row in strategy_df.iterrows():
                    regime = row['RegimeType']
                    mappings[regime] = {
                        'enable_strategy': row.get('EnableStrategy', 'YES') == 'YES',
                        'weight_multiplier': float(row.get('WeightMultiplier', 1.0)),
                        'custom_parameters': row.get('CustomParameters', ''),
                        'notes': row.get('Notes', '')
                    }
                
                return mappings
            else:
                # Get all strategy mappings
                mappings = {}
                for _, row in df.iterrows():
                    strategy = row['StrategyType']
                    regime = row['RegimeType']
                    
                    if strategy not in mappings:
                        mappings[strategy] = {}
                    
                    mappings[strategy][regime] = {
                        'enable_strategy': row.get('EnableStrategy', 'YES') == 'YES',
                        'weight_multiplier': float(row.get('WeightMultiplier', 1.0)),
                        'custom_parameters': row.get('CustomParameters', ''),
                        'notes': row.get('Notes', '')
                    }
                
                return mappings
                
        except Exception as e:
            logger.error(f"Error getting strategy mappings: {e}")
            return {}
    
    def get_live_trading_config(self) -> Dict[str, Any]:
        """Get live trading configuration"""
        try:
            if 'LiveTradingConfig' not in self.config_data:
                return self._get_default_live_config()
            
            df = self.config_data['LiveTradingConfig']
            config = {}
            
            for _, row in df.iterrows():
                param_name = row['Parameter']
                param_value = row['Value']
                data_type = row.get('DataType', 'str')
                
                # Convert to appropriate data type
                if data_type == 'bool':
                    param_value = str(param_value).upper() in ['YES', 'TRUE', '1']
                elif data_type == 'int':
                    param_value = int(param_value)
                elif data_type == 'float':
                    param_value = float(param_value)
                
                config[param_name] = param_value
            
            return config
            
        except Exception as e:
            logger.error(f"Error getting live trading config: {e}")
            return self._get_default_live_config()
    
    def _get_default_detection_parameters(self) -> Dict[str, Any]:
        """Get default detection parameters"""
        return {
            'ConfidenceThreshold': 0.6,
            'RegimeSmoothing': 3,
            'IndicatorWeightGreek': 0.35,
            'IndicatorWeightOI': 0.25,
            'IndicatorWeightPrice': 0.20,
            'IndicatorWeightTechnical': 0.15,
            'IndicatorWeightVolatility': 0.05
        }
    
    def _get_default_regime_adjustments(self) -> Dict[str, Any]:
        """Get default regime adjustments"""
        return {
            'NORMAL_VOLATILE_MILD_BULLISH': {
                'enable_regime_filter': True,
                'position_size_multiplier': 1.0,
                'stop_loss_multiplier': 1.0,
                'take_profit_multiplier': 1.0,
                'urgency_factor': 1.0,
                'risk_tolerance': 'MEDIUM'
            }
        }
    
    def _get_default_live_config(self) -> Dict[str, Any]:
        """Get default live trading configuration"""
        return {
            'EnableLiveTrading': False,
            'StreamingIntervalMs': 100,
            'RegimeUpdateFreqSec': 60,
            'EnableAlgobobaIntegration': False,
            'MaxDailyOrders': 100,
            'MaxRegimeExposure': 0.5
        }
    
    def validate_configuration(self) -> Tuple[bool, List[str]]:
        """
        Validate loaded configuration
        
        Returns:
            Tuple[bool, List[str]]: (is_valid, list_of_errors)
        """
        errors = []
        
        try:
            # Validate detection parameters
            detection_params = self.get_detection_parameters()
            
            # Check required parameters
            required_params = ['ConfidenceThreshold', 'RegimeSmoothing']
            for param in required_params:
                if param not in detection_params:
                    errors.append(f"Missing required parameter: {param}")
            
            # Validate ranges
            if 'ConfidenceThreshold' in detection_params:
                conf_threshold = detection_params['ConfidenceThreshold']
                if not 0.0 <= conf_threshold <= 1.0:
                    errors.append(f"ConfidenceThreshold must be between 0.0 and 1.0, got: {conf_threshold}")
            
            # Validate regime adjustments
            regime_adjustments = self.get_regime_adjustments()
            for regime, adjustments in regime_adjustments.items():
                if 'position_size_multiplier' in adjustments:
                    pos_mult = adjustments['position_size_multiplier']
                    if not 0.1 <= pos_mult <= 5.0:
                        errors.append(f"Invalid position_size_multiplier for {regime}: {pos_mult}")
            
            # Validate live trading config
            live_config = self.get_live_trading_config()
            if 'StreamingIntervalMs' in live_config:
                interval = live_config['StreamingIntervalMs']
                if not 10 <= interval <= 10000:
                    errors.append(f"StreamingIntervalMs should be between 10-10000ms, got: {interval}")
            
            is_valid = len(errors) == 0
            
            if is_valid:
                logger.info("Configuration validation passed")
            else:
                logger.warning(f"Configuration validation failed with {len(errors)} errors")
            
            return is_valid, errors
            
        except Exception as e:
            logger.error(f"Error during configuration validation: {e}")
            return False, [f"Validation error: {str(e)}"]
    
    def update_parameter(self, sheet_name: str, parameter: str, value: Any) -> bool:
        """
        Update a specific parameter in the configuration
        
        Args:
            sheet_name (str): Name of the Excel sheet
            parameter (str): Parameter name to update
            value (Any): New value for the parameter
            
        Returns:
            bool: True if update successful
        """
        try:
            if sheet_name not in self.config_data:
                logger.error(f"Sheet not found: {sheet_name}")
                return False
            
            df = self.config_data[sheet_name]
            
            if sheet_name in ['RegimeDetectionConfig', 'LiveTradingConfig']:
                # Update parameter-value format
                mask = df['Parameter'] == parameter
                if mask.any():
                    df.loc[mask, 'Value'] = value
                    logger.info(f"Updated {parameter} = {value} in {sheet_name}")
                    return True
                else:
                    logger.error(f"Parameter not found: {parameter} in {sheet_name}")
                    return False
            
            return False
            
        except Exception as e:
            logger.error(f"Error updating parameter: {e}")
            return False
    
    def save_configuration(self, output_path: Optional[str] = None) -> bool:
        """
        Save current configuration to Excel file
        
        Args:
            output_path (str, optional): Path to save configuration
            
        Returns:
            bool: True if save successful
        """
        try:
            save_path = output_path or self.config_path
            
            if not save_path:
                raise ValueError("No output path specified")
            
            save_path = Path(save_path)
            save_path.parent.mkdir(parents=True, exist_ok=True)
            
            with pd.ExcelWriter(save_path, engine='openpyxl') as writer:
                for sheet_name, df in self.config_data.items():
                    df.to_excel(writer, sheet_name=sheet_name, index=False)
            
            logger.info(f"Configuration saved to: {save_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving configuration: {e}")
            return False
