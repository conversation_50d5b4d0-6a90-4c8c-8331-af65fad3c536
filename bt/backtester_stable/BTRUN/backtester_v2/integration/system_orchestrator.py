"""
System Orchestrator for Comprehensive Trading System

This module orchestrates the entire modular trading system including:
- Live streaming
- 18 Market regime detection
- Strategy consolidation
- Algobaba integration
- Performance optimization
"""

import asyncio
import logging
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
import json
from pathlib import Path

# Import all system components
from ..live_streaming.kite_streamer import KiteStreamer
from ..market_regime.enhanced_regime_detector import Enhanced18RegimeDetector
from ..strategy_consolidator.base_consolidator import DataDrivenConsolidator
from ..algobaba_integration.regime_order_manager import RegimeOrderManager

logger = logging.getLogger(__name__)

class SystemOrchestrator:
    """
    Main system orchestrator for the comprehensive trading system
    
    This class coordinates all components of the modular trading system,
    providing a unified interface for live trading with regime awareness.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize System Orchestrator
        
        Args:
            config_path (str, optional): Path to system configuration file
        """
        self.config_path = config_path
        self.config = self._load_configuration()
        
        # Core components
        self.regime_detector = None
        self.live_streamer = None
        self.strategy_consolidator = None
        self.order_manager = None
        
        # System state
        self.running = False
        self.current_regime = None
        self.active_strategies = []
        self.system_metrics = {}
        
        # Callbacks
        self.regime_callbacks = []
        self.strategy_callbacks = []
        self.order_callbacks = []
        
        # Performance tracking
        self.performance_tracker = {
            'regime_changes': 0,
            'orders_executed': 0,
            'strategies_active': 0,
            'system_uptime': 0,
            'last_update': None
        }
        
        logger.info("SystemOrchestrator initialized")
    
    def _load_configuration(self) -> Dict[str, Any]:
        """Load system configuration"""
        try:
            if self.config_path and Path(self.config_path).exists():
                with open(self.config_path, 'r') as f:
                    config = json.load(f)
                logger.info(f"Loaded configuration from {self.config_path}")
            else:
                config = self._get_default_configuration()
                logger.info("Using default configuration")
            
            return config
            
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            return self._get_default_configuration()
    
    def _get_default_configuration(self) -> Dict[str, Any]:
        """Get default system configuration"""
        return {
            'live_streaming': {
                'stream_interval_ms': 100,
                'regime_update_freq_sec': 60,
                'enable_live_streaming': True
            },
            'regime_detection': {
                'confidence_threshold': 0.6,
                'regime_smoothing': 3,
                'enable_18_regimes': True
            },
            'strategy_consolidation': {
                'significance_threshold': 0.05,
                'min_trades': 30,
                'min_sharpe_ratio': 0.5,
                'enable_consolidation': True
            },
            'algobaba_integration': {
                'enable_integration': True,
                'max_daily_orders': 100,
                'risk_management': True
            },
            'system': {
                'enable_monitoring': True,
                'log_level': 'INFO',
                'auto_restart': True
            }
        }
    
    def initialize_system(self):
        """Initialize all system components"""
        try:
            logger.info("Initializing comprehensive trading system...")
            
            # Initialize 18-regime detector
            self.regime_detector = Enhanced18RegimeDetector(
                config=self.config.get('regime_detection', {})
            )
            logger.info("✅ 18-Regime detector initialized")
            
            # Initialize live streamer
            if self.config['live_streaming']['enable_live_streaming']:
                self.live_streamer = KiteStreamer(
                    regime_detector=self.regime_detector,
                    config=self.config.get('live_streaming', {})
                )
                
                # Register callbacks
                self.live_streamer.add_regime_callback(self._on_regime_update)
                self.live_streamer.add_data_callback(self._on_market_data_update)
                
                logger.info("✅ Live streamer initialized")
            
            # Initialize strategy consolidator
            if self.config['strategy_consolidation']['enable_consolidation']:
                self.strategy_consolidator = DataDrivenConsolidator(
                    config=self.config.get('strategy_consolidation', {})
                )
                logger.info("✅ Strategy consolidator initialized")
            
            # Initialize Algobaba order manager
            if self.config['algobaba_integration']['enable_integration']:
                self.order_manager = RegimeOrderManager(
                    config=self.config.get('algobaba_integration', {})
                )
                logger.info("✅ Algobaba order manager initialized")
            
            logger.info("🎉 System initialization complete")
            
        except Exception as e:
            logger.error(f"Error initializing system: {e}")
            raise
    
    def start_system(self):
        """Start the comprehensive trading system"""
        try:
            if self.running:
                logger.warning("System is already running")
                return
            
            logger.info("🚀 Starting comprehensive trading system...")
            
            self.running = True
            self.performance_tracker['start_time'] = datetime.now()
            
            # Start live streaming
            if self.live_streamer:
                self.live_streamer.start_streaming()
                logger.info("✅ Live streaming started")
            
            # Start system monitoring
            self._start_system_monitoring()
            
            logger.info("🎉 System started successfully")
            
        except Exception as e:
            logger.error(f"Error starting system: {e}")
            self.running = False
            raise
    
    def stop_system(self):
        """Stop the comprehensive trading system"""
        try:
            if not self.running:
                return
            
            logger.info("🛑 Stopping comprehensive trading system...")
            
            self.running = False
            
            # Stop live streaming
            if self.live_streamer:
                self.live_streamer.stop_streaming()
                logger.info("✅ Live streaming stopped")
            
            # Calculate final metrics
            if 'start_time' in self.performance_tracker:
                uptime = (datetime.now() - self.performance_tracker['start_time']).total_seconds()
                self.performance_tracker['system_uptime'] = uptime
            
            logger.info("🎉 System stopped successfully")
            
        except Exception as e:
            logger.error(f"Error stopping system: {e}")
    
    def _start_system_monitoring(self):
        """Start system monitoring thread"""
        def monitoring_loop():
            logger.info("Starting system monitoring")
            
            while self.running:
                try:
                    self._update_system_metrics()
                    self._check_system_health()
                    time.sleep(30)  # Update every 30 seconds
                    
                except Exception as e:
                    logger.error(f"Error in monitoring loop: {e}")
                    time.sleep(60)  # Wait longer on error
        
        monitoring_thread = threading.Thread(target=monitoring_loop, daemon=True)
        monitoring_thread.start()
    
    def _update_system_metrics(self):
        """Update system performance metrics"""
        try:
            current_time = datetime.now()
            
            # Update basic metrics
            self.performance_tracker['last_update'] = current_time
            
            if 'start_time' in self.performance_tracker:
                uptime = (current_time - self.performance_tracker['start_time']).total_seconds()
                self.performance_tracker['system_uptime'] = uptime
            
            # Update component metrics
            if self.live_streamer:
                streaming_stats = self.live_streamer.get_streaming_statistics()
                self.system_metrics['streaming'] = streaming_stats
            
            if self.regime_detector:
                regime_stats = self.regime_detector.get_regime_statistics()
                self.system_metrics['regime_detection'] = regime_stats
            
            if self.strategy_consolidator:
                consolidation_stats = self.strategy_consolidator.get_consolidation_summary()
                self.system_metrics['strategy_consolidation'] = consolidation_stats
            
            if self.order_manager:
                order_stats = self.order_manager.get_regime_performance()
                self.system_metrics['order_management'] = order_stats
            
        except Exception as e:
            logger.error(f"Error updating system metrics: {e}")
    
    def _check_system_health(self):
        """Check system health and restart components if needed"""
        try:
            # Check live streaming health
            if self.live_streamer and self.config['live_streaming']['enable_live_streaming']:
                streaming_stats = self.live_streamer.get_streaming_statistics()
                
                # Check if streaming is stalled
                if streaming_stats.get('last_update'):
                    last_update = datetime.fromisoformat(streaming_stats['last_update'])
                    if (datetime.now() - last_update).total_seconds() > 300:  # 5 minutes
                        logger.warning("Live streaming appears stalled, attempting restart")
                        if self.config['system']['auto_restart']:
                            self._restart_live_streaming()
            
            # Check regime detection health
            if self.current_regime:
                regime_age = (datetime.now() - self.current_regime['timestamp']).total_seconds()
                if regime_age > 1800:  # 30 minutes
                    logger.warning("Regime data is stale")
            
        except Exception as e:
            logger.error(f"Error checking system health: {e}")
    
    def _restart_live_streaming(self):
        """Restart live streaming component"""
        try:
            logger.info("Restarting live streaming...")
            
            if self.live_streamer:
                self.live_streamer.stop_streaming()
                time.sleep(2)
                self.live_streamer.start_streaming()
            
            logger.info("Live streaming restarted")
            
        except Exception as e:
            logger.error(f"Error restarting live streaming: {e}")
    
    def _on_regime_update(self, regime_update: Dict[str, Any]):
        """Handle regime update from live streamer"""
        try:
            regime_result = regime_update['regime_result']
            regime_changed = regime_update['regime_changed']
            
            # Update current regime
            self.current_regime = regime_result
            
            if regime_changed:
                self.performance_tracker['regime_changes'] += 1
                
                logger.info(f"🔄 Regime changed to: {regime_result['regime_type'].value} "
                           f"(confidence: {regime_result['confidence']:.2f})")
                
                # Process regime-based strategy adjustments
                self._process_regime_change(regime_result)
            
            # Notify regime callbacks
            for callback in self.regime_callbacks:
                try:
                    callback(regime_result, regime_changed)
                except Exception as e:
                    logger.error(f"Error in regime callback: {e}")
            
        except Exception as e:
            logger.error(f"Error handling regime update: {e}")
    
    def _on_market_data_update(self, market_data: Dict[str, Any]):
        """Handle market data update from live streamer"""
        try:
            # Process market data for strategy signals
            if self.active_strategies:
                self._process_strategy_signals(market_data)
            
        except Exception as e:
            logger.error(f"Error handling market data update: {e}")
    
    def _process_regime_change(self, regime_result: Dict[str, Any]):
        """Process regime change and adjust strategies"""
        try:
            if not self.order_manager:
                return
            
            # Apply regime-based adjustments to active strategies
            for strategy in self.active_strategies:
                # This would integrate with actual strategy objects
                logger.info(f"Applying regime adjustments to strategy: {strategy.get('name', 'unknown')}")
            
        except Exception as e:
            logger.error(f"Error processing regime change: {e}")
    
    def _process_strategy_signals(self, market_data: Dict[str, Any]):
        """Process strategy signals based on market data"""
        try:
            # This would integrate with actual strategy signal generation
            # For now, just log the processing
            logger.debug("Processing strategy signals based on market data")
            
        except Exception as e:
            logger.error(f"Error processing strategy signals: {e}")
    
    def consolidate_strategies(self, strategy_sources: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Consolidate strategies from multiple sources"""
        try:
            if not self.strategy_consolidator:
                logger.error("Strategy consolidator not initialized")
                return []
            
            logger.info(f"Starting strategy consolidation for {len(strategy_sources)} sources")
            
            consolidated_strategies = self.strategy_consolidator.consolidate_strategies(strategy_sources)
            
            # Update active strategies
            self.active_strategies = consolidated_strategies
            self.performance_tracker['strategies_active'] = len(consolidated_strategies)
            
            logger.info(f"Strategy consolidation complete: {len(consolidated_strategies)} strategies")
            
            return consolidated_strategies
            
        except Exception as e:
            logger.error(f"Error consolidating strategies: {e}")
            return []
    
    def execute_strategy_order(self, strategy_signal: Dict[str, Any]) -> Dict[str, Any]:
        """Execute strategy order with regime awareness"""
        try:
            if not self.order_manager or not self.current_regime:
                return {
                    'success': False,
                    'error': 'Order manager or regime data not available'
                }
            
            # Create mock strategy object (would be real strategy in production)
            class MockStrategy:
                def __init__(self):
                    self.instance_id = strategy_signal.get('strategy_id', 'unknown')
            
            mock_strategy = MockStrategy()
            
            # Execute order with regime awareness
            result = self.order_manager.execute_regime_order(
                strategy=mock_strategy,
                regime_data=self.current_regime,
                order_signal=strategy_signal
            )
            
            if result['success']:
                self.performance_tracker['orders_executed'] += 1
            
            # Notify order callbacks
            for callback in self.order_callbacks:
                try:
                    callback(result)
                except Exception as e:
                    logger.error(f"Error in order callback: {e}")
            
            return result
            
        except Exception as e:
            logger.error(f"Error executing strategy order: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def add_regime_callback(self, callback: Callable[[Dict[str, Any], bool], None]):
        """Add callback for regime updates"""
        self.regime_callbacks.append(callback)
    
    def add_strategy_callback(self, callback: Callable[[List[Dict[str, Any]]], None]):
        """Add callback for strategy updates"""
        self.strategy_callbacks.append(callback)
    
    def add_order_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """Add callback for order updates"""
        self.order_callbacks.append(callback)
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        try:
            status = {
                'system_running': self.running,
                'current_regime': {
                    'regime_type': self.current_regime['regime_type'].value if self.current_regime else None,
                    'confidence': self.current_regime['confidence'] if self.current_regime else 0.0,
                    'timestamp': self.current_regime['timestamp'].isoformat() if self.current_regime else None
                },
                'performance_tracker': self.performance_tracker,
                'system_metrics': self.system_metrics,
                'active_strategies_count': len(self.active_strategies),
                'components_status': {
                    'regime_detector': self.regime_detector is not None,
                    'live_streamer': self.live_streamer is not None and self.live_streamer.running if self.live_streamer else False,
                    'strategy_consolidator': self.strategy_consolidator is not None,
                    'order_manager': self.order_manager is not None
                }
            }
            
            return status
            
        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return {'error': str(e)}
    
    def get_regime_filtered_strategies(self, regime_filter: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get strategies filtered by regime performance"""
        try:
            if not self.active_strategies:
                return []
            
            filtered_strategies = []
            
            for strategy in self.active_strategies:
                # Filter strategies based on regime performance
                # This would use actual regime performance data
                
                if regime_filter:
                    # Check if strategy performs well in specific regime
                    regime_performance = strategy.get('regime_performance', {})
                    if regime_filter in regime_performance:
                        performance_score = regime_performance[regime_filter].get('score', 0)
                        if performance_score > 0.6:  # Threshold for good performance
                            filtered_strategies.append(strategy)
                else:
                    # Return all strategies
                    filtered_strategies.append(strategy)
            
            return filtered_strategies
            
        except Exception as e:
            logger.error(f"Error filtering strategies by regime: {e}")
            return []
    
    def update_configuration(self, new_config: Dict[str, Any]):
        """Update system configuration"""
        try:
            self.config.update(new_config)
            
            # Apply configuration changes to components
            if self.regime_detector and 'regime_detection' in new_config:
                # Update regime detector configuration
                pass
            
            if self.live_streamer and 'live_streaming' in new_config:
                # Update live streamer configuration
                pass
            
            logger.info("System configuration updated")
            
        except Exception as e:
            logger.error(f"Error updating configuration: {e}")
    
    def export_system_data(self, start_date: Optional[str] = None, 
                          end_date: Optional[str] = None) -> Dict[str, Any]:
        """Export comprehensive system data"""
        try:
            export_data = {
                'system_status': self.get_system_status(),
                'configuration': self.config,
                'export_timestamp': datetime.now().isoformat()
            }
            
            # Add regime history if available
            if self.live_streamer:
                regime_history = self.live_streamer.get_regime_history(limit=1000)
                export_data['regime_history'] = [
                    {
                        'regime_type': r['regime_type'].value,
                        'confidence': r['confidence'],
                        'timestamp': r['timestamp'].isoformat()
                    }
                    for r in regime_history
                ]
            
            # Add strategy data
            export_data['active_strategies'] = self.active_strategies
            
            return export_data
            
        except Exception as e:
            logger.error(f"Error exporting system data: {e}")
            return {'error': str(e)}
