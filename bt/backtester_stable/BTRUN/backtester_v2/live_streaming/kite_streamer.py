"""
Kite Streamer Integration

This module integrates with the existing OI-shift Zerodha Kite WebSocket
infrastructure to provide live market data for regime detection.
"""

import asyncio
import threading
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any
from collections import deque
import pandas as pd
import numpy as np

# Import existing OI-shift modules
import sys
import os
sys.path.append('/srv/samba/shared/oi-shift-dev')

try:
    from commons.models import Ohlc
    from commons.enums import Underlying
    from data import Cache
    import pricefeed
except ImportError as e:
    logging.warning(f"Could not import OI-shift modules: {e}")
    # Create mock classes for testing
    class Ohlc:
        def __init__(self):
            self.open = 0.0
            self.high = 0.0
            self.low = 0.0
            self.close = 0.0
            self.volume = 0
            self.token = 0
    
    class Underlying:
        NIFTY = "NIFTY"
    
    class Cache:
        def pull(self, name):
            return None
    
    class MockPricefeed:
        def get_quote_from_stream(self):
            return None
    
    pricefeed = MockPricefeed()

# Import market regime components
from ..market_regime.enhanced_regime_detector import Enhanced18RegimeDetector

logger = logging.getLogger(__name__)

class KiteStreamer:
    """
    Live market data streamer using existing Kite WebSocket infrastructure
    
    This class integrates with the existing OI-shift pricefeed system to
    provide real-time market data for regime detection and strategy execution.
    """
    
    def __init__(self, regime_detector: Enhanced18RegimeDetector, 
                 config: Optional[Dict[str, Any]] = None):
        """
        Initialize Kite Streamer
        
        Args:
            regime_detector (Enhanced18RegimeDetector): Regime detection engine
            config (Dict, optional): Configuration parameters
        """
        self.regime_detector = regime_detector
        self.config = config or {}
        
        # Streaming configuration
        self.stream_interval = self.config.get('stream_interval_ms', 100) / 1000.0  # Convert to seconds
        self.regime_update_frequency = self.config.get('regime_update_freq_sec', 60)
        
        # Data storage
        self.market_data_buffer = deque(maxlen=1000)
        self.regime_history = deque(maxlen=500)
        self.current_regime = None
        
        # Multi-timeframe data storage
        self.timeframe_data = {
            '1min': deque(maxlen=1440),   # 1 day of 1-minute data
            '5min': deque(maxlen=288),    # 1 day of 5-minute data
            '15min': deque(maxlen=96),    # 1 day of 15-minute data
            '30min': deque(maxlen=48),    # 1 day of 30-minute data
            '1hour': deque(maxlen=24)     # 1 day of hourly data
        }
        
        # Current candles for each timeframe
        self.current_candles = {tf: None for tf in self.timeframe_data.keys()}
        self.last_candle_times = {tf: None for tf in self.timeframe_data.keys()}
        
        # Threading control
        self.running = False
        self.stream_thread = None
        self.regime_thread = None
        
        # Callbacks
        self.regime_callbacks = []
        self.data_callbacks = []
        
        # Performance tracking
        self.stats = {
            'total_ticks': 0,
            'regime_updates': 0,
            'last_update': None,
            'start_time': None
        }
        
        logger.info("KiteStreamer initialized with regime detection")
    
    def add_regime_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """Add callback for regime updates"""
        self.regime_callbacks.append(callback)
    
    def add_data_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """Add callback for market data updates"""
        self.data_callbacks.append(callback)
    
    def start_streaming(self):
        """Start live market data streaming"""
        if self.running:
            logger.warning("Streaming is already running")
            return
        
        self.running = True
        self.stats['start_time'] = datetime.now()
        
        # Start streaming thread
        self.stream_thread = threading.Thread(target=self._stream_loop, daemon=True)
        self.stream_thread.start()
        
        # Start regime detection thread
        self.regime_thread = threading.Thread(target=self._regime_loop, daemon=True)
        self.regime_thread.start()
        
        logger.info("Live streaming started")
    
    def stop_streaming(self):
        """Stop live market data streaming"""
        self.running = False
        
        if self.stream_thread:
            self.stream_thread.join(timeout=5)
        
        if self.regime_thread:
            self.regime_thread.join(timeout=5)
        
        logger.info("Live streaming stopped")
    
    def _stream_loop(self):
        """Main streaming loop"""
        logger.info("Starting market data streaming loop")
        
        while self.running:
            try:
                # Get live market data from existing pricefeed
                market_tick = self._get_live_market_data()
                
                if market_tick:
                    # Process the tick
                    self._process_market_tick(market_tick)
                    
                    # Update statistics
                    self.stats['total_ticks'] += 1
                    self.stats['last_update'] = datetime.now()
                
                # Sleep for configured interval
                time.sleep(self.stream_interval)
                
            except Exception as e:
                logger.error(f"Error in streaming loop: {e}")
                time.sleep(1)  # Wait before retrying
    
    def _regime_loop(self):
        """Regime detection loop"""
        logger.info("Starting regime detection loop")
        
        last_regime_update = datetime.now()
        
        while self.running:
            try:
                current_time = datetime.now()
                
                # Check if it's time for regime update
                if (current_time - last_regime_update).total_seconds() >= self.regime_update_frequency:
                    self._update_market_regime()
                    last_regime_update = current_time
                
                # Sleep for 1 second
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"Error in regime loop: {e}")
                time.sleep(5)  # Wait longer before retrying
    
    def _get_live_market_data(self) -> Optional[Dict[str, Any]]:
        """Get live market data from existing pricefeed"""
        try:
            # Use existing pricefeed infrastructure
            ohlc_data = pricefeed.get_quote_from_stream()
            
            if ohlc_data and isinstance(ohlc_data, Ohlc):
                return {
                    'timestamp': datetime.now(),
                    'open': ohlc_data.open,
                    'high': ohlc_data.high,
                    'low': ohlc_data.low,
                    'close': ohlc_data.close,
                    'volume': ohlc_data.volume,
                    'token': ohlc_data.token,
                    'symbol': self._get_symbol_from_token(ohlc_data.token)
                }
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting live market data: {e}")
            return None
    
    def _get_symbol_from_token(self, token: int) -> str:
        """Get symbol name from instrument token"""
        try:
            # Use existing cache to get instrument info
            for underlying in [Underlying.NIFTY]:  # Add more as needed
                cached_instrument = Cache().pull(underlying)
                if cached_instrument and hasattr(cached_instrument, 'pricefeed_token'):
                    if cached_instrument.pricefeed_token == token:
                        return underlying
            return "UNKNOWN"
        except:
            return "UNKNOWN"
    
    def _process_market_tick(self, tick_data: Dict[str, Any]):
        """Process incoming market tick"""
        try:
            # Add to market data buffer
            self.market_data_buffer.append(tick_data)
            
            # Update multi-timeframe candles
            self._update_timeframe_candles(tick_data)
            
            # Notify data callbacks
            for callback in self.data_callbacks:
                try:
                    callback(tick_data)
                except Exception as e:
                    logger.error(f"Error in data callback: {e}")
            
        except Exception as e:
            logger.error(f"Error processing market tick: {e}")
    
    def _update_timeframe_candles(self, tick_data: Dict[str, Any]):
        """Update candles for different timeframes"""
        try:
            current_time = tick_data['timestamp']
            
            for timeframe in self.timeframe_data.keys():
                self._update_candle_for_timeframe(tick_data, timeframe, current_time)
                
        except Exception as e:
            logger.error(f"Error updating timeframe candles: {e}")
    
    def _update_candle_for_timeframe(self, tick_data: Dict[str, Any], 
                                   timeframe: str, current_time: datetime):
        """Update candle for specific timeframe"""
        try:
            # Calculate candle start time based on timeframe
            candle_start = self._get_candle_start_time(current_time, timeframe)
            
            # Check if we need a new candle
            if (self.last_candle_times[timeframe] is None or 
                candle_start > self.last_candle_times[timeframe]):
                
                # Save previous candle if it exists
                if self.current_candles[timeframe] is not None:
                    self.timeframe_data[timeframe].append(self.current_candles[timeframe])
                
                # Start new candle
                self.current_candles[timeframe] = {
                    'timestamp': candle_start,
                    'open': tick_data['close'],
                    'high': tick_data['close'],
                    'low': tick_data['close'],
                    'close': tick_data['close'],
                    'volume': tick_data['volume'],
                    'symbol': tick_data['symbol']
                }
                self.last_candle_times[timeframe] = candle_start
            
            else:
                # Update current candle
                if self.current_candles[timeframe] is not None:
                    candle = self.current_candles[timeframe]
                    candle['high'] = max(candle['high'], tick_data['close'])
                    candle['low'] = min(candle['low'], tick_data['close'])
                    candle['close'] = tick_data['close']
                    candle['volume'] += tick_data['volume']
                    
        except Exception as e:
            logger.error(f"Error updating candle for timeframe {timeframe}: {e}")
    
    def _get_candle_start_time(self, current_time: datetime, timeframe: str) -> datetime:
        """Calculate candle start time for given timeframe"""
        try:
            if timeframe == '1min':
                return current_time.replace(second=0, microsecond=0)
            elif timeframe == '5min':
                minute = (current_time.minute // 5) * 5
                return current_time.replace(minute=minute, second=0, microsecond=0)
            elif timeframe == '15min':
                minute = (current_time.minute // 15) * 15
                return current_time.replace(minute=minute, second=0, microsecond=0)
            elif timeframe == '30min':
                minute = (current_time.minute // 30) * 30
                return current_time.replace(minute=minute, second=0, microsecond=0)
            elif timeframe == '1hour':
                return current_time.replace(minute=0, second=0, microsecond=0)
            else:
                return current_time.replace(second=0, microsecond=0)
                
        except Exception as e:
            logger.error(f"Error calculating candle start time: {e}")
            return current_time
    
    def _update_market_regime(self):
        """Update market regime based on current data"""
        try:
            # Prepare market data for regime detection
            market_data = self._prepare_regime_data()
            
            if not market_data:
                logger.warning("No market data available for regime detection")
                return
            
            # Detect current regime
            regime_result = self.regime_detector.detect_regime(market_data)
            
            # Check if regime changed
            regime_changed = (
                self.current_regime is None or 
                self.current_regime['regime_type'] != regime_result['regime_type']
            )
            
            # Update current regime
            self.current_regime = regime_result
            self.regime_history.append(regime_result)
            
            # Update statistics
            self.stats['regime_updates'] += 1
            
            # Notify regime callbacks
            for callback in self.regime_callbacks:
                try:
                    callback({
                        'regime_result': regime_result,
                        'regime_changed': regime_changed,
                        'timestamp': datetime.now()
                    })
                except Exception as e:
                    logger.error(f"Error in regime callback: {e}")
            
            if regime_changed:
                logger.info(f"Regime changed to: {regime_result['regime_type'].value} "
                           f"(confidence: {regime_result['confidence']:.2f})")
            
        except Exception as e:
            logger.error(f"Error updating market regime: {e}")
    
    def _prepare_regime_data(self) -> Optional[Dict[str, Any]]:
        """Prepare market data for regime detection"""
        try:
            if not self.market_data_buffer:
                return None
            
            # Get recent market data
            recent_data = list(self.market_data_buffer)[-100:]  # Last 100 ticks
            
            if not recent_data:
                return None
            
            # Extract price data
            price_data = [tick['close'] for tick in recent_data]
            
            # Calculate basic technical indicators
            if len(price_data) >= 20:
                # Simple moving averages
                sma_20 = np.mean(price_data[-20:])
                sma_5 = np.mean(price_data[-5:])
                
                # Price momentum
                momentum = (price_data[-1] - price_data[-10]) / price_data[-10] if len(price_data) >= 10 else 0
                
                # Volatility (ATR approximation)
                highs = [tick['high'] for tick in recent_data[-20:]]
                lows = [tick['low'] for tick in recent_data[-20:]]
                closes = [tick['close'] for tick in recent_data[-20:]]
                
                if len(highs) >= 2:
                    tr_values = []
                    for i in range(1, len(highs)):
                        tr = max(
                            highs[i] - lows[i],
                            abs(highs[i] - closes[i-1]),
                            abs(lows[i] - closes[i-1])
                        )
                        tr_values.append(tr)
                    atr = np.mean(tr_values) if tr_values else 0.0
                else:
                    atr = 0.0
                
                # Prepare regime data
                regime_data = {
                    'price_data': price_data,
                    'technical_indicators': {
                        'sma_20': sma_20,
                        'sma_5': sma_5,
                        'momentum': momentum,
                        'ma_signal': (sma_5 - sma_20) / sma_20 if sma_20 > 0 else 0
                    },
                    'atr': atr,
                    'implied_volatility': atr / price_data[-1] if price_data[-1] > 0 else 0,
                    'timestamp': recent_data[-1]['timestamp']
                }
                
                # Add mock OI and Greek data (would be replaced with real data)
                regime_data['oi_data'] = {
                    'call_oi': 1000000,  # Mock data
                    'put_oi': 1200000,   # Mock data
                    'call_volume': 50000,
                    'put_volume': 60000
                }
                
                regime_data['greek_sentiment'] = {
                    'delta': momentum * 0.5,  # Approximate delta from momentum
                    'gamma': abs(momentum) * 0.1,
                    'theta': -0.05,
                    'vega': atr * 0.1
                }
                
                return regime_data
            
            return None
            
        except Exception as e:
            logger.error(f"Error preparing regime data: {e}")
            return None
    
    def get_current_regime(self) -> Optional[Dict[str, Any]]:
        """Get current market regime"""
        return self.current_regime
    
    def get_regime_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent regime history"""
        return list(self.regime_history)[-limit:]
    
    def get_timeframe_data(self, timeframe: str, limit: int = 100) -> List[Dict[str, Any]]:
        """Get candle data for specific timeframe"""
        if timeframe in self.timeframe_data:
            return list(self.timeframe_data[timeframe])[-limit:]
        return []
    
    def get_streaming_statistics(self) -> Dict[str, Any]:
        """Get streaming statistics"""
        current_time = datetime.now()
        
        stats = self.stats.copy()
        
        if stats['start_time']:
            uptime = (current_time - stats['start_time']).total_seconds()
            stats['uptime_seconds'] = uptime
            stats['ticks_per_second'] = stats['total_ticks'] / uptime if uptime > 0 else 0
        
        stats['current_regime'] = self.current_regime['regime_type'].value if self.current_regime else None
        stats['regime_confidence'] = self.current_regime['confidence'] if self.current_regime else 0.0
        stats['buffer_size'] = len(self.market_data_buffer)
        stats['regime_history_size'] = len(self.regime_history)
        
        return stats
