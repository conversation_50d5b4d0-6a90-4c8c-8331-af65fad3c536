"""
Live Streaming Manager

This module provides advanced streaming management capabilities for
coordinating multiple data sources, managing connections, and ensuring
reliable data flow for market regime detection.
"""

import asyncio
import threading
import time
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from collections import defaultdict
import json

from .kite_streamer import <PERSON><PERSON><PERSON><PERSON>ame<PERSON>
from .data_aggregator import MarketDataAggregator

logger = logging.getLogger(__name__)

class LiveStreamingManager:
    """
    Advanced streaming manager for coordinating multiple data sources
    
    Manages multiple streaming connections, data aggregation, error recovery,
    and provides unified interface for market regime detection system.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize Live Streaming Manager
        
        Args:
            config (Dict, optional): Configuration parameters
        """
        self.config = config or {}
        
        # Core components
        self.data_aggregator = MarketDataAggregator(
            config=self.config.get('aggregator', {})
        )
        self.streamers = {}
        
        # Connection management
        self.running = False
        self.connection_status = {}
        self.reconnection_attempts = defaultdict(int)
        self.max_reconnection_attempts = self.config.get('max_reconnection_attempts', 5)
        
        # Data flow management
        self.data_callbacks = []
        self.regime_callbacks = []
        self.error_callbacks = []
        
        # Performance monitoring
        self.performance_metrics = {
            'total_data_points': 0,
            'successful_connections': 0,
            'failed_connections': 0,
            'reconnections': 0,
            'last_data_time': None,
            'start_time': None
        }
        
        # Threading
        self.management_thread = None
        self.monitoring_thread = None
        
        logger.info("LiveStreamingManager initialized")
    
    def add_streamer(self, name: str, streamer_config: Dict[str, Any]) -> bool:
        """
        Add a new data streamer
        
        Args:
            name (str): Unique name for the streamer
            streamer_config (Dict): Streamer configuration
            
        Returns:
            bool: True if streamer added successfully
        """
        try:
            streamer_type = streamer_config.get('type', 'kite')
            
            if streamer_type == 'kite':
                # Create KiteStreamer instance
                from ..market_regime.enhanced_regime_detector import Enhanced18RegimeDetector
                
                regime_detector = Enhanced18RegimeDetector(
                    config=streamer_config.get('regime_config', {})
                )
                
                streamer = KiteStreamer(
                    regime_detector=regime_detector,
                    config=streamer_config.get('kite_config', {})
                )
                
                # Register callbacks
                streamer.add_data_callback(self._on_streamer_data)
                streamer.add_regime_callback(self._on_regime_update)
                
                self.streamers[name] = {
                    'instance': streamer,
                    'config': streamer_config,
                    'type': streamer_type,
                    'status': 'initialized'
                }
                
                self.connection_status[name] = {
                    'connected': False,
                    'last_connection_time': None,
                    'last_data_time': None,
                    'error_count': 0
                }
                
                logger.info(f"Added {streamer_type} streamer: {name}")
                return True
            
            else:
                logger.error(f"Unknown streamer type: {streamer_type}")
                return False
                
        except Exception as e:
            logger.error(f"Error adding streamer {name}: {e}")
            return False
    
    def start_streaming(self) -> bool:
        """
        Start all streaming connections
        
        Returns:
            bool: True if streaming started successfully
        """
        try:
            if self.running:
                logger.warning("Streaming is already running")
                return True
            
            self.running = True
            self.performance_metrics['start_time'] = datetime.now()
            
            # Start all streamers
            for name, streamer_info in self.streamers.items():
                self._start_streamer(name, streamer_info)
            
            # Start management threads
            self._start_management_threads()
            
            logger.info(f"Started streaming with {len(self.streamers)} streamers")
            return True
            
        except Exception as e:
            logger.error(f"Error starting streaming: {e}")
            self.running = False
            return False
    
    def stop_streaming(self) -> bool:
        """
        Stop all streaming connections
        
        Returns:
            bool: True if streaming stopped successfully
        """
        try:
            if not self.running:
                return True
            
            self.running = False
            
            # Stop all streamers
            for name, streamer_info in self.streamers.items():
                self._stop_streamer(name, streamer_info)
            
            # Stop management threads
            if self.management_thread:
                self.management_thread.join(timeout=5)
            
            if self.monitoring_thread:
                self.monitoring_thread.join(timeout=5)
            
            logger.info("Streaming stopped successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping streaming: {e}")
            return False
    
    def _start_streamer(self, name: str, streamer_info: Dict[str, Any]):
        """Start individual streamer"""
        try:
            streamer = streamer_info['instance']
            
            if hasattr(streamer, 'start_streaming'):
                streamer.start_streaming()
                self.connection_status[name]['connected'] = True
                self.connection_status[name]['last_connection_time'] = datetime.now()
                self.performance_metrics['successful_connections'] += 1
                streamer_info['status'] = 'running'
                
                logger.info(f"Started streamer: {name}")
            else:
                logger.error(f"Streamer {name} does not support start_streaming")
                
        except Exception as e:
            logger.error(f"Error starting streamer {name}: {e}")
            self.connection_status[name]['connected'] = False
            self.connection_status[name]['error_count'] += 1
            self.performance_metrics['failed_connections'] += 1
            streamer_info['status'] = 'error'
    
    def _stop_streamer(self, name: str, streamer_info: Dict[str, Any]):
        """Stop individual streamer"""
        try:
            streamer = streamer_info['instance']
            
            if hasattr(streamer, 'stop_streaming'):
                streamer.stop_streaming()
                self.connection_status[name]['connected'] = False
                streamer_info['status'] = 'stopped'
                
                logger.info(f"Stopped streamer: {name}")
                
        except Exception as e:
            logger.error(f"Error stopping streamer {name}: {e}")
    
    def _start_management_threads(self):
        """Start management and monitoring threads"""
        try:
            # Connection management thread
            self.management_thread = threading.Thread(
                target=self._connection_management_loop,
                daemon=True
            )
            self.management_thread.start()
            
            # Performance monitoring thread
            self.monitoring_thread = threading.Thread(
                target=self._performance_monitoring_loop,
                daemon=True
            )
            self.monitoring_thread.start()
            
            logger.info("Management threads started")
            
        except Exception as e:
            logger.error(f"Error starting management threads: {e}")
    
    def _connection_management_loop(self):
        """Connection management loop"""
        logger.info("Starting connection management loop")
        
        while self.running:
            try:
                # Check connection status for all streamers
                for name, status in self.connection_status.items():
                    if not status['connected'] and name in self.streamers:
                        self._attempt_reconnection(name)
                
                # Check for stale connections
                self._check_stale_connections()
                
                time.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                logger.error(f"Error in connection management loop: {e}")
                time.sleep(30)  # Wait longer on error
    
    def _performance_monitoring_loop(self):
        """Performance monitoring loop"""
        logger.info("Starting performance monitoring loop")
        
        while self.running:
            try:
                # Update performance metrics
                self._update_performance_metrics()
                
                # Log performance summary
                if self.config.get('enable_performance_logging', False):
                    self._log_performance_summary()
                
                time.sleep(60)  # Monitor every minute
                
            except Exception as e:
                logger.error(f"Error in performance monitoring loop: {e}")
                time.sleep(60)
    
    def _attempt_reconnection(self, name: str):
        """Attempt to reconnect a failed streamer"""
        try:
            if self.reconnection_attempts[name] >= self.max_reconnection_attempts:
                logger.warning(f"Max reconnection attempts reached for {name}")
                return
            
            logger.info(f"Attempting reconnection for {name} (attempt {self.reconnection_attempts[name] + 1})")
            
            streamer_info = self.streamers[name]
            self._start_streamer(name, streamer_info)
            
            if self.connection_status[name]['connected']:
                self.reconnection_attempts[name] = 0  # Reset on successful connection
                self.performance_metrics['reconnections'] += 1
                logger.info(f"Successfully reconnected {name}")
            else:
                self.reconnection_attempts[name] += 1
                
        except Exception as e:
            logger.error(f"Error during reconnection attempt for {name}: {e}")
            self.reconnection_attempts[name] += 1
    
    def _check_stale_connections(self):
        """Check for stale connections that haven't received data"""
        try:
            current_time = datetime.now()
            stale_threshold = timedelta(minutes=5)  # 5 minutes without data
            
            for name, status in self.connection_status.items():
                if (status['connected'] and 
                    status['last_data_time'] and 
                    current_time - status['last_data_time'] > stale_threshold):
                    
                    logger.warning(f"Stale connection detected for {name}, attempting restart")
                    status['connected'] = False
                    
        except Exception as e:
            logger.error(f"Error checking stale connections: {e}")
    
    def _on_streamer_data(self, data: Dict[str, Any]):
        """Handle data from streamers"""
        try:
            # Update connection status
            streamer_name = data.get('source', 'unknown')
            if streamer_name in self.connection_status:
                self.connection_status[streamer_name]['last_data_time'] = datetime.now()
            
            # Process through data aggregator
            aggregated_data = self.data_aggregator.process_tick(data)
            
            # Update performance metrics
            self.performance_metrics['total_data_points'] += 1
            self.performance_metrics['last_data_time'] = datetime.now()
            
            # Notify data callbacks
            for callback in self.data_callbacks:
                try:
                    callback({
                        'raw_data': data,
                        'aggregated_data': aggregated_data,
                        'timestamp': datetime.now()
                    })
                except Exception as e:
                    logger.error(f"Error in data callback: {e}")
                    
        except Exception as e:
            logger.error(f"Error handling streamer data: {e}")
    
    def _on_regime_update(self, regime_data: Dict[str, Any]):
        """Handle regime updates from streamers"""
        try:
            # Notify regime callbacks
            for callback in self.regime_callbacks:
                try:
                    callback(regime_data)
                except Exception as e:
                    logger.error(f"Error in regime callback: {e}")
                    
        except Exception as e:
            logger.error(f"Error handling regime update: {e}")
    
    def _update_performance_metrics(self):
        """Update performance metrics"""
        try:
            current_time = datetime.now()
            
            if self.performance_metrics['start_time']:
                uptime = (current_time - self.performance_metrics['start_time']).total_seconds()
                self.performance_metrics['uptime_seconds'] = uptime
                
                if uptime > 0:
                    self.performance_metrics['data_points_per_second'] = (
                        self.performance_metrics['total_data_points'] / uptime
                    )
            
            # Add aggregator statistics
            aggregator_stats = self.data_aggregator.get_aggregator_statistics()
            self.performance_metrics['aggregator_stats'] = aggregator_stats
            
        except Exception as e:
            logger.error(f"Error updating performance metrics: {e}")
    
    def _log_performance_summary(self):
        """Log performance summary"""
        try:
            metrics = self.performance_metrics
            
            logger.info("=== Streaming Performance Summary ===")
            logger.info(f"Uptime: {metrics.get('uptime_seconds', 0):.1f} seconds")
            logger.info(f"Total data points: {metrics['total_data_points']}")
            logger.info(f"Data rate: {metrics.get('data_points_per_second', 0):.2f} points/sec")
            logger.info(f"Successful connections: {metrics['successful_connections']}")
            logger.info(f"Failed connections: {metrics['failed_connections']}")
            logger.info(f"Reconnections: {metrics['reconnections']}")
            
            # Connection status
            connected_count = sum(1 for status in self.connection_status.values() if status['connected'])
            logger.info(f"Active connections: {connected_count}/{len(self.streamers)}")
            
        except Exception as e:
            logger.error(f"Error logging performance summary: {e}")
    
    def add_data_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """Add callback for data updates"""
        self.data_callbacks.append(callback)
    
    def add_regime_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """Add callback for regime updates"""
        self.regime_callbacks.append(callback)
    
    def add_error_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """Add callback for error notifications"""
        self.error_callbacks.append(callback)
    
    def get_streaming_status(self) -> Dict[str, Any]:
        """Get comprehensive streaming status"""
        try:
            return {
                'running': self.running,
                'streamers': {
                    name: {
                        'type': info['type'],
                        'status': info['status'],
                        'connected': self.connection_status[name]['connected'],
                        'last_data_time': self.connection_status[name]['last_data_time'].isoformat() 
                                         if self.connection_status[name]['last_data_time'] else None,
                        'error_count': self.connection_status[name]['error_count']
                    }
                    for name, info in self.streamers.items()
                },
                'performance_metrics': self.performance_metrics,
                'aggregator_stats': self.data_aggregator.get_aggregator_statistics()
            }
            
        except Exception as e:
            logger.error(f"Error getting streaming status: {e}")
            return {'error': str(e)}
    
    def get_market_data(self, timeframe: str = '1min', limit: int = 100) -> List[Dict[str, Any]]:
        """Get aggregated market data"""
        return self.data_aggregator.get_timeframe_data(timeframe, limit)
    
    def get_technical_indicators(self, timeframe: str = '1min', limit: int = 100) -> Dict[str, Any]:
        """Get technical indicators for regime detection"""
        return self.data_aggregator.calculate_technical_indicators(timeframe, limit)
    
    def restart_streamer(self, name: str) -> bool:
        """Restart specific streamer"""
        try:
            if name not in self.streamers:
                logger.error(f"Streamer {name} not found")
                return False
            
            # Stop streamer
            self._stop_streamer(name, self.streamers[name])
            time.sleep(2)
            
            # Start streamer
            self._start_streamer(name, self.streamers[name])
            
            return self.connection_status[name]['connected']
            
        except Exception as e:
            logger.error(f"Error restarting streamer {name}: {e}")
            return False
    
    def remove_streamer(self, name: str) -> bool:
        """Remove streamer"""
        try:
            if name not in self.streamers:
                return True
            
            # Stop streamer if running
            self._stop_streamer(name, self.streamers[name])
            
            # Remove from collections
            del self.streamers[name]
            del self.connection_status[name]
            if name in self.reconnection_attempts:
                del self.reconnection_attempts[name]
            
            logger.info(f"Removed streamer: {name}")
            return True
            
        except Exception as e:
            logger.error(f"Error removing streamer {name}: {e}")
            return False
