"""
Market Data Aggregator for Live Streaming

This module provides multi-timeframe data aggregation capabilities for
live market data streaming, supporting regime detection across different
timeframes with proper OHLC aggregation.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from collections import deque
import logging
from threading import Lock

logger = logging.getLogger(__name__)

class MarketDataAggregator:
    """
    Multi-timeframe market data aggregator for live streaming
    
    Aggregates tick data into multiple timeframes (1min, 5min, 15min, 30min, 1hour)
    with proper OHLC calculation and volume aggregation for regime detection.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize Market Data Aggregator
        
        Args:
            config (Dict, optional): Configuration parameters
        """
        self.config = config or {}
        
        # Supported timeframes (in minutes)
        self.timeframes = {
            '1min': 1,
            '5min': 5,
            '15min': 15,
            '30min': 30,
            '1hour': 60
        }
        
        # Data storage for each timeframe
        self.timeframe_data = {}
        self.current_candles = {}
        self.last_candle_times = {}
        
        # Initialize storage for each timeframe
        for tf_name in self.timeframes.keys():
            max_candles = self.config.get(f'{tf_name}_max_candles', 1000)
            self.timeframe_data[tf_name] = deque(maxlen=max_candles)
            self.current_candles[tf_name] = None
            self.last_candle_times[tf_name] = None
        
        # Thread safety
        self.lock = Lock()
        
        # Statistics
        self.stats = {
            'total_ticks_processed': 0,
            'candles_generated': {tf: 0 for tf in self.timeframes.keys()},
            'last_update': None,
            'start_time': datetime.now()
        }
        
        logger.info("MarketDataAggregator initialized with timeframes: %s", list(self.timeframes.keys()))
    
    def process_tick(self, tick_data: Dict[str, Any]) -> Dict[str, List[Dict[str, Any]]]:
        """
        Process incoming tick data and update all timeframes
        
        Args:
            tick_data (Dict): Tick data with timestamp, price, volume, etc.
            
        Returns:
            Dict: Updated candles for each timeframe (only if new candles were created)
        """
        try:
            with self.lock:
                updated_timeframes = {}
                
                # Validate tick data
                if not self._validate_tick_data(tick_data):
                    logger.warning("Invalid tick data received: %s", tick_data)
                    return {}
                
                current_time = tick_data.get('timestamp', datetime.now())
                if isinstance(current_time, str):
                    current_time = pd.to_datetime(current_time)
                
                # Process each timeframe
                for tf_name, tf_minutes in self.timeframes.items():
                    new_candles = self._update_timeframe(tf_name, tf_minutes, tick_data, current_time)
                    if new_candles:
                        updated_timeframes[tf_name] = new_candles
                
                # Update statistics
                self.stats['total_ticks_processed'] += 1
                self.stats['last_update'] = current_time
                
                return updated_timeframes
                
        except Exception as e:
            logger.error("Error processing tick data: %s", e)
            return {}
    
    def _validate_tick_data(self, tick_data: Dict[str, Any]) -> bool:
        """Validate incoming tick data"""
        required_fields = ['timestamp', 'close']
        
        for field in required_fields:
            if field not in tick_data:
                return False
        
        # Validate price data
        try:
            price = float(tick_data['close'])
            if price <= 0:
                return False
        except (ValueError, TypeError):
            return False
        
        return True
    
    def _update_timeframe(self, tf_name: str, tf_minutes: int, tick_data: Dict[str, Any], 
                         current_time: datetime) -> Optional[List[Dict[str, Any]]]:
        """Update specific timeframe with tick data"""
        try:
            # Calculate candle start time for this timeframe
            candle_start = self._get_candle_start_time(current_time, tf_minutes)
            
            # Check if we need a new candle
            if (self.last_candle_times[tf_name] is None or 
                candle_start > self.last_candle_times[tf_name]):
                
                # Complete previous candle if it exists
                completed_candles = []
                if self.current_candles[tf_name] is not None:
                    completed_candle = self.current_candles[tf_name].copy()
                    self.timeframe_data[tf_name].append(completed_candle)
                    completed_candles.append(completed_candle)
                    self.stats['candles_generated'][tf_name] += 1
                
                # Start new candle
                self.current_candles[tf_name] = self._create_new_candle(tick_data, candle_start)
                self.last_candle_times[tf_name] = candle_start
                
                return completed_candles if completed_candles else None
            
            else:
                # Update current candle
                if self.current_candles[tf_name] is not None:
                    self._update_current_candle(self.current_candles[tf_name], tick_data)
                
                return None
                
        except Exception as e:
            logger.error("Error updating timeframe %s: %s", tf_name, e)
            return None
    
    def _get_candle_start_time(self, current_time: datetime, tf_minutes: int) -> datetime:
        """Calculate candle start time for given timeframe"""
        try:
            if tf_minutes == 1:
                return current_time.replace(second=0, microsecond=0)
            elif tf_minutes == 5:
                minute = (current_time.minute // 5) * 5
                return current_time.replace(minute=minute, second=0, microsecond=0)
            elif tf_minutes == 15:
                minute = (current_time.minute // 15) * 15
                return current_time.replace(minute=minute, second=0, microsecond=0)
            elif tf_minutes == 30:
                minute = (current_time.minute // 30) * 30
                return current_time.replace(minute=minute, second=0, microsecond=0)
            elif tf_minutes == 60:
                return current_time.replace(minute=0, second=0, microsecond=0)
            else:
                # Custom timeframe
                minute = (current_time.minute // tf_minutes) * tf_minutes
                return current_time.replace(minute=minute, second=0, microsecond=0)
                
        except Exception as e:
            logger.error("Error calculating candle start time: %s", e)
            return current_time.replace(second=0, microsecond=0)
    
    def _create_new_candle(self, tick_data: Dict[str, Any], candle_start: datetime) -> Dict[str, Any]:
        """Create new candle from tick data"""
        price = float(tick_data['close'])
        volume = tick_data.get('volume', 0)
        
        return {
            'timestamp': candle_start,
            'open': price,
            'high': price,
            'low': price,
            'close': price,
            'volume': volume,
            'tick_count': 1,
            'symbol': tick_data.get('symbol', 'UNKNOWN'),
            'token': tick_data.get('token', 0)
        }
    
    def _update_current_candle(self, candle: Dict[str, Any], tick_data: Dict[str, Any]):
        """Update current candle with new tick data"""
        price = float(tick_data['close'])
        volume = tick_data.get('volume', 0)
        
        # Update OHLC
        candle['high'] = max(candle['high'], price)
        candle['low'] = min(candle['low'], price)
        candle['close'] = price
        
        # Update volume and tick count
        candle['volume'] += volume
        candle['tick_count'] += 1
    
    def get_timeframe_data(self, timeframe: str, limit: int = 100) -> List[Dict[str, Any]]:
        """
        Get historical data for specific timeframe
        
        Args:
            timeframe (str): Timeframe name (e.g., '1min', '5min')
            limit (int): Maximum number of candles to return
            
        Returns:
            List[Dict]: List of candle data
        """
        try:
            with self.lock:
                if timeframe not in self.timeframe_data:
                    return []
                
                data = list(self.timeframe_data[timeframe])
                
                # Add current candle if it exists
                if self.current_candles[timeframe] is not None:
                    data.append(self.current_candles[timeframe])
                
                # Return last 'limit' candles
                return data[-limit:] if limit > 0 else data
                
        except Exception as e:
            logger.error("Error getting timeframe data: %s", e)
            return []
    
    def get_latest_candle(self, timeframe: str) -> Optional[Dict[str, Any]]:
        """Get the latest candle for specific timeframe"""
        try:
            with self.lock:
                if timeframe not in self.timeframe_data:
                    return None
                
                # Return current candle if available, otherwise last completed candle
                if self.current_candles[timeframe] is not None:
                    return self.current_candles[timeframe].copy()
                elif self.timeframe_data[timeframe]:
                    return list(self.timeframe_data[timeframe])[-1].copy()
                else:
                    return None
                    
        except Exception as e:
            logger.error("Error getting latest candle: %s", e)
            return None
    
    def get_ohlc_dataframe(self, timeframe: str, limit: int = 100) -> pd.DataFrame:
        """
        Get OHLC data as pandas DataFrame
        
        Args:
            timeframe (str): Timeframe name
            limit (int): Maximum number of candles
            
        Returns:
            pd.DataFrame: OHLC data with timestamp index
        """
        try:
            data = self.get_timeframe_data(timeframe, limit)
            
            if not data:
                return pd.DataFrame()
            
            df = pd.DataFrame(data)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df.set_index('timestamp', inplace=True)
            
            # Ensure numeric columns
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            
            return df
            
        except Exception as e:
            logger.error("Error creating OHLC DataFrame: %s", e)
            return pd.DataFrame()
    
    def calculate_technical_indicators(self, timeframe: str, limit: int = 100) -> Dict[str, Any]:
        """
        Calculate technical indicators for regime detection
        
        Args:
            timeframe (str): Timeframe name
            limit (int): Number of candles to use
            
        Returns:
            Dict: Technical indicators
        """
        try:
            df = self.get_ohlc_dataframe(timeframe, limit)
            
            if df.empty or len(df) < 20:
                return {}
            
            indicators = {}
            
            # Moving averages
            indicators['sma_20'] = df['close'].rolling(20).mean().iloc[-1]
            indicators['sma_50'] = df['close'].rolling(50).mean().iloc[-1] if len(df) >= 50 else None
            indicators['ema_20'] = df['close'].ewm(span=20).mean().iloc[-1]
            
            # Volatility indicators
            indicators['atr_14'] = self._calculate_atr(df, 14)
            indicators['volatility'] = df['close'].pct_change().rolling(20).std().iloc[-1]
            
            # Momentum indicators
            indicators['rsi_14'] = self._calculate_rsi(df['close'], 14)
            indicators['momentum'] = (df['close'].iloc[-1] - df['close'].iloc[-10]) / df['close'].iloc[-10] if len(df) >= 10 else 0
            
            # Volume indicators
            indicators['volume_sma'] = df['volume'].rolling(20).mean().iloc[-1]
            indicators['volume_ratio'] = df['volume'].iloc[-1] / indicators['volume_sma'] if indicators['volume_sma'] > 0 else 1
            
            # Price action
            indicators['price_change'] = df['close'].pct_change().iloc[-1]
            indicators['high_low_ratio'] = (df['high'].iloc[-1] - df['low'].iloc[-1]) / df['close'].iloc[-1]
            
            return indicators
            
        except Exception as e:
            logger.error("Error calculating technical indicators: %s", e)
            return {}
    
    def _calculate_atr(self, df: pd.DataFrame, period: int = 14) -> float:
        """Calculate Average True Range"""
        try:
            high_low = df['high'] - df['low']
            high_close = np.abs(df['high'] - df['close'].shift())
            low_close = np.abs(df['low'] - df['close'].shift())
            
            true_range = np.maximum(high_low, np.maximum(high_close, low_close))
            atr = true_range.rolling(period).mean().iloc[-1]
            
            return atr if not np.isnan(atr) else 0.0
            
        except Exception as e:
            logger.error("Error calculating ATR: %s", e)
            return 0.0
    
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> float:
        """Calculate Relative Strength Index"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            
            return rsi.iloc[-1] if not np.isnan(rsi.iloc[-1]) else 50.0
            
        except Exception as e:
            logger.error("Error calculating RSI: %s", e)
            return 50.0
    
    def get_aggregator_statistics(self) -> Dict[str, Any]:
        """Get aggregator statistics"""
        try:
            with self.lock:
                current_time = datetime.now()
                uptime = (current_time - self.stats['start_time']).total_seconds()
                
                stats = self.stats.copy()
                stats['uptime_seconds'] = uptime
                stats['ticks_per_second'] = stats['total_ticks_processed'] / uptime if uptime > 0 else 0
                
                # Add timeframe statistics
                stats['timeframe_stats'] = {}
                for tf_name in self.timeframes.keys():
                    stats['timeframe_stats'][tf_name] = {
                        'total_candles': len(self.timeframe_data[tf_name]),
                        'current_candle_exists': self.current_candles[tf_name] is not None,
                        'last_candle_time': self.last_candle_times[tf_name].isoformat() if self.last_candle_times[tf_name] else None
                    }
                
                return stats
                
        except Exception as e:
            logger.error("Error getting aggregator statistics: %s", e)
            return {'error': str(e)}
    
    def reset_aggregator(self):
        """Reset aggregator state"""
        try:
            with self.lock:
                # Clear all data
                for tf_name in self.timeframes.keys():
                    self.timeframe_data[tf_name].clear()
                    self.current_candles[tf_name] = None
                    self.last_candle_times[tf_name] = None
                
                # Reset statistics
                self.stats = {
                    'total_ticks_processed': 0,
                    'candles_generated': {tf: 0 for tf in self.timeframes.keys()},
                    'last_update': None,
                    'start_time': datetime.now()
                }
                
                logger.info("MarketDataAggregator reset successfully")
                
        except Exception as e:
            logger.error("Error resetting aggregator: %s", e)
