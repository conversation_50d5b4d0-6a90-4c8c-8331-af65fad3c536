"""
Data-Driven Strategy Consolidator

This module provides data-driven strategy consolidation without pre-assumptions,
analyzing strategies purely based on historical performance and statistical significance.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import logging
from pathlib import Path
import json
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans

logger = logging.getLogger(__name__)

class DataDrivenConsolidator:
    """
    Data-driven strategy consolidator
    
    This class analyzes strategies from multiple sources without making
    assumptions about their nature, focusing purely on statistical
    performance and regime sensitivity.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize Data-Driven Consolidator
        
        Args:
            config (Dict, optional): Configuration parameters
        """
        self.config = config or {}
        
        # Statistical thresholds
        self.significance_threshold = self.config.get('significance_threshold', 0.05)
        self.min_trades_threshold = self.config.get('min_trades', 30)
        self.min_sharpe_ratio = self.config.get('min_sharpe_ratio', 0.5)
        self.min_win_rate = self.config.get('min_win_rate', 0.4)
        
        # Consolidation parameters
        self.correlation_threshold = self.config.get('correlation_threshold', 0.8)
        self.performance_weight = self.config.get('performance_weight', 0.6)
        self.consistency_weight = self.config.get('consistency_weight', 0.4)
        
        # Strategy storage
        self.analyzed_strategies = []
        self.consolidated_strategies = []
        self.performance_metrics = {}
        
        logger.info("DataDrivenConsolidator initialized")
    
    def consolidate_strategies(self, strategy_sources: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Consolidate strategies from multiple sources
        
        Args:
            strategy_sources (List[Dict]): List of strategy source configurations
            
        Returns:
            List[Dict]: Consolidated strategies with performance metrics
        """
        try:
            logger.info(f"Starting consolidation of {len(strategy_sources)} strategy sources")
            
            # Step 1: Parse and analyze all strategies
            all_strategies = []
            for source in strategy_sources:
                strategies = self._parse_strategy_source(source)
                all_strategies.extend(strategies)
            
            logger.info(f"Parsed {len(all_strategies)} individual strategies")
            
            # Step 2: Analyze performance for each strategy
            analyzed_strategies = []
            for strategy in all_strategies:
                analysis = self._analyze_strategy_performance(strategy)
                if analysis and self._is_statistically_significant(analysis):
                    analyzed_strategies.append(analysis)
            
            logger.info(f"Found {len(analyzed_strategies)} statistically significant strategies")
            
            # Step 3: Cluster similar strategies
            strategy_clusters = self._cluster_strategies(analyzed_strategies)
            
            # Step 4: Consolidate within clusters
            consolidated_strategies = []
            for cluster in strategy_clusters:
                consolidated = self._consolidate_cluster(cluster)
                if consolidated:
                    consolidated_strategies.append(consolidated)
            
            # Step 5: Rank and filter final strategies
            final_strategies = self._rank_and_filter_strategies(consolidated_strategies)
            
            self.consolidated_strategies = final_strategies
            
            logger.info(f"Consolidation complete: {len(final_strategies)} final strategies")
            
            return final_strategies
            
        except Exception as e:
            logger.error(f"Error in strategy consolidation: {e}")
            return []
    
    def _parse_strategy_source(self, source: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Parse strategies from a source"""
        try:
            source_type = source.get('type', 'UNKNOWN')
            source_path = source.get('path', '')
            
            if source_type == 'TBS':
                return self._parse_tbs_strategies(source_path)
            elif source_type == 'OI':
                return self._parse_oi_strategies(source_path)
            elif source_type == 'EXTERNAL':
                return self._parse_external_strategies(source_path)
            elif source_type == 'CSV':
                return self._parse_csv_strategies(source_path)
            elif source_type == 'JSON':
                return self._parse_json_strategies(source_path)
            else:
                logger.warning(f"Unknown source type: {source_type}")
                return []
                
        except Exception as e:
            logger.error(f"Error parsing strategy source {source}: {e}")
            return []
    
    def _parse_tbs_strategies(self, file_path: str) -> List[Dict[str, Any]]:
        """Parse TBS strategies from Excel file"""
        try:
            if not Path(file_path).exists():
                logger.warning(f"TBS file not found: {file_path}")
                return []
            
            # Read TBS Excel file
            df = pd.read_excel(file_path, sheet_name=None)
            
            strategies = []
            for sheet_name, sheet_data in df.items():
                if 'pnl' in sheet_data.columns.str.lower():
                    strategy = {
                        'source_type': 'TBS',
                        'source_file': file_path,
                        'strategy_name': sheet_name,
                        'data': sheet_data,
                        'pnl_column': self._find_pnl_column(sheet_data),
                        'date_column': self._find_date_column(sheet_data)
                    }
                    strategies.append(strategy)
            
            return strategies
            
        except Exception as e:
            logger.error(f"Error parsing TBS strategies: {e}")
            return []
    
    def _parse_oi_strategies(self, file_path: str) -> List[Dict[str, Any]]:
        """Parse OI strategies from CSV/Excel file"""
        try:
            if not Path(file_path).exists():
                logger.warning(f"OI file not found: {file_path}")
                return []
            
            # Determine file type and read
            if file_path.endswith('.csv'):
                df = pd.read_csv(file_path)
            else:
                df = pd.read_excel(file_path)
            
            # Extract strategy data
            strategy = {
                'source_type': 'OI',
                'source_file': file_path,
                'strategy_name': Path(file_path).stem,
                'data': df,
                'pnl_column': self._find_pnl_column(df),
                'date_column': self._find_date_column(df)
            }
            
            return [strategy]
            
        except Exception as e:
            logger.error(f"Error parsing OI strategies: {e}")
            return []
    
    def _parse_external_strategies(self, file_path: str) -> List[Dict[str, Any]]:
        """Parse external strategies from JSON file"""
        try:
            if not Path(file_path).exists():
                logger.warning(f"External file not found: {file_path}")
                return []
            
            with open(file_path, 'r') as f:
                data = json.load(f)
            
            strategies = []
            if isinstance(data, list):
                for i, strategy_data in enumerate(data):
                    strategy = {
                        'source_type': 'EXTERNAL',
                        'source_file': file_path,
                        'strategy_name': strategy_data.get('name', f'external_{i}'),
                        'data': pd.DataFrame(strategy_data.get('trades', [])),
                        'pnl_column': 'pnl',
                        'date_column': 'date'
                    }
                    strategies.append(strategy)
            
            return strategies
            
        except Exception as e:
            logger.error(f"Error parsing external strategies: {e}")
            return []
    
    def _parse_csv_strategies(self, file_path: str) -> List[Dict[str, Any]]:
        """Parse strategies from CSV file"""
        try:
            df = pd.read_csv(file_path)
            
            strategy = {
                'source_type': 'CSV',
                'source_file': file_path,
                'strategy_name': Path(file_path).stem,
                'data': df,
                'pnl_column': self._find_pnl_column(df),
                'date_column': self._find_date_column(df)
            }
            
            return [strategy]
            
        except Exception as e:
            logger.error(f"Error parsing CSV strategies: {e}")
            return []
    
    def _parse_json_strategies(self, file_path: str) -> List[Dict[str, Any]]:
        """Parse strategies from JSON file"""
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
            
            df = pd.DataFrame(data)
            
            strategy = {
                'source_type': 'JSON',
                'source_file': file_path,
                'strategy_name': Path(file_path).stem,
                'data': df,
                'pnl_column': self._find_pnl_column(df),
                'date_column': self._find_date_column(df)
            }
            
            return [strategy]
            
        except Exception as e:
            logger.error(f"Error parsing JSON strategies: {e}")
            return []
    
    def _find_pnl_column(self, df: pd.DataFrame) -> Optional[str]:
        """Find PnL column in DataFrame"""
        pnl_keywords = ['pnl', 'profit', 'loss', 'return', 'pl', 'net_pnl']
        
        for col in df.columns:
            if any(keyword in col.lower() for keyword in pnl_keywords):
                return col
        
        # If no obvious PnL column, look for numeric columns
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 0:
            return numeric_cols[0]
        
        return None
    
    def _find_date_column(self, df: pd.DataFrame) -> Optional[str]:
        """Find date column in DataFrame"""
        date_keywords = ['date', 'time', 'timestamp', 'datetime']
        
        for col in df.columns:
            if any(keyword in col.lower() for keyword in date_keywords):
                return col
        
        # Look for datetime columns
        for col in df.columns:
            try:
                pd.to_datetime(df[col].iloc[0])
                return col
            except:
                continue
        
        return None
    
    def _analyze_strategy_performance(self, strategy: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Analyze performance of a single strategy"""
        try:
            df = strategy['data']
            pnl_col = strategy['pnl_column']
            date_col = strategy['date_column']
            
            if pnl_col is None or pnl_col not in df.columns:
                logger.warning(f"No PnL column found for strategy {strategy['strategy_name']}")
                return None
            
            # Extract PnL data
            pnl_data = df[pnl_col].dropna()
            
            if len(pnl_data) < self.min_trades_threshold:
                logger.warning(f"Insufficient trades for strategy {strategy['strategy_name']}: {len(pnl_data)}")
                return None
            
            # Calculate performance metrics
            total_return = pnl_data.sum()
            mean_return = pnl_data.mean()
            std_return = pnl_data.std()
            
            # Sharpe ratio
            sharpe_ratio = mean_return / std_return if std_return > 0 else 0
            
            # Win rate
            win_rate = (pnl_data > 0).mean()
            
            # Maximum drawdown
            cumulative_pnl = pnl_data.cumsum()
            running_max = cumulative_pnl.expanding().max()
            drawdown = (cumulative_pnl - running_max) / running_max
            max_drawdown = drawdown.min()
            
            # Profit factor
            gross_profit = pnl_data[pnl_data > 0].sum()
            gross_loss = abs(pnl_data[pnl_data < 0].sum())
            profit_factor = gross_profit / gross_loss if gross_loss > 0 else np.inf
            
            # Statistical significance tests
            t_stat, p_value = stats.ttest_1samp(pnl_data, 0)
            
            # Consistency metrics
            monthly_returns = self._calculate_monthly_returns(df, pnl_col, date_col)
            consistency_score = self._calculate_consistency_score(monthly_returns)
            
            analysis = {
                'strategy': strategy,
                'performance_metrics': {
                    'total_return': total_return,
                    'mean_return': mean_return,
                    'std_return': std_return,
                    'sharpe_ratio': sharpe_ratio,
                    'win_rate': win_rate,
                    'max_drawdown': max_drawdown,
                    'profit_factor': profit_factor,
                    'total_trades': len(pnl_data),
                    'consistency_score': consistency_score
                },
                'statistical_tests': {
                    't_statistic': t_stat,
                    'p_value': p_value,
                    'is_significant': p_value < self.significance_threshold
                },
                'pnl_data': pnl_data,
                'monthly_returns': monthly_returns
            }
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing strategy performance: {e}")
            return None
    
    def _calculate_monthly_returns(self, df: pd.DataFrame, pnl_col: str, 
                                 date_col: Optional[str]) -> pd.Series:
        """Calculate monthly returns"""
        try:
            if date_col and date_col in df.columns:
                df_copy = df.copy()
                df_copy[date_col] = pd.to_datetime(df_copy[date_col])
                df_copy.set_index(date_col, inplace=True)
                monthly_returns = df_copy[pnl_col].resample('M').sum()
            else:
                # If no date column, assume daily data and group by 30-day periods
                pnl_data = df[pnl_col].dropna()
                monthly_returns = pd.Series([
                    pnl_data.iloc[i:i+30].sum() 
                    for i in range(0, len(pnl_data), 30)
                ])
            
            return monthly_returns.dropna()
            
        except Exception as e:
            logger.error(f"Error calculating monthly returns: {e}")
            return pd.Series()
    
    def _calculate_consistency_score(self, monthly_returns: pd.Series) -> float:
        """Calculate consistency score based on monthly returns"""
        try:
            if len(monthly_returns) < 3:
                return 0.0
            
            # Percentage of positive months
            positive_months = (monthly_returns > 0).mean()
            
            # Coefficient of variation (lower is better)
            cv = monthly_returns.std() / abs(monthly_returns.mean()) if monthly_returns.mean() != 0 else np.inf
            cv_score = 1 / (1 + cv) if cv != np.inf else 0
            
            # Combine metrics
            consistency_score = (positive_months * 0.6) + (cv_score * 0.4)
            
            return consistency_score
            
        except Exception as e:
            logger.error(f"Error calculating consistency score: {e}")
            return 0.0
    
    def _is_statistically_significant(self, analysis: Dict[str, Any]) -> bool:
        """Check if strategy is statistically significant"""
        try:
            metrics = analysis['performance_metrics']
            stats_tests = analysis['statistical_tests']
            
            # Check minimum thresholds
            if metrics['total_trades'] < self.min_trades_threshold:
                return False
            
            if metrics['sharpe_ratio'] < self.min_sharpe_ratio:
                return False
            
            if metrics['win_rate'] < self.min_win_rate:
                return False
            
            # Check statistical significance
            if not stats_tests['is_significant']:
                return False
            
            # Check for reasonable profit factor
            if metrics['profit_factor'] < 1.1:  # At least 10% more profit than loss
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking statistical significance: {e}")
            return False
    
    def _cluster_strategies(self, strategies: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
        """Cluster similar strategies"""
        try:
            if len(strategies) < 2:
                return [strategies]
            
            # Extract features for clustering
            features = []
            for strategy in strategies:
                metrics = strategy['performance_metrics']
                feature_vector = [
                    metrics['sharpe_ratio'],
                    metrics['win_rate'],
                    metrics['max_drawdown'],
                    metrics['consistency_score'],
                    np.log(metrics['profit_factor']) if metrics['profit_factor'] != np.inf else 5
                ]
                features.append(feature_vector)
            
            # Standardize features
            scaler = StandardScaler()
            features_scaled = scaler.fit_transform(features)
            
            # Determine optimal number of clusters
            n_clusters = min(len(strategies) // 2, 5)  # Max 5 clusters
            
            if n_clusters < 2:
                return [strategies]
            
            # Perform clustering
            kmeans = KMeans(n_clusters=n_clusters, random_state=42)
            cluster_labels = kmeans.fit_predict(features_scaled)
            
            # Group strategies by cluster
            clusters = [[] for _ in range(n_clusters)]
            for i, label in enumerate(cluster_labels):
                clusters[label].append(strategies[i])
            
            # Remove empty clusters
            clusters = [cluster for cluster in clusters if cluster]
            
            return clusters
            
        except Exception as e:
            logger.error(f"Error clustering strategies: {e}")
            return [strategies]
    
    def _consolidate_cluster(self, cluster: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """Consolidate strategies within a cluster"""
        try:
            if not cluster:
                return None
            
            if len(cluster) == 1:
                return cluster[0]
            
            # Find the best strategy in the cluster
            best_strategy = max(cluster, key=lambda s: s['performance_metrics']['sharpe_ratio'])
            
            # Calculate ensemble metrics
            all_pnl_data = []
            for strategy in cluster:
                all_pnl_data.extend(strategy['pnl_data'].tolist())
            
            ensemble_pnl = pd.Series(all_pnl_data)
            
            # Recalculate metrics for ensemble
            ensemble_metrics = {
                'total_return': ensemble_pnl.sum(),
                'mean_return': ensemble_pnl.mean(),
                'std_return': ensemble_pnl.std(),
                'sharpe_ratio': ensemble_pnl.mean() / ensemble_pnl.std() if ensemble_pnl.std() > 0 else 0,
                'win_rate': (ensemble_pnl > 0).mean(),
                'total_trades': len(ensemble_pnl),
                'strategies_count': len(cluster)
            }
            
            consolidated = {
                'type': 'CONSOLIDATED',
                'best_strategy': best_strategy,
                'cluster_strategies': cluster,
                'ensemble_metrics': ensemble_metrics,
                'consolidation_score': self._calculate_consolidation_score(cluster, ensemble_metrics)
            }
            
            return consolidated
            
        except Exception as e:
            logger.error(f"Error consolidating cluster: {e}")
            return None
    
    def _calculate_consolidation_score(self, cluster: List[Dict[str, Any]], 
                                     ensemble_metrics: Dict[str, Any]) -> float:
        """Calculate consolidation score"""
        try:
            # Average individual performance
            avg_individual_sharpe = np.mean([
                s['performance_metrics']['sharpe_ratio'] for s in cluster
            ])
            
            # Ensemble performance
            ensemble_sharpe = ensemble_metrics['sharpe_ratio']
            
            # Diversity bonus
            diversity_score = len(cluster) / 10.0  # Bonus for diversity
            
            # Consolidation score
            score = (ensemble_sharpe * 0.7) + (avg_individual_sharpe * 0.2) + (diversity_score * 0.1)
            
            return score
            
        except Exception as e:
            logger.error(f"Error calculating consolidation score: {e}")
            return 0.0
    
    def _rank_and_filter_strategies(self, strategies: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Rank and filter final strategies"""
        try:
            # Calculate final scores
            for strategy in strategies:
                if strategy.get('type') == 'CONSOLIDATED':
                    strategy['final_score'] = strategy['consolidation_score']
                else:
                    metrics = strategy['performance_metrics']
                    strategy['final_score'] = (
                        metrics['sharpe_ratio'] * self.performance_weight +
                        metrics['consistency_score'] * self.consistency_weight
                    )
            
            # Sort by final score
            strategies.sort(key=lambda s: s['final_score'], reverse=True)
            
            # Filter top strategies (max 10)
            top_strategies = strategies[:10]
            
            return top_strategies
            
        except Exception as e:
            logger.error(f"Error ranking and filtering strategies: {e}")
            return strategies
    
    def get_consolidation_summary(self) -> Dict[str, Any]:
        """Get consolidation summary"""
        try:
            if not self.consolidated_strategies:
                return {'message': 'No strategies consolidated yet'}
            
            summary = {
                'total_strategies': len(self.consolidated_strategies),
                'consolidated_count': len([s for s in self.consolidated_strategies if s.get('type') == 'CONSOLIDATED']),
                'individual_count': len([s for s in self.consolidated_strategies if s.get('type') != 'CONSOLIDATED']),
                'top_strategy_score': self.consolidated_strategies[0]['final_score'],
                'average_score': np.mean([s['final_score'] for s in self.consolidated_strategies])
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"Error getting consolidation summary: {e}")
            return {'error': str(e)}
