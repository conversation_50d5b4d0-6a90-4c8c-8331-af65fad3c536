"""
Strategy Consolidator Module

This module provides data-driven strategy consolidation capabilities,
analyzing and merging strategies from multiple sources based on
historical performance and statistical significance.
"""

from .base_consolidator import DataDrivenConsolidator
from .performance_analyzer import StrategyPerformanceAnalyzer
from .regime_integrator import RegimeStrategyIntegrator

__version__ = "1.0.0"
__author__ = "Backtester V2 Team"

__all__ = [
    'DataDrivenConsolidator',
    'StrategyPerformanceAnalyzer',
    'RegimeStrategyIntegrator'
]
