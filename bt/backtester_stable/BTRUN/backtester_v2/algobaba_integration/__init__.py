"""
Algobaba Integration Module

This module provides integration with the Algobaba order execution system,
enabling regime-aware order management and strategy execution.
"""

from .regime_order_manager import RegimeOrderManager
from .strategy_executor import RegimeStrategyExecutor
from .portfolio_manager import RegimePortfolioManager

__version__ = "1.0.0"
__author__ = "Backtester V2 Team"

__all__ = [
    'RegimeOrderManager',
    'RegimeStrategyExecutor',
    'RegimePortfolioManager'
]
