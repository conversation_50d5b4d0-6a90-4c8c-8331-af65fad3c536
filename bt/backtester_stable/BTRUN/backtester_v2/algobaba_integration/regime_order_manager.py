"""
Regime-Aware Order Manager for Algobaba Integration

This module provides regime-aware order management that integrates with
the existing Algobaba infrastructure while applying regime-based adjustments.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import json

# Import existing OI-shift modules
import sys
import os
sys.path.append('/srv/samba/shared/oi-shift-dev')

try:
    from brokers.algobaba import place_entry_order, place_exit_order
    from config import Config
    from commons.models import Strategy
    from commons.enums import Underlying, OiType
except ImportError as e:
    logging.warning(f"Could not import OI-shift modules: {e}")
    # Create mock functions for testing
    def place_entry_order(order_data):
        return ["mock_portfolio_1", "mock_portfolio_2"]
    
    def place_exit_order(order_data):
        return True
    
    class Config:
        BASE_URLS = ["http://localhost:8080"]
        OPTION_PORTFOLIO = ["portfolio1", "portfolio2"]
        STRATEGY_TAGS = ["tag1", "tag2"]
    
    class Strategy:
        def __init__(self):
            self.instance_id = "mock_strategy"
            self.shift_delay = 60
            self.exit_time = 900

# Import market regime components
from ..market_regime.enhanced_regime_detector import Enhanced18RegimeType

logger = logging.getLogger(__name__)

class RegimeOrderManager:
    """
    Regime-aware order management for Algobaba integration
    
    This class integrates with the existing Algobaba infrastructure while
    applying intelligent regime-based adjustments to order parameters.
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize Regime Order Manager
        
        Args:
            config (Dict, optional): Configuration parameters
        """
        self.config = config or {}
        
        # Use existing Algobaba configuration
        self.algobaba_config = {
            'base_urls': Config.BASE_URLS,
            'portfolio_names': Config.OPTION_PORTFOLIO,
            'strategy_tags': Config.STRATEGY_TAGS
        }
        
        # Regime-specific order adjustments
        self.regime_adjustments = self._initialize_regime_adjustments()
        
        # Order tracking
        self.active_orders = {}
        self.order_history = []
        self.regime_performance = {}
        
        # Risk management
        self.risk_limits = {
            'max_position_size_multiplier': 2.0,
            'min_position_size_multiplier': 0.1,
            'max_daily_orders': 100,
            'max_regime_exposure': 0.5  # Max 50% of capital per regime
        }
        
        logger.info("RegimeOrderManager initialized with Algobaba integration")
    
    def _initialize_regime_adjustments(self) -> Dict[str, Dict[str, Any]]:
        """Initialize regime-specific order adjustments"""
        return {
            # Strong Bullish Regimes
            Enhanced18RegimeType.HIGH_VOLATILE_STRONG_BULLISH.value: {
                'position_size_multiplier': 1.5,
                'stop_loss_multiplier': 0.8,
                'take_profit_multiplier': 1.3,
                'urgency_factor': 0.7,  # Faster execution
                'risk_tolerance': 'HIGH'
            },
            Enhanced18RegimeType.NORMAL_VOLATILE_STRONG_BULLISH.value: {
                'position_size_multiplier': 1.3,
                'stop_loss_multiplier': 0.9,
                'take_profit_multiplier': 1.2,
                'urgency_factor': 0.8,
                'risk_tolerance': 'MEDIUM_HIGH'
            },
            Enhanced18RegimeType.LOW_VOLATILE_STRONG_BULLISH.value: {
                'position_size_multiplier': 1.4,
                'stop_loss_multiplier': 1.0,
                'take_profit_multiplier': 1.1,
                'urgency_factor': 0.9,
                'risk_tolerance': 'MEDIUM'
            },
            
            # Mild Bullish Regimes
            Enhanced18RegimeType.HIGH_VOLATILE_MILD_BULLISH.value: {
                'position_size_multiplier': 1.1,
                'stop_loss_multiplier': 0.8,
                'take_profit_multiplier': 1.1,
                'urgency_factor': 0.8,
                'risk_tolerance': 'MEDIUM'
            },
            Enhanced18RegimeType.NORMAL_VOLATILE_MILD_BULLISH.value: {
                'position_size_multiplier': 1.0,
                'stop_loss_multiplier': 1.0,
                'take_profit_multiplier': 1.0,
                'urgency_factor': 1.0,
                'risk_tolerance': 'MEDIUM'
            },
            Enhanced18RegimeType.LOW_VOLATILE_MILD_BULLISH.value: {
                'position_size_multiplier': 1.2,
                'stop_loss_multiplier': 1.1,
                'take_profit_multiplier': 1.0,
                'urgency_factor': 1.1,
                'risk_tolerance': 'LOW'
            },
            
            # Neutral Regimes
            Enhanced18RegimeType.HIGH_VOLATILE_NEUTRAL.value: {
                'position_size_multiplier': 0.8,
                'stop_loss_multiplier': 0.7,
                'take_profit_multiplier': 0.9,
                'urgency_factor': 0.9,
                'risk_tolerance': 'MEDIUM'
            },
            Enhanced18RegimeType.NORMAL_VOLATILE_NEUTRAL.value: {
                'position_size_multiplier': 0.9,
                'stop_loss_multiplier': 1.0,
                'take_profit_multiplier': 1.0,
                'urgency_factor': 1.0,
                'risk_tolerance': 'MEDIUM'
            },
            Enhanced18RegimeType.LOW_VOLATILE_NEUTRAL.value: {
                'position_size_multiplier': 1.0,
                'stop_loss_multiplier': 1.2,
                'take_profit_multiplier': 0.9,
                'urgency_factor': 1.2,
                'risk_tolerance': 'LOW'
            },
            
            # Sideways Regimes
            Enhanced18RegimeType.HIGH_VOLATILE_SIDEWAYS.value: {
                'position_size_multiplier': 0.6,
                'stop_loss_multiplier': 0.6,
                'take_profit_multiplier': 0.8,
                'urgency_factor': 0.8,
                'risk_tolerance': 'HIGH'
            },
            Enhanced18RegimeType.NORMAL_VOLATILE_SIDEWAYS.value: {
                'position_size_multiplier': 0.7,
                'stop_loss_multiplier': 0.8,
                'take_profit_multiplier': 0.9,
                'urgency_factor': 1.0,
                'risk_tolerance': 'MEDIUM'
            },
            Enhanced18RegimeType.LOW_VOLATILE_SIDEWAYS.value: {
                'position_size_multiplier': 0.8,
                'stop_loss_multiplier': 1.0,
                'take_profit_multiplier': 0.8,
                'urgency_factor': 1.3,
                'risk_tolerance': 'LOW'
            },
            
            # Bearish Regimes (mirror of bullish with opposite direction)
            Enhanced18RegimeType.HIGH_VOLATILE_MILD_BEARISH.value: {
                'position_size_multiplier': 1.1,
                'stop_loss_multiplier': 0.8,
                'take_profit_multiplier': 1.1,
                'urgency_factor': 0.8,
                'risk_tolerance': 'MEDIUM'
            },
            Enhanced18RegimeType.NORMAL_VOLATILE_MILD_BEARISH.value: {
                'position_size_multiplier': 1.0,
                'stop_loss_multiplier': 1.0,
                'take_profit_multiplier': 1.0,
                'urgency_factor': 1.0,
                'risk_tolerance': 'MEDIUM'
            },
            Enhanced18RegimeType.LOW_VOLATILE_MILD_BEARISH.value: {
                'position_size_multiplier': 1.2,
                'stop_loss_multiplier': 1.1,
                'take_profit_multiplier': 1.0,
                'urgency_factor': 1.1,
                'risk_tolerance': 'LOW'
            },
            Enhanced18RegimeType.HIGH_VOLATILE_STRONG_BEARISH.value: {
                'position_size_multiplier': 1.5,
                'stop_loss_multiplier': 0.8,
                'take_profit_multiplier': 1.3,
                'urgency_factor': 0.7,
                'risk_tolerance': 'HIGH'
            },
            Enhanced18RegimeType.NORMAL_VOLATILE_STRONG_BEARISH.value: {
                'position_size_multiplier': 1.3,
                'stop_loss_multiplier': 0.9,
                'take_profit_multiplier': 1.2,
                'urgency_factor': 0.8,
                'risk_tolerance': 'MEDIUM_HIGH'
            },
            Enhanced18RegimeType.LOW_VOLATILE_STRONG_BEARISH.value: {
                'position_size_multiplier': 1.4,
                'stop_loss_multiplier': 1.0,
                'take_profit_multiplier': 1.1,
                'urgency_factor': 0.9,
                'risk_tolerance': 'MEDIUM'
            }
        }
    
    def execute_regime_order(self, strategy: Strategy, regime_data: Dict[str, Any], 
                           order_signal: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute order with regime-aware adjustments
        
        Args:
            strategy (Strategy): Strategy instance
            regime_data (Dict): Current regime information
            order_signal (Dict): Order signal from strategy
            
        Returns:
            Dict: Execution result
        """
        try:
            regime_type = regime_data['regime_type'].value
            regime_confidence = regime_data['confidence']
            
            # Get regime adjustments
            adjustments = self.regime_adjustments.get(regime_type, {})
            
            # Apply regime-based adjustments to order
            adjusted_order = self._apply_regime_adjustments(
                order_signal, adjustments, regime_confidence
            )
            
            # Validate order against risk limits
            if not self._validate_order_risk(adjusted_order, regime_type):
                return {
                    'success': False,
                    'error': 'Order rejected due to risk limits',
                    'regime_type': regime_type
                }
            
            # Execute order based on action type
            if adjusted_order['action'] == 'ENTRY':
                result = self._execute_entry_order(adjusted_order, strategy, regime_data)
            elif adjusted_order['action'] == 'EXIT':
                result = self._execute_exit_order(adjusted_order, strategy, regime_data)
            else:
                return {
                    'success': False,
                    'error': f"Unknown order action: {adjusted_order['action']}"
                }
            
            # Track order execution
            self._track_order_execution(result, regime_data, adjusted_order)
            
            return result
            
        except Exception as e:
            logger.error(f"Error executing regime order: {e}")
            return {
                'success': False,
                'error': str(e),
                'regime_type': regime_data.get('regime_type', 'UNKNOWN')
            }
    
    def _apply_regime_adjustments(self, order_signal: Dict[str, Any], 
                                adjustments: Dict[str, Any], 
                                confidence: float) -> Dict[str, Any]:
        """Apply regime-specific adjustments to order"""
        try:
            adjusted_order = order_signal.copy()
            
            # Apply position size adjustment
            if 'position_size_multiplier' in adjustments:
                base_quantity = adjusted_order.get('quantity', 1)
                multiplier = adjustments['position_size_multiplier']
                
                # Apply confidence factor
                confidence_factor = 0.5 + (confidence * 0.5)  # Range: 0.5 to 1.0
                final_multiplier = multiplier * confidence_factor
                
                # Apply risk limits
                final_multiplier = max(
                    self.risk_limits['min_position_size_multiplier'],
                    min(final_multiplier, self.risk_limits['max_position_size_multiplier'])
                )
                
                adjusted_order['quantity'] = max(1, int(base_quantity * final_multiplier))
            
            # Apply stop loss adjustment
            if 'stop_loss_multiplier' in adjustments and 'stop_loss' in adjusted_order:
                adjusted_order['stop_loss'] *= adjustments['stop_loss_multiplier']
            
            # Apply take profit adjustment
            if 'take_profit_multiplier' in adjustments and 'take_profit' in adjusted_order:
                adjusted_order['take_profit'] *= adjustments['take_profit_multiplier']
            
            # Apply urgency factor (affects execution timing)
            if 'urgency_factor' in adjustments:
                adjusted_order['urgency_factor'] = adjustments['urgency_factor']
            
            # Add regime metadata
            adjusted_order['regime_metadata'] = {
                'regime_type': adjustments.get('risk_tolerance', 'MEDIUM'),
                'confidence': confidence,
                'adjustments_applied': list(adjustments.keys())
            }
            
            return adjusted_order
            
        except Exception as e:
            logger.error(f"Error applying regime adjustments: {e}")
            return order_signal
    
    def _validate_order_risk(self, order: Dict[str, Any], regime_type: str) -> bool:
        """Validate order against risk management rules"""
        try:
            # Check daily order limit
            today_orders = len([
                o for o in self.order_history 
                if o['timestamp'].date() == datetime.now().date()
            ])
            
            if today_orders >= self.risk_limits['max_daily_orders']:
                logger.warning(f"Daily order limit reached: {today_orders}")
                return False
            
            # Check regime exposure
            regime_exposure = self._calculate_regime_exposure(regime_type)
            if regime_exposure >= self.risk_limits['max_regime_exposure']:
                logger.warning(f"Regime exposure limit reached: {regime_exposure:.2f}")
                return False
            
            # Check position size limits
            quantity = order.get('quantity', 1)
            if quantity <= 0:
                logger.warning(f"Invalid quantity: {quantity}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating order risk: {e}")
            return False
    
    def _calculate_regime_exposure(self, regime_type: str) -> float:
        """Calculate current exposure to specific regime type"""
        try:
            regime_orders = [
                o for o in self.active_orders.values()
                if o.get('regime_type') == regime_type
            ]
            
            total_exposure = sum(o.get('quantity', 0) for o in regime_orders)
            
            # Normalize by some base value (could be portfolio value)
            base_value = 1000  # Placeholder
            return total_exposure / base_value
            
        except Exception as e:
            logger.error(f"Error calculating regime exposure: {e}")
            return 0.0
    
    def _execute_entry_order(self, order: Dict[str, Any], strategy: Strategy, 
                           regime_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute entry order through Algobaba"""
        try:
            # Prepare order data for Algobaba
            algobaba_order = self._prepare_algobaba_order(order, strategy, 'ENTRY')
            
            # Execute through existing Algobaba infrastructure
            portfolio_names = place_entry_order(algobaba_order)
            
            if portfolio_names:
                order_id = f"entry_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                
                # Track active order
                self.active_orders[order_id] = {
                    'order_id': order_id,
                    'strategy_id': strategy.instance_id,
                    'regime_type': regime_data['regime_type'].value,
                    'action': 'ENTRY',
                    'quantity': order['quantity'],
                    'portfolio_names': portfolio_names,
                    'timestamp': datetime.now(),
                    'status': 'ACTIVE'
                }
                
                return {
                    'success': True,
                    'order_id': order_id,
                    'portfolio_names': portfolio_names,
                    'regime_type': regime_data['regime_type'].value,
                    'adjusted_quantity': order['quantity']
                }
            else:
                return {
                    'success': False,
                    'error': 'Failed to place entry order through Algobaba'
                }
                
        except Exception as e:
            logger.error(f"Error executing entry order: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _execute_exit_order(self, order: Dict[str, Any], strategy: Strategy, 
                          regime_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute exit order through Algobaba"""
        try:
            # Prepare order data for Algobaba
            algobaba_order = self._prepare_algobaba_order(order, strategy, 'EXIT')
            
            # Execute through existing Algobaba infrastructure
            success = place_exit_order(algobaba_order)
            
            if success:
                order_id = f"exit_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                
                # Update active orders
                for active_order in self.active_orders.values():
                    if (active_order['strategy_id'] == strategy.instance_id and 
                        active_order['status'] == 'ACTIVE'):
                        active_order['status'] = 'CLOSED'
                        active_order['exit_time'] = datetime.now()
                
                return {
                    'success': True,
                    'order_id': order_id,
                    'regime_type': regime_data['regime_type'].value,
                    'action': 'EXIT'
                }
            else:
                return {
                    'success': False,
                    'error': 'Failed to place exit order through Algobaba'
                }
                
        except Exception as e:
            logger.error(f"Error executing exit order: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _prepare_algobaba_order(self, order: Dict[str, Any], strategy: Strategy, 
                              action: str) -> Dict[str, Any]:
        """Prepare order data for Algobaba API"""
        try:
            algobaba_order = {
                'strategy_id': strategy.instance_id,
                'action': action,
                'quantity': order['quantity'],
                'portfolios': self.algobaba_config['portfolio_names'],
                'tags': self.algobaba_config['strategy_tags'],
                'urgency_factor': order.get('urgency_factor', 1.0),
                'regime_metadata': order.get('regime_metadata', {})
            }
            
            # Add order-specific parameters
            if 'stop_loss' in order:
                algobaba_order['stop_loss'] = order['stop_loss']
            
            if 'take_profit' in order:
                algobaba_order['take_profit'] = order['take_profit']
            
            return algobaba_order
            
        except Exception as e:
            logger.error(f"Error preparing Algobaba order: {e}")
            return {}
    
    def _track_order_execution(self, result: Dict[str, Any], regime_data: Dict[str, Any], 
                             order: Dict[str, Any]):
        """Track order execution for performance analysis"""
        try:
            execution_record = {
                'timestamp': datetime.now(),
                'regime_type': regime_data['regime_type'].value,
                'regime_confidence': regime_data['confidence'],
                'order_action': order['action'],
                'quantity': order['quantity'],
                'success': result['success'],
                'adjustments_applied': order.get('regime_metadata', {}).get('adjustments_applied', [])
            }
            
            if not result['success']:
                execution_record['error'] = result.get('error', 'Unknown error')
            
            self.order_history.append(execution_record)
            
            # Update regime performance tracking
            regime_type = regime_data['regime_type'].value
            if regime_type not in self.regime_performance:
                self.regime_performance[regime_type] = {
                    'total_orders': 0,
                    'successful_orders': 0,
                    'total_quantity': 0,
                    'success_rate': 0.0
                }
            
            perf = self.regime_performance[regime_type]
            perf['total_orders'] += 1
            perf['total_quantity'] += order['quantity']
            
            if result['success']:
                perf['successful_orders'] += 1
            
            perf['success_rate'] = perf['successful_orders'] / perf['total_orders']
            
        except Exception as e:
            logger.error(f"Error tracking order execution: {e}")
    
    def get_regime_performance(self) -> Dict[str, Any]:
        """Get regime-based performance statistics"""
        return {
            'regime_performance': self.regime_performance,
            'active_orders_count': len(self.active_orders),
            'total_orders_today': len([
                o for o in self.order_history 
                if o['timestamp'].date() == datetime.now().date()
            ]),
            'overall_success_rate': self._calculate_overall_success_rate()
        }
    
    def _calculate_overall_success_rate(self) -> float:
        """Calculate overall success rate"""
        try:
            if not self.order_history:
                return 0.0
            
            successful_orders = sum(1 for o in self.order_history if o['success'])
            return successful_orders / len(self.order_history)
            
        except Exception as e:
            logger.error(f"Error calculating success rate: {e}")
            return 0.0
    
    def get_active_orders(self) -> Dict[str, Any]:
        """Get currently active orders"""
        return {
            order_id: order for order_id, order in self.active_orders.items()
            if order['status'] == 'ACTIVE'
        }
    
    def update_regime_adjustments(self, regime_type: str, adjustments: Dict[str, Any]):
        """Update regime-specific adjustments"""
        try:
            if regime_type in self.regime_adjustments:
                self.regime_adjustments[regime_type].update(adjustments)
                logger.info(f"Updated adjustments for regime {regime_type}")
            else:
                logger.warning(f"Unknown regime type: {regime_type}")
                
        except Exception as e:
            logger.error(f"Error updating regime adjustments: {e}")
