#!/usr/bin/env python3
"""
Market Regime Detection System - Usage Demonstration

This script demonstrates how to use the Market Regime Detection System
for real-time market analysis and regime classification.

Usage:
    python3 demo_market_regime_usage.py

Features Demonstrated:
- Configuration loading and validation
- Market regime analysis
- Real-time regime detection
- Performance tracking
- Golden file generation
"""

import os
import sys
import pandas as pd
from datetime import datetime, date
import logging

# Add project root to path
sys.path.insert(0, '/srv/samba/shared/bt/backtester_stable/BTRUN')

# Import market regime modules
from backtester_v2.market_regime.processor import RegimeProcessor
from backtester_v2.market_regime.parser import RegimeConfigParser
from backtester_v2.market_regime.models import RegimeConfig

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def demo_configuration_management():
    """Demonstrate configuration management capabilities"""
    print("🔧 DEMO 1: Configuration Management")
    print("=" * 50)
    
    # Create configuration parser
    parser = RegimeConfigParser()
    
    # Generate template
    template_path = '/tmp/demo_regime_config.xlsx'
    parser.create_template(template_path)
    print(f"✅ Configuration template created: {template_path}")
    
    # Load and validate configuration
    config = parser.parse(template_path)
    validation_results = parser.validate_config(config)
    
    print(f"✅ Configuration loaded: {config.strategy_name}")
    print(f"   - Symbol: {config.symbol}")
    print(f"   - Indicators: {len(config.indicators)}")
    print(f"   - Timeframes: {len(config.timeframes)}")
    print(f"   - Validation errors: {len(validation_results['errors'])}")
    print(f"   - Validation warnings: {len(validation_results['warnings'])}")
    
    return config

def demo_regime_analysis(config: RegimeConfig):
    """Demonstrate regime analysis capabilities"""
    print("\n📊 DEMO 2: Market Regime Analysis")
    print("=" * 50)
    
    # Initialize processor
    processor = RegimeProcessor(db_connection=None, regime_config=config)
    
    # Run regime analysis
    start_date = '2025-01-01'
    end_date = '2025-01-02'
    
    print(f"🔍 Analyzing market regime from {start_date} to {end_date}")
    results = processor.process_regime_analysis(start_date, end_date)
    
    # Display results
    if results['processing_summary']['total_classifications'] > 0:
        print(f"✅ Analysis completed successfully!")
        print(f"   - Total classifications: {results['processing_summary']['total_classifications']}")
        print(f"   - Alerts generated: {len(results['alerts'])}")
        print(f"   - Golden file sheets: {len(results['golden_file_data'])}")
        
        # Show current regime
        current_regime = processor.get_current_regime()
        if current_regime:
            print(f"   - Current regime: {current_regime['regime_type']}")
            print(f"   - Confidence: {current_regime['confidence']:.2f}")
            print(f"   - Timestamp: {current_regime['timestamp']}")
    else:
        print("❌ No classifications generated")
    
    return results

def demo_performance_tracking(processor: RegimeProcessor):
    """Demonstrate performance tracking capabilities"""
    print("\n📈 DEMO 3: Performance Tracking")
    print("=" * 50)
    
    # Get performance metrics
    performance_metrics = processor.get_performance_summary()
    
    if performance_metrics:
        print("✅ Performance metrics available:")
        for indicator_id, metrics in performance_metrics.items():
            print(f"   - {indicator_id}:")
            print(f"     • Hit Rate: {metrics['hit_rate']:.3f}")
            print(f"     • Sharpe Ratio: {metrics['sharpe_ratio']:.3f}")
            print(f"     • Performance Score: {metrics['performance_score']:.3f}")
            print(f"     • Current Weight: {metrics['current_weight']:.3f}")
    else:
        print("ℹ️  No performance metrics available yet")

def demo_golden_file_generation(results: dict):
    """Demonstrate golden file generation"""
    print("\n📄 DEMO 4: Golden File Generation")
    print("=" * 50)
    
    golden_data = results.get('golden_file_data', {})
    
    if golden_data:
        # Save golden file
        output_path = '/tmp/demo_market_regime_output.xlsx'
        
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            for sheet_name, df in golden_data.items():
                df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        print(f"✅ Golden file generated: {output_path}")
        print(f"   - Sheets created: {list(golden_data.keys())}")
        
        # Show sheet details
        for sheet_name, df in golden_data.items():
            print(f"   - {sheet_name}: {len(df)} rows, {len(df.columns)} columns")
        
        file_size = os.path.getsize(output_path)
        print(f"   - File size: {file_size:,} bytes")
    else:
        print("❌ No golden file data available")

def demo_real_time_usage():
    """Demonstrate real-time usage patterns"""
    print("\n⚡ DEMO 5: Real-Time Usage Patterns")
    print("=" * 50)
    
    print("📋 Typical usage workflow:")
    print("1. Load configuration from Excel template")
    print("2. Initialize RegimeProcessor with HeavyDB connection")
    print("3. Call process_regime_analysis() for date range")
    print("4. Monitor current_regime for real-time updates")
    print("5. Track performance metrics for optimization")
    print("6. Generate golden files for reporting")
    
    print("\n💡 Integration examples:")
    print("• OI Strategy: Use regime_type to adjust strike shifting sensitivity")
    print("• Risk Management: Increase position sizing in STRONG_BULLISH regimes")
    print("• Alert System: Generate alerts on regime transitions")
    print("• Portfolio Optimization: Adjust strategy weights based on regime")

def main():
    """Main demonstration function"""
    print("🎯 Market Regime Detection System - Usage Demonstration")
    print("=" * 70)
    print("This demo shows how to use the Market Regime Detection System")
    print("for real-time market analysis and regime classification.")
    print("=" * 70)
    
    try:
        # Demo 1: Configuration Management
        config = demo_configuration_management()
        
        # Demo 2: Regime Analysis
        results = demo_regime_analysis(config)
        
        # Demo 3: Performance Tracking
        processor = RegimeProcessor(db_connection=None, regime_config=config)
        processor.process_regime_analysis('2025-01-01', '2025-01-02')  # Initialize
        demo_performance_tracking(processor)
        
        # Demo 4: Golden File Generation
        demo_golden_file_generation(results)
        
        # Demo 5: Real-Time Usage
        demo_real_time_usage()
        
        print("\n🎉 DEMONSTRATION COMPLETED SUCCESSFULLY!")
        print("=" * 70)
        print("The Market Regime Detection System is ready for production use.")
        print("See the generated files in /tmp/ for examples.")
        print("=" * 70)
        
    except Exception as e:
        print(f"\n❌ DEMONSTRATION FAILED: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
