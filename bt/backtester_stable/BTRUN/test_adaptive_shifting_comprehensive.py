#!/usr/bin/env python3
"""
Comprehensive Test Suite for Adaptive Shifting System

This script tests the complete adaptive shifting system with:
- Historical threshold adjustments
- DTE-based sensitivity
- Market regime awareness
- Emergency override conditions
- Golden file format output validation
- Complete test-validate-analyze-fix cycle
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import date, datetime, timedelta
import logging
from typing import Dict, List, Any, Optional
import time
import json

# Add project root to path
sys.path.insert(0, '/srv/samba/shared/bt/backtester_stable/BTRUN')

# Import enhanced OI modules
from backtester_v2.strategies.oi.enhanced_models import (
    EnhancedOIConfig, EnhancedLegConfig, DynamicWeightConfig, 
    FactorConfig, PerformanceMetrics
)
from backtester_v2.strategies.oi.adaptive_shift_manager import (
    AdaptiveShiftManager, ShiftConfig, ShiftSignal, ShiftExecution
)
from backtester_v2.strategies.oi.enhanced_processor import EnhancedOIProcessor
from backtester_v2.strategies.oi.unified_oi_interface import UnifiedOIInterface

# Import HeavyDB connection
try:
    from heavydb import connect
    HEAVYDB_AVAILABLE = True
except ImportError:
    HEAVYDB_AVAILABLE = False

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class AdaptiveShiftingTestSuite:
    """Comprehensive test suite for adaptive shifting system."""
    
    def __init__(self):
        """Initialize the test suite."""
        self.base_dir = '/srv/samba/shared/bt/backtester_stable/BTRUN'
        self.output_dir = os.path.join(self.base_dir, 'output', 'adaptive_shifting_tests')
        self.golden_output_path = os.path.join(self.output_dir, 'Nifty_Golden_Output.xlsx')
        
        # Create output directory
        os.makedirs(self.output_dir, exist_ok=True)
        
        # HeavyDB connection
        self.db_connection = self._get_heavydb_connection()
        
        # Test results tracking
        self.test_results = {}
        self.test_cycle_count = 0
        
    def _get_heavydb_connection(self):
        """Get HeavyDB connection."""
        if not HEAVYDB_AVAILABLE:
            logger.warning("HeavyDB not available")
            return None
            
        try:
            conn = connect(
                host='127.0.0.1',
                port=6274,
                user='admin',
                password='HyperInteractive',
                dbname='heavyai'
            )
            logger.info("Connected to HeavyDB successfully")
            return conn
        except Exception as e:
            logger.error(f"Failed to connect to HeavyDB: {e}")
            return None
    
    def test_shift_manager_initialization(self) -> Dict[str, Any]:
        """Test 1: Shift manager initialization and configuration."""
        logger.info("🔧 Test 1: Shift Manager Initialization")
        
        results = {
            'test_name': 'Shift Manager Initialization',
            'status': 'RUNNING',
            'details': {}
        }
        
        try:
            # Test default configuration
            default_config = ShiftConfig()
            default_manager = AdaptiveShiftManager(default_config)
            
            results['details']['default_config'] = {
                'shift_delay_minutes': default_config.shift_delay_minutes,
                'base_oi_threshold': default_config.base_oi_threshold,
                'base_weight_threshold': default_config.base_weight_threshold,
                'emergency_oi_change': default_config.emergency_oi_change,
                'emergency_vega_change': default_config.emergency_vega_change,
                'emergency_delta_change': default_config.emergency_delta_change
            }
            
            # Test custom configuration
            custom_config = ShiftConfig(
                shift_delay_minutes=5,
                base_oi_threshold=0.15,
                base_weight_threshold=0.10,
                emergency_oi_change=0.40,
                emergency_vega_change=0.25,
                emergency_delta_change=0.25
            )
            custom_manager = AdaptiveShiftManager(custom_config)
            
            results['details']['custom_config'] = {
                'shift_delay_minutes': custom_config.shift_delay_minutes,
                'base_oi_threshold': custom_config.base_oi_threshold,
                'base_weight_threshold': custom_config.base_weight_threshold,
                'emergency_oi_change': custom_config.emergency_oi_change,
                'emergency_vega_change': custom_config.emergency_vega_change,
                'emergency_delta_change': custom_config.emergency_delta_change
            }
            
            # Test threshold calculation for different scenarios
            test_scenarios = [
                {'dte': 10, 'regime': 'normal'},
                {'dte': 5, 'regime': 'trending'},
                {'dte': 1, 'regime': 'high_volatility'},
                {'dte': 0, 'regime': 'sideways'}
            ]
            
            threshold_tests = {}
            for scenario in test_scenarios:
                thresholds = default_manager.get_current_thresholds(
                    scenario['dte'], scenario['regime']
                )
                threshold_tests[f"dte_{scenario['dte']}_{scenario['regime']}"] = thresholds
            
            results['details']['threshold_tests'] = threshold_tests
            results['status'] = 'PASSED'
            
            logger.info("✅ Shift manager initialization test passed")
            
        except Exception as e:
            results['status'] = 'FAILED'
            results['error'] = str(e)
            logger.error(f"❌ Shift manager initialization test failed: {e}")
        
        return results
    
    def test_shift_signal_evaluation(self) -> Dict[str, Any]:
        """Test 2: Shift signal evaluation with various conditions."""
        logger.info("📊 Test 2: Shift Signal Evaluation")
        
        results = {
            'test_name': 'Shift Signal Evaluation',
            'status': 'RUNNING',
            'details': {}
        }
        
        try:
            shift_manager = AdaptiveShiftManager()
            
            # Test scenarios
            test_scenarios = [
                {
                    'name': 'Normal OI Improvement',
                    'current_strike': 23000,
                    'current_oi': 1000000,
                    'new_strike': 23050,
                    'new_oi': 1250000,  # 25% improvement
                    'current_weights': {'oi_factor': 0.3, 'coi_factor': 0.2},
                    'new_weights': {'oi_factor': 0.35, 'coi_factor': 0.25},
                    'dte': 5,
                    'regime': 'normal'
                },
                {
                    'name': 'Emergency OI Change',
                    'current_strike': 23000,
                    'current_oi': 1000000,
                    'new_strike': 23100,
                    'new_oi': 1600000,  # 60% improvement - emergency
                    'current_weights': {'oi_factor': 0.3, 'coi_factor': 0.2},
                    'new_weights': {'oi_factor': 0.32, 'coi_factor': 0.22},
                    'dte': 3,
                    'regime': 'high_volatility'
                },
                {
                    'name': 'Weight Improvement Only',
                    'current_strike': 23000,
                    'current_oi': 1000000,
                    'new_strike': 23050,
                    'new_oi': 1050000,  # 5% improvement - below threshold
                    'current_weights': {'oi_factor': 0.3, 'vega_factor': 0.1},
                    'new_weights': {'oi_factor': 0.35, 'vega_factor': 0.15},  # 20% weight improvement
                    'dte': 2,
                    'regime': 'trending'
                },
                {
                    'name': 'No Shift Warranted',
                    'current_strike': 23000,
                    'current_oi': 1000000,
                    'new_strike': 23050,
                    'new_oi': 1050000,  # 5% improvement - below threshold
                    'current_weights': {'oi_factor': 0.3, 'coi_factor': 0.2},
                    'new_weights': {'oi_factor': 0.31, 'coi_factor': 0.21},  # 5% weight improvement - below threshold
                    'dte': 7,
                    'regime': 'sideways'
                }
            ]
            
            scenario_results = {}
            
            for scenario in test_scenarios:
                shift_signal = shift_manager.evaluate_shift_signal(
                    leg_id='CE_LEG_1',
                    current_strike=scenario['current_strike'],
                    current_oi=scenario['current_oi'],
                    new_strike=scenario['new_strike'],
                    new_oi=scenario['new_oi'],
                    current_weights=scenario['current_weights'],
                    new_weights=scenario['new_weights'],
                    dte=scenario['dte'],
                    market_regime=scenario['regime']
                )
                
                scenario_results[scenario['name']] = {
                    'shift_signal_generated': shift_signal is not None,
                    'shift_signal': {
                        'oi_improvement': shift_signal.oi_improvement if shift_signal else None,
                        'weight_improvement': shift_signal.weight_improvement if shift_signal else None,
                        'shift_reason': shift_signal.shift_reason if shift_signal else None,
                        'emergency_override': shift_signal.emergency_override if shift_signal else None
                    } if shift_signal else None,
                    'thresholds_used': shift_manager.get_current_thresholds(scenario['dte'], scenario['regime'])
                }
                
                logger.info(f"  📋 {scenario['name']}: {'✅ Signal' if shift_signal else '❌ No Signal'}")
            
            results['details']['scenario_results'] = scenario_results
            results['status'] = 'PASSED'
            
            logger.info("✅ Shift signal evaluation test passed")
            
        except Exception as e:
            results['status'] = 'FAILED'
            results['error'] = str(e)
            logger.error(f"❌ Shift signal evaluation test failed: {e}")
        
        return results
    
    def test_shift_delay_logic(self) -> Dict[str, Any]:
        """Test 3: Shift delay logic and execution timing."""
        logger.info("⏰ Test 3: Shift Delay Logic")
        
        results = {
            'test_name': 'Shift Delay Logic',
            'status': 'RUNNING',
            'details': {}
        }
        
        try:
            # Test with 1-minute delay for faster testing
            config = ShiftConfig(shift_delay_minutes=0.1)  # 6 seconds
            shift_manager = AdaptiveShiftManager(config)
            
            # Create a shift signal
            shift_signal = ShiftSignal(
                timestamp=datetime.now(),
                current_strike=23000,
                new_strike=23050,
                current_oi=1000000,
                new_oi=1250000,
                oi_improvement=0.25,
                weight_improvement=0.15,
                shift_reason="OI_AND_WEIGHT_IMPROVEMENT",
                emergency_override=False,
                dte=5,
                market_regime="normal"
            )
            
            # Test delay logic
            leg_id = 'CE_LEG_1'
            
            # First call - should not execute (delay not passed)
            should_execute_1 = shift_manager.should_execute_shift(leg_id, shift_signal)
            
            # Wait for delay period
            time.sleep(0.2)  # 200ms > 100ms delay
            
            # Second call - should execute (delay passed)
            should_execute_2 = shift_manager.should_execute_shift(leg_id, shift_signal)
            
            # Test emergency override (should execute immediately)
            emergency_signal = ShiftSignal(
                timestamp=datetime.now(),
                current_strike=23050,
                new_strike=23100,
                current_oi=1000000,
                new_oi=1600000,
                oi_improvement=0.60,
                weight_improvement=0.10,
                shift_reason="EMERGENCY_OVERRIDE",
                emergency_override=True,
                dte=3,
                market_regime="high_volatility"
            )
            
            should_execute_emergency = shift_manager.should_execute_shift('PE_LEG_1', emergency_signal)
            
            results['details']['delay_tests'] = {
                'first_call_should_not_execute': not should_execute_1,
                'second_call_should_execute': should_execute_2,
                'emergency_should_execute_immediately': should_execute_emergency,
                'delay_period_seconds': config.shift_delay_minutes * 60
            }
            
            results['status'] = 'PASSED'
            logger.info("✅ Shift delay logic test passed")
            
        except Exception as e:
            results['status'] = 'FAILED'
            results['error'] = str(e)
            logger.error(f"❌ Shift delay logic test failed: {e}")
        
        return results

    def test_golden_file_output_generation(self) -> Dict[str, Any]:
        """Test 7: Golden file format output generation."""
        logger.info("📄 Test 7: Golden File Output Generation")

        results = {
            'test_name': 'Golden File Output Generation',
            'status': 'RUNNING',
            'details': {}
        }

        try:
            if not self.db_connection:
                results['status'] = 'SKIPPED'
                results['reason'] = 'HeavyDB connection not available'
                return results

            # Create enhanced configuration
            enhanced_config = EnhancedOIConfig(
                strategy_name='Adaptive_Shift_Test',
                index='NIFTY',
                oi_method='MAXOI_1',
                timeframe=3,
                start_time='091600',
                end_time='152000',
                enable_dynamic_weights=True
            )

            # Create leg configurations
            leg_configs = [
                EnhancedLegConfig(
                    strategy_name='Adaptive_Shift_Test',
                    leg_id='CE_LEG_1',
                    instrument='CE',
                    transaction='SELL',
                    strike_method='MAXOI_1',
                    lots=1
                ),
                EnhancedLegConfig(
                    strategy_name='Adaptive_Shift_Test',
                    leg_id='PE_LEG_1',
                    instrument='PE',
                    transaction='SELL',
                    strike_method='MAXOI_1',
                    lots=1
                )
            ]

            # Create weight and factor configurations
            weight_config = DynamicWeightConfig()
            factor_configs = [
                FactorConfig(factor_name='oi_factor', factor_type='OI', base_weight=0.30),
                FactorConfig(factor_name='coi_factor', factor_type='COI', base_weight=0.25),
                FactorConfig(factor_name='delta_factor', factor_type='GREEK', base_weight=0.20),
                FactorConfig(factor_name='vega_factor', factor_type='GREEK', base_weight=0.15),
                FactorConfig(factor_name='volume_factor', factor_type='MARKET', base_weight=0.10)
            ]

            # Create shift configuration
            shift_config = ShiftConfig(
                shift_delay_minutes=3,
                base_oi_threshold=0.20,
                base_weight_threshold=0.15
            )

            # Initialize enhanced processor
            processor = EnhancedOIProcessor(
                self.db_connection,
                enhanced_config,
                leg_configs,
                weight_config,
                factor_configs,
                shift_config
            )

            # Process strategy for a single day
            start_date = date(2025, 1, 2)
            end_date = date(2025, 1, 2)

            strategy_results = processor.process_enhanced_strategy(start_date, end_date)

            # Generate golden file output
            golden_data = strategy_results.get('golden_file_data', {})

            # Validate golden file structure
            expected_sheets = ['PortfolioParameter', 'GeneralParameter', 'LegParameter', 'Metrics']
            sheets_present = all(sheet in golden_data for sheet in expected_sheets)

            # Save to golden output path
            if golden_data and sheets_present:
                with pd.ExcelWriter(self.golden_output_path, engine='openpyxl') as writer:
                    for sheet_name, df in golden_data.items():
                        df.to_excel(writer, sheet_name=sheet_name, index=False)

                golden_file_created = os.path.exists(self.golden_output_path)
                file_size = os.path.getsize(self.golden_output_path) if golden_file_created else 0
            else:
                golden_file_created = False
                file_size = 0

            results['details']['golden_file_validation'] = {
                'expected_sheets': expected_sheets,
                'sheets_present': list(golden_data.keys()),
                'all_sheets_present': sheets_present,
                'golden_file_created': golden_file_created,
                'golden_file_path': self.golden_output_path,
                'file_size_bytes': file_size,
                'strategy_results_summary': {
                    'total_trades': len(strategy_results.get('trades', [])),
                    'processing_summary': strategy_results.get('processing_summary', {}),
                    'shift_manager_enabled': hasattr(processor, 'shift_manager')
                }
            }

            results['status'] = 'PASSED' if (sheets_present and golden_file_created) else 'FAILED'

            if results['status'] == 'PASSED':
                logger.info(f"✅ Golden file output generated: {self.golden_output_path}")
            else:
                logger.error("❌ Golden file output generation failed")

        except Exception as e:
            results['status'] = 'FAILED'
            results['error'] = str(e)
            logger.error(f"❌ Golden file output generation test failed: {e}")

        return results

    def run_test_cycle(self) -> Dict[str, Any]:
        """Run complete test cycle: Test → Validate → Analyze → Fix → Retest."""
        self.test_cycle_count += 1
        logger.info(f"🔄 Starting Test Cycle #{self.test_cycle_count}")
        logger.info("=" * 80)

        start_time = time.time()

        # Define test methods
        test_methods = [
            self.test_shift_manager_initialization,
            self.test_shift_signal_evaluation,
            self.test_shift_delay_logic,
            self.test_golden_file_output_generation
        ]

        cycle_results = {
            'cycle_number': self.test_cycle_count,
            'start_time': datetime.now().isoformat(),
            'tests': {},
            'summary': {}
        }

        passed_tests = 0
        failed_tests = 0
        skipped_tests = 0

        # Execute tests
        for test_method in test_methods:
            try:
                test_result = test_method()
                test_name = test_result['test_name']
                cycle_results['tests'][test_name] = test_result

                if test_result['status'] == 'PASSED':
                    passed_tests += 1
                    logger.info(f"✅ {test_name}: PASSED")
                elif test_result['status'] == 'FAILED':
                    failed_tests += 1
                    logger.error(f"❌ {test_name}: FAILED - {test_result.get('error', 'Unknown error')}")
                elif test_result['status'] == 'SKIPPED':
                    skipped_tests += 1
                    logger.warning(f"⏭️  {test_name}: SKIPPED - {test_result.get('reason', 'Unknown reason')}")

            except Exception as e:
                failed_tests += 1
                logger.error(f"❌ {test_method.__name__}: EXCEPTION - {e}")
                cycle_results['tests'][test_method.__name__] = {
                    'test_name': test_method.__name__,
                    'status': 'EXCEPTION',
                    'error': str(e)
                }

        end_time = time.time()
        execution_time = end_time - start_time

        # Calculate summary
        total_tests = len(test_methods)
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0

        cycle_results['summary'] = {
            'total_tests': total_tests,
            'passed': passed_tests,
            'failed': failed_tests,
            'skipped': skipped_tests,
            'execution_time_seconds': execution_time,
            'success_rate': success_rate,
            'cycle_complete': failed_tests == 0
        }

        cycle_results['end_time'] = datetime.now().isoformat()

        # Save cycle results
        cycle_file = os.path.join(self.output_dir, f'test_cycle_{self.test_cycle_count}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
        with open(cycle_file, 'w') as f:
            json.dump(cycle_results, f, indent=2, default=str)

        # Print cycle summary
        logger.info("=" * 80)
        logger.info(f"🏁 TEST CYCLE #{self.test_cycle_count} SUMMARY")
        logger.info("=" * 80)
        logger.info(f"📊 Total Tests: {total_tests}")
        logger.info(f"✅ Passed: {passed_tests}")
        logger.info(f"❌ Failed: {failed_tests}")
        logger.info(f"⏭️  Skipped: {skipped_tests}")
        logger.info(f"⏱️  Execution Time: {execution_time:.2f} seconds")
        logger.info(f"📈 Success Rate: {success_rate:.1f}%")
        logger.info(f"🔄 Cycle Complete: {'✅ YES' if cycle_results['summary']['cycle_complete'] else '❌ NO'}")
        logger.info(f"📁 Results saved to: {cycle_file}")
        logger.info("=" * 80)

        return cycle_results

    def run_complete_testing_cycle(self) -> Dict[str, Any]:
        """Run complete testing cycle until all tests pass or max cycles reached."""
        logger.info("🚀 Starting Complete Adaptive Shifting Testing Cycle")
        logger.info("Testing: Shifting Logic + Historical Adjustments + DTE Sensitivity + Market Regime + Golden Output")
        logger.info("=" * 100)

        max_cycles = 5
        all_cycles = []

        for cycle in range(1, max_cycles + 1):
            cycle_result = self.run_test_cycle()
            all_cycles.append(cycle_result)

            if cycle_result['summary']['cycle_complete']:
                logger.info(f"🎉 ALL TESTS PASSED in cycle {cycle}!")
                break
            elif cycle < max_cycles:
                logger.info(f"🔄 Cycle {cycle} incomplete. Running next cycle...")
                time.sleep(1)  # Brief pause between cycles
            else:
                logger.warning(f"⚠️  Maximum cycles ({max_cycles}) reached. Some tests may still be failing.")

        # Final summary
        final_summary = {
            'test_suite': 'Adaptive Shifting Comprehensive Tests',
            'total_cycles': len(all_cycles),
            'max_cycles': max_cycles,
            'final_success': all_cycles[-1]['summary']['cycle_complete'] if all_cycles else False,
            'cycles': all_cycles,
            'golden_file_generated': os.path.exists(self.golden_output_path),
            'golden_file_path': self.golden_output_path
        }

        # Save final summary
        final_file = os.path.join(self.output_dir, f'adaptive_shifting_final_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
        with open(final_file, 'w') as f:
            json.dump(final_summary, f, indent=2, default=str)

        logger.info("🏆 FINAL ADAPTIVE SHIFTING TEST SUMMARY")
        logger.info("=" * 100)
        logger.info(f"📊 Total Cycles Run: {final_summary['total_cycles']}")
        logger.info(f"🎯 Final Success: {'✅ YES' if final_summary['final_success'] else '❌ NO'}")
        logger.info(f"📄 Golden File Generated: {'✅ YES' if final_summary['golden_file_generated'] else '❌ NO'}")
        logger.info(f"📁 Golden File Path: {final_summary['golden_file_path']}")
        logger.info(f"📁 Final Results: {final_file}")
        logger.info("=" * 100)

        return final_summary


if __name__ == "__main__":
    """Run the complete adaptive shifting test suite."""
    print("🎯 Adaptive Shifting Comprehensive Test Suite")
    print("Testing: Shifting + Historical + DTE + Regime + Golden Output")
    print("=" * 80)

    # Initialize test suite
    test_suite = AdaptiveShiftingTestSuite()

    # Run complete testing cycle
    results = test_suite.run_complete_testing_cycle()

    # Exit with appropriate code
    if results['final_success']:
        print("✅ All adaptive shifting tests completed successfully!")
        sys.exit(0)
    else:
        print("❌ Some adaptive shifting tests failed!")
        sys.exit(1)
