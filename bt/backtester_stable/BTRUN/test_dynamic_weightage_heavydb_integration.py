#!/usr/bin/env python3
"""
Dynamic Weightage HeavyDB Integration Test

This script tests dynamic weightage with real HeavyDB data using proper SQL queries
and validates all six factors: MAXOI, COI, Delta, Volume, Vega, Theta
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import date, datetime, timedelta
import logging
from typing import Dict, List, Any, Optional
import time
import json

# Add project root to path
sys.path.insert(0, '/srv/samba/shared/bt/backtester_stable/BTRUN')

# Import enhanced OI modules
from backtester_v2.strategies.oi.enhanced_models import (
    EnhancedOIConfig, EnhancedLegConfig, DynamicWeightConfig, 
    FactorConfig, PerformanceMetrics
)
from backtester_v2.strategies.oi.dynamic_weight_engine import DynamicWeightEngine
from backtester_v2.strategies.oi.unified_oi_interface import UnifiedOIInterface

# Import HeavyDB connection
try:
    from heavydb import connect
    HEAVYDB_AVAILABLE = True
except ImportError:
    HEAVYDB_AVAILABLE = False
    print("Warning: HeavyDB not available.")

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DynamicWeightageHeavyDBTest:
    """Test dynamic weightage with real HeavyDB integration."""
    
    def __init__(self):
        """Initialize the HeavyDB integration test."""
        self.base_dir = '/srv/samba/shared/bt/backtester_stable/BTRUN'
        self.output_dir = os.path.join(self.base_dir, 'output', 'heavydb_dynamic_tests')
        
        # Create output directory
        os.makedirs(self.output_dir, exist_ok=True)
        
        # HeavyDB connection
        self.db_connection = self._get_heavydb_connection()
        
    def _get_heavydb_connection(self):
        """Get HeavyDB connection."""
        if not HEAVYDB_AVAILABLE:
            logger.warning("HeavyDB not available")
            return None
            
        try:
            conn = connect(
                host='127.0.0.1',
                port=6274,
                user='admin',
                password='HyperInteractive',
                dbname='heavyai'
            )
            logger.info("Connected to HeavyDB successfully")
            return conn
        except Exception as e:
            logger.error(f"Failed to connect to HeavyDB: {e}")
            return None
    
    def get_real_factor_data(self, trade_date: str = '2025-01-02') -> Dict[str, Any]:
        """Get real factor data from HeavyDB with proper SQL queries."""
        if not self.db_connection:
            return {'error': 'No HeavyDB connection'}
        
        try:
            # Get basic market data with proper date/time handling
            # Use recent available data if specified date doesn't exist
            base_query = f"""
            SELECT
                trade_date,
                trade_time,
                strike,
                ce_oi,
                pe_oi,
                COALESCE(ce_delta, 0) as ce_delta,
                COALESCE(pe_delta, 0) as pe_delta,
                COALESCE(ce_vega, 0) as ce_vega,
                COALESCE(pe_vega, 0) as pe_vega,
                COALESCE(ce_theta, 0) as ce_theta,
                COALESCE(pe_theta, 0) as pe_theta,
                COALESCE(ce_volume, 0) as ce_volume,
                COALESCE(pe_volume, 0) as pe_volume,
                ce_close,
                pe_close
            FROM nifty_option_chain
            WHERE trade_date >= '2024-01-01'
            AND trade_time >= '09:15:00'
            AND trade_time <= '15:20:00'
            AND ce_oi > 50000
            AND pe_oi > 50000
            ORDER BY trade_date DESC, trade_time, strike
            LIMIT 1000
            """
            
            market_data = pd.read_sql(base_query, self.db_connection)
            
            if market_data.empty:
                logger.warning(f"No market data found for {trade_date}")
                return {'error': f'No data for {trade_date}'}
            
            logger.info(f"Retrieved {len(market_data)} market data points")
            
            # Calculate factor values
            factor_data = {}
            
            # MAXOI Factor - Total OI
            market_data['total_oi'] = market_data['ce_oi'] + market_data['pe_oi']
            factor_data['MAXOI'] = {
                'values': market_data['total_oi'].tolist(),
                'mean': float(market_data['total_oi'].mean()),
                'std': float(market_data['total_oi'].std()),
                'max': float(market_data['total_oi'].max()),
                'min': float(market_data['total_oi'].min())
            }
            
            # COI Factor - Change in OI (simplified as OI difference between CE and PE)
            market_data['coi'] = market_data['ce_oi'] - market_data['pe_oi']
            factor_data['COI'] = {
                'values': market_data['coi'].tolist(),
                'mean': float(market_data['coi'].mean()),
                'std': float(market_data['coi'].std()),
                'max': float(market_data['coi'].max()),
                'min': float(market_data['coi'].min())
            }
            
            # DELTA Factor - Total delta exposure
            market_data['total_delta'] = abs(market_data['ce_delta']) + abs(market_data['pe_delta'])
            factor_data['DELTA'] = {
                'values': market_data['total_delta'].tolist(),
                'mean': float(market_data['total_delta'].mean()),
                'std': float(market_data['total_delta'].std()),
                'max': float(market_data['total_delta'].max()),
                'min': float(market_data['total_delta'].min())
            }
            
            # VOLUME Factor - Total volume
            market_data['total_volume'] = market_data['ce_volume'] + market_data['pe_volume']
            factor_data['VOLUME'] = {
                'values': market_data['total_volume'].tolist(),
                'mean': float(market_data['total_volume'].mean()),
                'std': float(market_data['total_volume'].std()),
                'max': float(market_data['total_volume'].max()),
                'min': float(market_data['total_volume'].min())
            }
            
            # VEGA Factor - Total vega exposure
            market_data['total_vega'] = market_data['ce_vega'] + market_data['pe_vega']
            factor_data['VEGA'] = {
                'values': market_data['total_vega'].tolist(),
                'mean': float(market_data['total_vega'].mean()),
                'std': float(market_data['total_vega'].std()),
                'max': float(market_data['total_vega'].max()),
                'min': float(market_data['total_vega'].min())
            }
            
            # THETA Factor - Total theta exposure
            market_data['total_theta'] = abs(market_data['ce_theta']) + abs(market_data['pe_theta'])
            factor_data['THETA'] = {
                'values': market_data['total_theta'].tolist(),
                'mean': float(market_data['total_theta'].mean()),
                'std': float(market_data['total_theta'].std()),
                'max': float(market_data['total_theta'].max()),
                'min': float(market_data['total_theta'].min())
            }
            
            return {
                'success': True,
                'trade_date': trade_date,
                'data_points': len(market_data),
                'factors': factor_data,
                'raw_data_sample': market_data.head(10).to_dict('records')
            }
            
        except Exception as e:
            logger.error(f"Error getting real factor data: {e}")
            return {'error': str(e)}
    
    def test_dynamic_weightage_with_real_data(self) -> Dict[str, Any]:
        """Test dynamic weightage with real HeavyDB data."""
        logger.info("🔥 Testing dynamic weightage with real HeavyDB data...")
        
        results = {
            'test_name': 'Dynamic Weightage with Real HeavyDB Data',
            'status': 'RUNNING',
            'details': {}
        }
        
        try:
            # Get real factor data
            factor_data_result = self.get_real_factor_data()
            
            if 'error' in factor_data_result:
                results['status'] = 'FAILED'
                results['error'] = factor_data_result['error']
                return results
            
            results['details']['data_source'] = factor_data_result
            
            # Create factor configurations based on real data
            factor_configs = [
                FactorConfig(factor_name='maxoi_factor', factor_type='OI', base_weight=0.25),
                FactorConfig(factor_name='coi_factor', factor_type='COI', base_weight=0.20),
                FactorConfig(factor_name='delta_factor', factor_type='GREEK', base_weight=0.18),
                FactorConfig(factor_name='volume_factor', factor_type='MARKET', base_weight=0.15),
                FactorConfig(factor_name='vega_factor', factor_type='GREEK', base_weight=0.12),
                FactorConfig(factor_name='theta_factor', factor_type='GREEK', base_weight=0.10)
            ]
            
            # Create dynamic weight configuration
            weight_config = DynamicWeightConfig(
                oi_factor_weight=0.45,  # MAXOI + COI
                greek_factor_weight=0.40,  # DELTA + VEGA + THETA
                market_factor_weight=0.15,  # VOLUME
                weight_learning_rate=0.02,
                performance_threshold=0.65
            )
            
            # Initialize weight engine
            engine = DynamicWeightEngine(weight_config, factor_configs)
            
            # Calculate performance based on real data quality and characteristics
            performance_data = {}
            factor_data = factor_data_result['factors']
            
            for factor_name in ['MAXOI', 'COI', 'DELTA', 'VOLUME', 'VEGA', 'THETA']:
                if factor_name in factor_data:
                    factor_info = factor_data[factor_name]
                    
                    # Calculate performance score based on data characteristics
                    # Higher std/mean ratio indicates more dynamic behavior (better for trading)
                    variability = factor_info['std'] / factor_info['mean'] if factor_info['mean'] != 0 else 0
                    
                    # Normalize variability to 0-1 range and use as performance indicator
                    performance_score = min(1.0, max(0.0, variability * 2))  # Scale factor of 2
                    
                    performance_data[f'{factor_name.lower()}_factor'] = performance_score
                    
                    logger.info(f"📊 {factor_name}: Performance={performance_score:.3f}, "
                              f"Mean={factor_info['mean']:.2f}, Std={factor_info['std']:.2f}")
            
            # Test weight updates with real data-driven performance
            market_conditions = {
                'volatility': 0.6,
                'trend_strength': 0.4,
                'liquidity': 0.8,
                'regime': 'normal'
            }
            
            initial_weights = engine.get_current_weights()
            updated_weights = engine.update_weights(performance_data, market_conditions)
            
            # Calculate weight changes
            weight_changes = {}
            for factor in initial_weights:
                change = updated_weights.get(factor, 0) - initial_weights.get(factor, 0)
                weight_changes[factor] = change
                if abs(change) > 0.001:
                    logger.info(f"  🔄 {factor}: {change:+.4f} ({updated_weights.get(factor, 0):.3f})")
            
            results['details']['performance_analysis'] = {
                'performance_data': performance_data,
                'initial_weights': initial_weights,
                'updated_weights': updated_weights,
                'weight_changes': weight_changes,
                'total_weight_sum': sum(updated_weights.values()),
                'market_conditions': market_conditions
            }
            
            # Validate weight constraints
            weight_sum = sum(updated_weights.values())
            if abs(weight_sum - 1.0) < 0.1:  # Allow some tolerance for multiple factor groups
                results['details']['weight_validation'] = 'PASSED'
            else:
                results['details']['weight_validation'] = f'WARNING: Weight sum = {weight_sum:.3f}'
            
            results['status'] = 'PASSED'
            logger.info("✅ Dynamic weightage with real HeavyDB data test passed")
            
        except Exception as e:
            results['status'] = 'FAILED'
            results['error'] = str(e)
            logger.error(f"Dynamic weightage with real data test failed: {e}")
        
        return results
    
    def run_heavydb_integration_tests(self) -> Dict[str, Any]:
        """Run HeavyDB integration tests."""
        logger.info("🚀 Starting HeavyDB Dynamic Weightage Integration Tests...")
        logger.info("=" * 80)
        
        start_time = time.time()
        
        all_results = {
            'test_suite': 'HeavyDB Dynamic Weightage Integration',
            'start_time': datetime.now().isoformat(),
            'tests': {},
            'summary': {}
        }
        
        # Run the main test
        test_result = self.test_dynamic_weightage_with_real_data()
        all_results['tests'][test_result['test_name']] = test_result
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Summary
        passed = 1 if test_result['status'] == 'PASSED' else 0
        failed = 1 if test_result['status'] == 'FAILED' else 0
        
        all_results['summary'] = {
            'total_tests': 1,
            'passed': passed,
            'failed': failed,
            'execution_time_seconds': execution_time,
            'success_rate': passed * 100,
            'heavydb_available': self.db_connection is not None
        }
        
        all_results['end_time'] = datetime.now().isoformat()
        
        # Save results
        results_file = os.path.join(self.output_dir, f'heavydb_integration_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
        with open(results_file, 'w') as f:
            json.dump(all_results, f, indent=2, default=str)
        
        # Print summary
        logger.info("=" * 80)
        logger.info("🏁 HEAVYDB DYNAMIC WEIGHTAGE INTEGRATION SUMMARY")
        logger.info("=" * 80)
        logger.info(f"🔗 HeavyDB Connection: {'✅ Available' if self.db_connection else '❌ Not Available'}")
        logger.info(f"📈 Total Tests: {all_results['summary']['total_tests']}")
        logger.info(f"✅ Passed: {all_results['summary']['passed']}")
        logger.info(f"❌ Failed: {all_results['summary']['failed']}")
        logger.info(f"⏱️  Execution Time: {execution_time:.2f} seconds")
        logger.info(f"📊 Success Rate: {all_results['summary']['success_rate']:.1f}%")
        logger.info(f"📁 Results saved to: {results_file}")
        logger.info("=" * 80)
        
        return all_results


if __name__ == "__main__":
    """Run the HeavyDB integration tests."""
    print("🔥 HeavyDB Dynamic Weightage Integration Test")
    print("Testing: MAXOI, COI, Delta, Volume, Vega, Theta with Real Data")
    print("=" * 70)
    
    # Initialize test suite
    test_suite = DynamicWeightageHeavyDBTest()
    
    # Run tests
    results = test_suite.run_heavydb_integration_tests()
    
    # Exit with appropriate code
    if results['summary']['failed'] > 0:
        print("❌ Integration test failed!")
        sys.exit(1)
    else:
        print("✅ HeavyDB integration test completed successfully!")
        sys.exit(0)
