#!/usr/bin/env python3
"""
Final Validation Test for Complete Adaptive Shifting System

This script validates the complete implementation including:
- Adaptive shifting logic with configurable delays
- Historical performance-based threshold adjustments  
- DTE-based sensitivity adjustments
- Market regime awareness
- Emergency override conditions
- Golden file format output
- Integration with dynamic weightage system
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import date, datetime, timedelta
import logging
import time
import json

# Add project root to path
sys.path.insert(0, '/srv/samba/shared/bt/backtester_stable/BTRUN')

# Import all components
from backtester_v2.strategies.oi.enhanced_models import (
    EnhancedOIConfig, EnhancedLegConfig, DynamicWeightConfig, FactorConfig
)
from backtester_v2.strategies.oi.adaptive_shift_manager import (
    AdaptiveShiftManager, ShiftConfig, ShiftSignal
)
from backtester_v2.strategies.oi.enhanced_processor import EnhancedOIProcessor
from backtester_v2.strategies.oi.dynamic_weight_engine import DynamicWeightEngine

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_complete_adaptive_shifting_system():
    """Test the complete adaptive shifting system with all features."""
    logger.info("🚀 Starting Final Validation of Complete Adaptive Shifting System")
    logger.info("=" * 80)
    
    results = {
        'test_suite': 'Complete Adaptive Shifting Final Validation',
        'start_time': datetime.now().isoformat(),
        'tests': {},
        'summary': {}
    }
    
    # Test 1: Adaptive Shift Manager with Historical Learning
    logger.info("📊 Test 1: Adaptive Shift Manager with Historical Learning")
    
    try:
        # Create shift configuration with testing parameters
        shift_config = ShiftConfig(
            shift_delay_minutes=3,
            base_oi_threshold=0.20,
            base_weight_threshold=0.15,
            emergency_oi_change=0.50,
            emergency_vega_change=0.30,
            emergency_delta_change=0.30,
            performance_window=10,
            enable_regime_sensitivity=True
        )
        
        shift_manager = AdaptiveShiftManager(shift_config)
        
        # Simulate historical performance data
        logger.info("  📈 Simulating historical performance data...")
        
        # Good performance period
        for i in range(5):
            shift_manager.performance_tracker.add_trade_result(
                trade_pnl=300 + i * 50,
                shift_count=1,
                dte=5,
                regime='normal'
            )
        
        # Poor performance period  
        for i in range(5):
            shift_manager.performance_tracker.add_trade_result(
                trade_pnl=-150 - i * 25,
                shift_count=3,
                dte=5,
                regime='normal'
            )
        
        # Test threshold adjustments for different scenarios
        test_scenarios = [
            {'dte': 10, 'regime': 'normal', 'expected': 'conservative'},
            {'dte': 5, 'regime': 'normal', 'expected': 'baseline'},
            {'dte': 1, 'regime': 'normal', 'expected': 'aggressive'},
            {'dte': 5, 'regime': 'trending', 'expected': 'more_aggressive'},
            {'dte': 5, 'regime': 'sideways', 'expected': 'more_conservative'},
            {'dte': 5, 'regime': 'high_volatility', 'expected': 'emergency_sensitive'}
        ]
        
        threshold_results = {}
        base_thresholds = shift_manager.get_current_thresholds(5, 'normal')
        
        for scenario in test_scenarios:
            thresholds = shift_manager.get_current_thresholds(scenario['dte'], scenario['regime'])
            threshold_results[f"{scenario['dte']}_{scenario['regime']}"] = {
                'thresholds': thresholds,
                'oi_ratio': thresholds['oi_threshold'] / base_thresholds['oi_threshold'],
                'weight_ratio': thresholds['weight_threshold'] / base_thresholds['weight_threshold'],
                'expected_behavior': scenario['expected']
            }
        
        results['tests']['adaptive_shift_manager'] = {
            'status': 'PASSED',
            'threshold_results': threshold_results,
            'performance_tracking': {
                'recent_performance': shift_manager.performance_tracker.get_recent_performance(),
                'trade_history_count': len(shift_manager.performance_tracker.trade_history)
            }
        }
        
        logger.info("  ✅ Adaptive shift manager test passed")
        
    except Exception as e:
        results['tests']['adaptive_shift_manager'] = {
            'status': 'FAILED',
            'error': str(e)
        }
        logger.error(f"  ❌ Adaptive shift manager test failed: {e}")
    
    # Test 2: Shift Signal Evaluation with Real Scenarios
    logger.info("📡 Test 2: Shift Signal Evaluation with Real Scenarios")
    
    try:
        # Test various shift scenarios
        shift_scenarios = [
            {
                'name': 'Normal_OI_Improvement',
                'current_oi': 1000000,
                'new_oi': 1250000,  # 25% improvement
                'current_weights': {'oi_factor': 0.3, 'coi_factor': 0.2},
                'new_weights': {'oi_factor': 0.35, 'coi_factor': 0.25},
                'dte': 5,
                'regime': 'normal',
                'expected_signal': True
            },
            {
                'name': 'Emergency_Override',
                'current_oi': 1000000,
                'new_oi': 1600000,  # 60% improvement - emergency
                'current_weights': {'oi_factor': 0.3, 'vega_factor': 0.1},
                'new_weights': {'oi_factor': 0.32, 'vega_factor': 0.14},  # 40% vega improvement
                'dte': 3,
                'regime': 'high_volatility',
                'expected_signal': True
            },
            {
                'name': 'Insufficient_Improvement',
                'current_oi': 1000000,
                'new_oi': 1050000,  # 5% improvement - below threshold
                'current_weights': {'oi_factor': 0.3, 'coi_factor': 0.2},
                'new_weights': {'oi_factor': 0.31, 'coi_factor': 0.21},  # 5% weight improvement
                'dte': 7,
                'regime': 'sideways',
                'expected_signal': False
            }
        ]
        
        signal_results = {}
        
        for scenario in shift_scenarios:
            shift_signal = shift_manager.evaluate_shift_signal(
                leg_id='TEST_LEG',
                current_strike=23000,
                current_oi=scenario['current_oi'],
                new_strike=23050,
                new_oi=scenario['new_oi'],
                current_weights=scenario['current_weights'],
                new_weights=scenario['new_weights'],
                dte=scenario['dte'],
                market_regime=scenario['regime']
            )
            
            signal_generated = shift_signal is not None
            signal_results[scenario['name']] = {
                'signal_generated': signal_generated,
                'expected_signal': scenario['expected_signal'],
                'test_passed': signal_generated == scenario['expected_signal'],
                'signal_details': {
                    'oi_improvement': shift_signal.oi_improvement if shift_signal else None,
                    'weight_improvement': shift_signal.weight_improvement if shift_signal else None,
                    'shift_reason': shift_signal.shift_reason if shift_signal else None,
                    'emergency_override': shift_signal.emergency_override if shift_signal else None
                } if shift_signal else None
            }
        
        all_signals_correct = all(result['test_passed'] for result in signal_results.values())
        
        results['tests']['shift_signal_evaluation'] = {
            'status': 'PASSED' if all_signals_correct else 'FAILED',
            'signal_results': signal_results,
            'all_signals_correct': all_signals_correct
        }
        
        if all_signals_correct:
            logger.info("  ✅ Shift signal evaluation test passed")
        else:
            logger.error("  ❌ Some shift signals incorrect")
        
    except Exception as e:
        results['tests']['shift_signal_evaluation'] = {
            'status': 'FAILED',
            'error': str(e)
        }
        logger.error(f"  ❌ Shift signal evaluation test failed: {e}")
    
    # Test 3: Integration with Dynamic Weightage System
    logger.info("⚖️ Test 3: Integration with Dynamic Weightage System")
    
    try:
        # Create comprehensive system configuration
        enhanced_config = EnhancedOIConfig(
            strategy_name='Final_Validation_Test',
            index='NIFTY',
            oi_method='MAXOI_1',
            timeframe=3,
            enable_dynamic_weights=True
        )
        
        leg_configs = [
            EnhancedLegConfig(
                strategy_name='Final_Validation_Test',
                leg_id='CE_LEG_1',
                instrument='CE',
                transaction='SELL',
                strike_method='MAXOI_1',
                lots=1
            )
        ]
        
        weight_config = DynamicWeightConfig()
        factor_configs = [
            FactorConfig(factor_name='oi_factor', factor_type='OI', base_weight=0.30),
            FactorConfig(factor_name='coi_factor', factor_type='COI', base_weight=0.25),
            FactorConfig(factor_name='delta_factor', factor_type='GREEK', base_weight=0.20),
            FactorConfig(factor_name='vega_factor', factor_type='GREEK', base_weight=0.15),
            FactorConfig(factor_name='volume_factor', factor_type='MARKET', base_weight=0.10)
        ]
        
        # Test integration
        weight_engine = DynamicWeightEngine(weight_config, factor_configs)
        initial_weights = weight_engine.get_current_weights()
        
        # Simulate weight updates
        performance_data = {
            'oi_factor': 0.75,
            'coi_factor': 0.65,
            'delta_factor': 0.55,
            'vega_factor': 0.45,
            'volume_factor': 0.60
        }
        
        market_conditions = {
            'volatility': 0.6,
            'trend_strength': 0.4,
            'liquidity': 0.8,
            'regime': 'normal'
        }
        
        updated_weights = weight_engine.update_weights(performance_data, market_conditions)
        
        # Test shift evaluation with updated weights
        shift_signal = shift_manager.evaluate_shift_signal(
            leg_id='CE_LEG_1',
            current_strike=23000,
            current_oi=1000000,
            new_strike=23050,
            new_oi=1300000,
            current_weights=initial_weights,
            new_weights=updated_weights,
            dte=5,
            market_regime='normal'
        )
        
        # Check if main factors sum to approximately 1.0
        main_factors = ['oi_factor', 'coi_factor', 'delta_factor', 'vega_factor', 'volume_factor']
        main_weight_sum = sum(updated_weights.get(factor, 0) for factor in main_factors if factor in updated_weights)

        integration_successful = (
            len(initial_weights) > 0 and
            len(updated_weights) > 0 and
            abs(main_weight_sum - 1.0) < 0.1 and  # Main factors should sum to ~1.0
            shift_signal is not None
        )
        
        results['tests']['dynamic_weightage_integration'] = {
            'status': 'PASSED' if integration_successful else 'FAILED',
            'initial_weights': initial_weights,
            'updated_weights': updated_weights,
            'total_weight_sum': sum(updated_weights.values()),
            'main_weight_sum': main_weight_sum,
            'shift_signal_generated': shift_signal is not None,
            'integration_successful': integration_successful
        }
        
        if integration_successful:
            logger.info("  ✅ Dynamic weightage integration test passed")
        else:
            logger.error("  ❌ Dynamic weightage integration failed")
        
    except Exception as e:
        results['tests']['dynamic_weightage_integration'] = {
            'status': 'FAILED',
            'error': str(e)
        }
        logger.error(f"  ❌ Dynamic weightage integration test failed: {e}")
    
    # Test 4: Golden File Output Validation
    logger.info("📄 Test 4: Golden File Output Validation")
    
    try:
        golden_file_path = '/srv/samba/shared/bt/backtester_stable/BTRUN/output/adaptive_shifting_tests/Nifty_Golden_Output.xlsx'
        
        if os.path.exists(golden_file_path):
            file_size = os.path.getsize(golden_file_path)
            
            # Try to read the Excel file to validate structure
            try:
                excel_data = pd.read_excel(golden_file_path, sheet_name=None)
                sheets = list(excel_data.keys())
                
                expected_sheets = ['PortfolioParameter', 'GeneralParameter', 'LegParameter', 'Metrics']
                sheets_present = all(sheet in sheets for sheet in expected_sheets)
                
                results['tests']['golden_file_output'] = {
                    'status': 'PASSED' if sheets_present else 'FAILED',
                    'file_exists': True,
                    'file_size_bytes': file_size,
                    'sheets_found': sheets,
                    'expected_sheets': expected_sheets,
                    'all_sheets_present': sheets_present,
                    'file_path': golden_file_path
                }
                
                if sheets_present:
                    logger.info(f"  ✅ Golden file output validation passed: {file_size} bytes")
                else:
                    logger.error(f"  ❌ Missing expected sheets in golden file")
                
            except Exception as read_error:
                results['tests']['golden_file_output'] = {
                    'status': 'FAILED',
                    'file_exists': True,
                    'file_size_bytes': file_size,
                    'read_error': str(read_error)
                }
                logger.error(f"  ❌ Error reading golden file: {read_error}")
        else:
            results['tests']['golden_file_output'] = {
                'status': 'FAILED',
                'file_exists': False,
                'file_path': golden_file_path
            }
            logger.error("  ❌ Golden file not found")
        
    except Exception as e:
        results['tests']['golden_file_output'] = {
            'status': 'FAILED',
            'error': str(e)
        }
        logger.error(f"  ❌ Golden file output validation failed: {e}")
    
    # Calculate final summary
    end_time = datetime.now()
    execution_time = (end_time - datetime.fromisoformat(results['start_time'])).total_seconds()
    
    passed_tests = sum(1 for test in results['tests'].values() if test['status'] == 'PASSED')
    failed_tests = sum(1 for test in results['tests'].values() if test['status'] == 'FAILED')
    total_tests = len(results['tests'])
    
    results['summary'] = {
        'total_tests': total_tests,
        'passed': passed_tests,
        'failed': failed_tests,
        'success_rate': (passed_tests / total_tests) * 100 if total_tests > 0 else 0,
        'execution_time_seconds': execution_time,
        'all_tests_passed': failed_tests == 0
    }
    
    results['end_time'] = end_time.isoformat()
    
    # Save final results
    output_dir = '/srv/samba/shared/bt/backtester_stable/BTRUN/output/adaptive_shifting_tests'
    results_file = os.path.join(output_dir, f'final_validation_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json')
    
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    # Print final summary
    logger.info("=" * 80)
    logger.info("🏆 FINAL ADAPTIVE SHIFTING SYSTEM VALIDATION SUMMARY")
    logger.info("=" * 80)
    logger.info(f"📊 Total Tests: {total_tests}")
    logger.info(f"✅ Passed: {passed_tests}")
    logger.info(f"❌ Failed: {failed_tests}")
    logger.info(f"📈 Success Rate: {results['summary']['success_rate']:.1f}%")
    logger.info(f"⏱️  Execution Time: {execution_time:.2f} seconds")
    logger.info(f"🎯 All Tests Passed: {'✅ YES' if results['summary']['all_tests_passed'] else '❌ NO'}")
    logger.info(f"📁 Results saved to: {results_file}")
    logger.info("=" * 80)
    
    return results

if __name__ == "__main__":
    """Run the final validation test."""
    print("🎯 Final Validation: Complete Adaptive Shifting System")
    print("Features: Shifting + Historical + DTE + Regime + Emergency + Golden Output")
    print("=" * 80)
    
    # Run final validation
    results = test_complete_adaptive_shifting_system()
    
    # Exit with appropriate code
    if results['summary']['all_tests_passed']:
        print("✅ All adaptive shifting system validation tests passed!")
        sys.exit(0)
    else:
        print("❌ Some validation tests failed!")
        sys.exit(1)
