#!/usr/bin/env python3
"""
Complete Market Regime System Implementation

This script completes the market regime system with all missing components:
- Excel input sheet parsing following BT patterns
- Live streaming integration with multi-timeframe aggregation
- Database integration for historical analysis
- Complete executor following BT patterns
- Template generation for easy configuration

Usage:
    python3 complete_market_regime_system.py [--mode MODE] [--config CONFIG_PATH]
"""

import sys
import logging
import argparse
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any

# Import all market regime components
from backtester_v2.market_regime.enhanced_regime_detector import Enhanced18RegimeDetector
from backtester_v2.market_regime.excel_config_manager import MarketRegimeExcelManager
from backtester_v2.market_regime.input_sheet_parser import MarketRegimeInputSheetParser
from backtester_v2.market_regime.executor import MarketRegimeExecutor
from backtester_v2.live_streaming.streaming_manager import LiveStreamingManager
from backtester_v2.live_streaming.data_aggregator import MarketDataAggregator

logger = logging.getLogger(__name__)

class CompleteMarketRegimeSystem:
    """
    Complete Market Regime System Implementation
    
    This class provides a complete implementation of the market regime system
    with all components integrated and following backtester_v2 patterns.
    """
    
    def __init__(self, config_path: str = None):
        """
        Initialize Complete Market Regime System
        
        Args:
            config_path (str, optional): Path to configuration file
        """
        self.config_path = config_path
        
        # Core components
        self.regime_detector = None
        self.excel_manager = None
        self.input_parser = None
        self.executor = None
        self.streaming_manager = None
        self.data_aggregator = None
        
        # System state
        self.system_initialized = False
        self.current_mode = None
        self.execution_results = []
        
        logger.info("CompleteMarketRegimeSystem initialized")
    
    def initialize_system(self, mode: str = 'backtest') -> bool:
        """
        Initialize the complete market regime system
        
        Args:
            mode (str): System mode ('backtest', 'live', 'template')
            
        Returns:
            bool: True if initialization successful
        """
        try:
            logger.info(f"Initializing complete market regime system in {mode} mode")
            
            self.current_mode = mode
            
            # Initialize core components
            self._initialize_core_components()
            
            # Mode-specific initialization
            if mode == 'backtest':
                self._initialize_backtest_mode()
            elif mode == 'live':
                self._initialize_live_mode()
            elif mode == 'template':
                self._initialize_template_mode()
            else:
                raise ValueError(f"Unknown mode: {mode}")
            
            self.system_initialized = True
            logger.info(f"✅ Complete market regime system initialized in {mode} mode")
            
            return True
            
        except Exception as e:
            logger.error(f"Error initializing system: {e}")
            return False
    
    def _initialize_core_components(self):
        """Initialize core system components"""
        try:
            # Initialize Excel configuration manager
            if self.config_path and Path(self.config_path).exists():
                self.excel_manager = MarketRegimeExcelManager(self.config_path)
                logger.info("✅ Excel configuration manager initialized")
            
            # Initialize input sheet parser
            self.input_parser = MarketRegimeInputSheetParser()
            logger.info("✅ Input sheet parser initialized")
            
            # Initialize executor
            self.executor = MarketRegimeExecutor()
            logger.info("✅ Market regime executor initialized")
            
        except Exception as e:
            logger.error(f"Error initializing core components: {e}")
            raise
    
    def _initialize_backtest_mode(self):
        """Initialize components for backtest mode"""
        try:
            # Initialize regime detector with configuration
            if self.excel_manager:
                detection_params = self.excel_manager.get_detection_parameters()
            else:
                detection_params = {}
            
            self.regime_detector = Enhanced18RegimeDetector(config=detection_params)
            logger.info("✅ Regime detector initialized for backtest mode")
            
        except Exception as e:
            logger.error(f"Error initializing backtest mode: {e}")
            raise
    
    def _initialize_live_mode(self):
        """Initialize components for live trading mode"""
        try:
            # Initialize regime detector
            if self.excel_manager:
                detection_params = self.excel_manager.get_detection_parameters()
                live_config = self.excel_manager.get_live_trading_config()
            else:
                detection_params = {}
                live_config = {}
            
            self.regime_detector = Enhanced18RegimeDetector(config=detection_params)
            
            # Initialize data aggregator
            aggregator_config = {
                '1min_max_candles': 1440,  # 1 day
                '5min_max_candles': 288,   # 1 day
                '15min_max_candles': 96,   # 1 day
                '30min_max_candles': 48,   # 1 day
                '1hour_max_candles': 24    # 1 day
            }
            self.data_aggregator = MarketDataAggregator(config=aggregator_config)
            
            # Initialize streaming manager
            streaming_config = {
                'aggregator': aggregator_config,
                'max_reconnection_attempts': 5,
                'enable_performance_logging': True
            }
            self.streaming_manager = LiveStreamingManager(config=streaming_config)
            
            # Add Kite streamer
            kite_config = {
                'type': 'kite',
                'regime_config': detection_params,
                'kite_config': {
                    'stream_interval_ms': live_config.get('StreamingIntervalMs', 100),
                    'regime_update_freq_sec': live_config.get('RegimeUpdateFreqSec', 60)
                }
            }
            self.streaming_manager.add_streamer('main_kite', kite_config)
            
            logger.info("✅ Live trading components initialized")
            
        except Exception as e:
            logger.error(f"Error initializing live mode: {e}")
            raise
    
    def _initialize_template_mode(self):
        """Initialize components for template generation mode"""
        try:
            # Template mode doesn't need heavy components
            logger.info("✅ Template mode initialized")
            
        except Exception as e:
            logger.error(f"Error initializing template mode: {e}")
            raise
    
    def run_backtest(self, input_sheet_path: str) -> Dict[str, Any]:
        """
        Run market regime backtest
        
        Args:
            input_sheet_path (str): Path to input sheet
            
        Returns:
            Dict: Backtest results
        """
        try:
            if self.current_mode != 'backtest':
                raise ValueError("System not in backtest mode")
            
            logger.info(f"Starting market regime backtest with input sheet: {input_sheet_path}")
            
            # Parse input sheets
            parsed_config = self.input_parser.parse_input_sheets(input_sheet_path)
            
            # Validate configuration
            if not parsed_config['validation_status']['is_valid']:
                logger.warning(f"Configuration validation warnings: {parsed_config['validation_status']['errors']}")
            
            # Execute backtest for each strategy
            backtest_results = []
            
            for strategy in parsed_config['strategies']:
                if strategy['enabled'] and strategy['regime_detection_enabled']:
                    logger.info(f"Running backtest for strategy: {strategy['strategy_id']}")
                    
                    # Prepare execution parameters
                    portfolio = next(
                        (p for p in parsed_config['portfolios'] if p['portfolio_name'] == strategy['portfolio_name']),
                        parsed_config['portfolios'][0]
                    )
                    
                    execution_params = {
                        'excel_config_path': input_sheet_path,
                        'start_date': portfolio['start_date'],
                        'end_date': portfolio['end_date'],
                        'symbols': ['NIFTY'],  # Default symbol
                        'timeframes': ['1min', '5min', '15min'],
                        'live_mode': False,
                        'backtest_mode': True,
                        'strategy_config': strategy,
                        'portfolio_config': portfolio,
                        'regime_configuration': parsed_config['regime_configuration']
                    }
                    
                    # Parse input for executor
                    parsed_params = self.executor.parse_input(execution_params)
                    
                    # Execute backtest
                    result = self.executor.execute_backtest(parsed_params)
                    result['strategy_info'] = strategy
                    result['portfolio_info'] = portfolio
                    
                    backtest_results.append(result)
            
            # Consolidate results
            consolidated_results = {
                'individual_results': backtest_results,
                'consolidated_summary': self._consolidate_backtest_results(backtest_results),
                'input_configuration': parsed_config,
                'execution_timestamp': datetime.now().isoformat(),
                'total_strategies_tested': len(backtest_results)
            }
            
            self.execution_results.append(consolidated_results)
            
            logger.info(f"✅ Backtest completed: {len(backtest_results)} strategies tested")
            
            return consolidated_results
            
        except Exception as e:
            logger.error(f"Error running backtest: {e}")
            return {'error': str(e)}
    
    def start_live_trading(self, input_sheet_path: str) -> Dict[str, Any]:
        """
        Start live market regime trading
        
        Args:
            input_sheet_path (str): Path to input sheet
            
        Returns:
            Dict: Live trading status
        """
        try:
            if self.current_mode != 'live':
                raise ValueError("System not in live mode")
            
            logger.info(f"Starting live market regime trading with input sheet: {input_sheet_path}")
            
            # Parse input sheets
            parsed_config = self.input_parser.parse_input_sheets(input_sheet_path)
            
            # Check if live trading is enabled
            if not parsed_config['live_trading_config']['enable_live_trading']:
                raise ValueError("Live trading is not enabled in configuration")
            
            # Prepare execution parameters
            execution_params = {
                'excel_config_path': input_sheet_path,
                'live_mode': True,
                'backtest_mode': False,
                'regime_configuration': parsed_config['regime_configuration'],
                'live_trading_config': parsed_config['live_trading_config']
            }
            
            # Parse input for executor
            parsed_params = self.executor.parse_input(execution_params)
            
            # Start live execution
            live_status = self.executor.execute_live_trading(parsed_params)
            
            if live_status['status'] == 'running':
                logger.info("✅ Live market regime trading started successfully")
            else:
                logger.error(f"Failed to start live trading: {live_status}")
            
            return live_status
            
        except Exception as e:
            logger.error(f"Error starting live trading: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def generate_templates(self, output_dir: str) -> Dict[str, str]:
        """
        Generate Excel templates for market regime configuration
        
        Args:
            output_dir (str): Output directory for templates
            
        Returns:
            Dict: Generated template paths
        """
        try:
            logger.info(f"Generating market regime templates in: {output_dir}")
            
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            generated_templates = {}
            
            # Generate basic template
            basic_template_path = output_path / 'market_regime_basic_config.xlsx'
            excel_manager = MarketRegimeExcelManager()
            basic_path = excel_manager.generate_excel_template(str(basic_template_path))
            generated_templates['basic'] = basic_path
            
            # Generate advanced template with examples
            advanced_template_path = output_path / 'market_regime_advanced_config.xlsx'
            advanced_manager = MarketRegimeExcelManager()
            advanced_path = advanced_manager.generate_excel_template(str(advanced_template_path))
            
            # Load and enhance with examples
            advanced_manager.load_configuration(advanced_path)
            advanced_manager.update_parameter('RegimeDetectionConfig', 'ConfidenceThreshold', 0.75)
            advanced_manager.update_parameter('RegimeDetectionConfig', 'RegimeSmoothing', 5)
            advanced_manager.update_parameter('LiveTradingConfig', 'EnableLiveTrading', 'YES')
            advanced_manager.save_configuration(advanced_path)
            generated_templates['advanced'] = advanced_path
            
            # Generate input sheet template (following BT pattern)
            input_sheet_path = output_path / 'market_regime_input_sheet.xlsx'
            self._generate_input_sheet_template(str(input_sheet_path))
            generated_templates['input_sheet'] = str(input_sheet_path)
            
            logger.info(f"✅ Generated {len(generated_templates)} templates")
            
            return generated_templates
            
        except Exception as e:
            logger.error(f"Error generating templates: {e}")
            return {'error': str(e)}
    
    def _generate_input_sheet_template(self, output_path: str):
        """Generate input sheet template following BT patterns"""
        try:
            import pandas as pd
            
            # PortfolioSetting sheet
            portfolio_data = [{
                'PortfolioName': 'MarketRegime_Portfolio_1',
                'StartDate': '01_01_2024',
                'EndDate': '31_12_2024',
                'Multiplier': 1,
                'IsTickBT': 'NO',
                'SlippagePercent': 0.1
            }]
            
            # StrategySetting sheet
            strategy_data = [{
                'Enabled': 'YES',
                'PortfolioName': 'MarketRegime_Portfolio_1',
                'StrategyType': 'MARKET_REGIME',
                'StrategyExcelFilePath': output_path
            }]
            
            # Write to Excel
            with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
                pd.DataFrame(portfolio_data).to_excel(writer, sheet_name='PortfolioSetting', index=False)
                pd.DataFrame(strategy_data).to_excel(writer, sheet_name='StrategySetting', index=False)
            
            logger.info(f"Generated input sheet template: {output_path}")
            
        except Exception as e:
            logger.error(f"Error generating input sheet template: {e}")
            raise
    
    def _consolidate_backtest_results(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Consolidate multiple backtest results"""
        try:
            if not results:
                return {'message': 'No results to consolidate'}
            
            # Calculate aggregate metrics
            total_detections = sum(
                len(r.get('regime_detection_results', [])) for r in results
            )
            
            avg_confidence = 0
            if total_detections > 0:
                all_confidences = []
                for result in results:
                    regime_results = result.get('regime_detection_results', [])
                    all_confidences.extend([r['confidence'] for r in regime_results])
                avg_confidence = sum(all_confidences) / len(all_confidences)
            
            # Regime distribution across all strategies
            regime_distribution = {}
            for result in results:
                regime_results = result.get('regime_detection_results', [])
                for regime_result in regime_results:
                    regime_type = regime_result['regime_type']
                    regime_distribution[regime_type] = regime_distribution.get(regime_type, 0) + 1
            
            return {
                'total_strategies': len(results),
                'total_regime_detections': total_detections,
                'average_confidence': avg_confidence,
                'regime_distribution': regime_distribution,
                'successful_strategies': len([r for r in results if 'error' not in r]),
                'failed_strategies': len([r for r in results if 'error' in r])
            }
            
        except Exception as e:
            logger.error(f"Error consolidating results: {e}")
            return {'error': str(e)}
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        try:
            status = {
                'system_initialized': self.system_initialized,
                'current_mode': self.current_mode,
                'components_status': {
                    'regime_detector': self.regime_detector is not None,
                    'excel_manager': self.excel_manager is not None,
                    'input_parser': self.input_parser is not None,
                    'executor': self.executor is not None,
                    'streaming_manager': self.streaming_manager is not None,
                    'data_aggregator': self.data_aggregator is not None
                },
                'execution_results_count': len(self.execution_results)
            }
            
            # Add executor status if available
            if self.executor:
                status['executor_status'] = self.executor.get_execution_status()
            
            # Add streaming status if in live mode
            if self.streaming_manager:
                status['streaming_status'] = self.streaming_manager.get_streaming_status()
            
            return status
            
        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return {'error': str(e)}
    
    def stop_system(self) -> bool:
        """Stop the system and cleanup resources"""
        try:
            logger.info("Stopping complete market regime system")
            
            # Stop live execution if running
            if self.executor:
                self.executor.stop_live_execution()
            
            # Stop streaming if running
            if self.streaming_manager:
                self.streaming_manager.stop_streaming()
            
            self.system_initialized = False
            
            logger.info("✅ System stopped successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping system: {e}")
            return False

def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(description='Complete Market Regime System')
    parser.add_argument('--mode', choices=['backtest', 'live', 'template'], 
                       default='template', help='System mode')
    parser.add_argument('--config', help='Path to configuration file')
    parser.add_argument('--input-sheet', help='Path to input sheet for backtest/live mode')
    parser.add_argument('--output-dir', default='input_sheets/market_regime', 
                       help='Output directory for templates')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose logging')
    
    args = parser.parse_args()
    
    # Configure logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S'
    )
    
    try:
        logger.info("🚀 Complete Market Regime System")
        logger.info(f"Mode: {args.mode.upper()}")
        logger.info("-" * 60)
        
        # Initialize system
        system = CompleteMarketRegimeSystem(config_path=args.config)
        
        if not system.initialize_system(mode=args.mode):
            logger.error("Failed to initialize system")
            return 1
        
        # Execute based on mode
        if args.mode == 'template':
            logger.info("Generating Excel templates...")
            templates = system.generate_templates(args.output_dir)
            
            if 'error' not in templates:
                logger.info("✅ Template generation completed!")
                for template_type, path in templates.items():
                    logger.info(f"   {template_type.upper()}: {path}")
            else:
                logger.error(f"Template generation failed: {templates['error']}")
                return 1
                
        elif args.mode == 'backtest':
            if not args.input_sheet:
                logger.error("Input sheet required for backtest mode")
                return 1
            
            logger.info(f"Running backtest with input sheet: {args.input_sheet}")
            results = system.run_backtest(args.input_sheet)
            
            if 'error' not in results:
                logger.info("✅ Backtest completed successfully!")
                summary = results['consolidated_summary']
                logger.info(f"   Strategies tested: {summary['total_strategies']}")
                logger.info(f"   Total detections: {summary['total_regime_detections']}")
                logger.info(f"   Average confidence: {summary['average_confidence']:.2f}")
            else:
                logger.error(f"Backtest failed: {results['error']}")
                return 1
                
        elif args.mode == 'live':
            if not args.input_sheet:
                logger.error("Input sheet required for live mode")
                return 1
            
            logger.info(f"Starting live trading with input sheet: {args.input_sheet}")
            status = system.start_live_trading(args.input_sheet)
            
            if status['status'] == 'running':
                logger.info("✅ Live trading started successfully!")
                logger.info("Press Ctrl+C to stop...")
                
                try:
                    while True:
                        import time
                        time.sleep(10)
                        # Print status updates
                        system_status = system.get_system_status()
                        logger.info(f"System running... Components active: {sum(system_status['components_status'].values())}")
                        
                except KeyboardInterrupt:
                    logger.info("Stopping live trading...")
                    system.stop_system()
            else:
                logger.error(f"Failed to start live trading: {status}")
                return 1
        
        # Print final status
        final_status = system.get_system_status()
        logger.info("\n📊 Final System Status:")
        logger.info(f"   Initialized: {final_status['system_initialized']}")
        logger.info(f"   Mode: {final_status['current_mode']}")
        logger.info(f"   Components: {sum(final_status['components_status'].values())}/{len(final_status['components_status'])}")
        
        return 0
        
    except Exception as e:
        logger.error(f"System execution failed: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
