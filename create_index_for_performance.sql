-- Create index on index_name column for better performance
-- This will significantly speed up queries that filter by index_name

-- Check if index already exists and create if not
-- HeavyDB syntax for creating index
CREATE INDEX IF NOT EXISTS idx_nifty_option_chain_index_name 
ON nifty_option_chain (index_name);

-- Also create composite index for common query patterns
CREATE INDEX IF NOT EXISTS idx_nifty_option_chain_index_date 
ON nifty_option_chain (index_name, trade_date);

-- Analyze table to update statistics
-- This helps the query optimizer make better decisions
ANALYZE nifty_option_chain;