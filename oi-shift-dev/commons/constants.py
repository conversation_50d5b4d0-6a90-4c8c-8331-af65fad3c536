RESTART_INSTANCE_FILE_PATH = "/restart_instance.json"
SQUAREOFF_INSTANCE_FILE_PATH = "/squareoff_instance.json"


STRIKE_DIFF = {
    "NIFTY": 50,
    "BANKNIFTY": 100,
    "FINNIFTY": 50,
    "MIDCPNIFTY": 25,
    "BANKEX": 100,
    "SENSEX": 100,
    "CRUDEOIL": 50,
    "CRUDEOILM": 50
}

LOT_SIZE = {
    
}

FREEZE_QTY = {
    "NIFTY": 72,
    "BANKNIFTY": 60,
    "FINNIFTY": 45,
    "MIDCPNIFTY": 35,
    "BANKEX": 50,
    "SENSEX": 100,
    "CRUDEOIL": 100,
    "CRUDEOILM": 100
}

INDICES = {
    "NIFTY": ("NFO", "NIFTY"),
    "BANKNIFTY": ("NFO", "BANKNIFTY"),
    "FINNIFTY": ("NFO", "FINNIFTY"),
    "MIDCPNIFTY": ("NFO", "MIDCPNIFTY"),
    "SENS<PERSON>": ("BF<PERSON>", "SENSEX"),
    "BANKEX": ("BF<PERSON>", "BANKEX"),
    "CRUDEOIL": ("MCX", "CRUDEOIL"),
    "CRUDEOILM": ("MCX", "CRUDEOILM")
}

MARKET_START_TIME = {
    "NFO": 91500,
    "BFO": 91500,
    "MCX": 90000
}

MARKET_END_TIME = {
    "NFO": 153000,
    "BFO": 153000,
    "MCX": 233000
}

HOLIDAYS = [
    241225,
    250226,
    250314,
    250331,
    250410,
    250414,
    250418,
    250501,
    250815,
    250827,
    251002,
    251021,
    251022,
    251105,
    251225
]


VERSION = "V3.3.3 (7 MAR 2025)"

