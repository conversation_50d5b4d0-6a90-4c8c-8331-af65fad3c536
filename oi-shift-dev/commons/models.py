from datetime import datetime
from typing import List, Dict
from commons.enums import (
    ExpiryType,
    OiCriteria,
    OiType,
    Underlying,
    InstrumentType,
    OptionType,
    ExchangeType,
    ProductType,
    OrderType,
    PositionType,
    OrderStatus,
    StrategyStatus,
    PositionStatus,
)
from commons.constants import LOT_SIZE, FREEZE_QTY

class Singleton(object):
    _instance = None
    def __new__(class_, *args, **kwargs):
        if not isinstance(class_._instance, class_):
            class_._instance = object.__new__(class_, *args, **kwargs)
        return class_._instance

class Ohlc:
    def __init__(self, token: str, ltp: float):
        self.token = token
        self.ltp = ltp

    def __str__(self) -> str:
        return f"Ohlc(token: {self.token}, ltp: {self.ltp})"


class Instrument:
    def __init__(
        self,
        pricefeed_token: str,
        exchange_token: str,
        exchange: ExchangeType,
        underlying: Underlying,
        instrument_type: InstrumentType,
        expiry_date: datetime,
        strike_price: float,
        option_type: OptionType,
        trading_symbol: str,
        lot_size: int = 1,
    ):
        self.pricefeed_token = pricefeed_token
        self.exchange_token = exchange_token
        self.exchange = exchange
        self.underlying = underlying
        self.instrument_type = instrument_type
        self.expiry_date = expiry_date
        self.strike_price = strike_price
        self.option_type = option_type
        self.trading_symbol = trading_symbol
        self.lot_size = lot_size
        
    def __str__(self):
        return f"Instrument(pricefeed_token={self.pricefeed_token}, exchange_token={self.exchange_token}, exchange={self.exchange}, underlying={self.underlying}, instrument_type={self.instrument_type}, expiry_date={self.expiry_date}, strike_price={self.strike_price}, option_type={self.option_type}, trading_symbol={self.trading_symbol})"


class Strategy:
    def __init__(
            self, 
            id: str,
            instance_id: str,
            underlying: Underlying,
            expiry_type: ExpiryType,
            oi_type: OiType,
            itm_count: int,
            otm_count: int,
            entry_times: dict,
            exit_time: int,
            quantity: int, 
            shift_delay: int,
            order_strike_diff: int,
            oi_condition: str,
            oi_condition_type: OiCriteria
        ):
        self.id = id
        self.instance_id = instance_id
        self.underlying = underlying
        self.expiry_type = expiry_type
        self.oi_type = oi_type
        self.itm_count = itm_count
        self.otm_count = otm_count
        self.entry_times = entry_times
        self.exit_time = exit_time
        self.quantity = quantity
        self.freeze_qty = FREEZE_QTY[self.underlying.name]
        self.lot_size = LOT_SIZE[self.underlying.name]
        self.shift_delay = shift_delay
        self.order_strike_diff = order_strike_diff

        self.oi_condition = oi_condition
        self.oi_condition_type = oi_condition_type

        self.underlying_token = None
        self.running_call_qty = 0
        self.running_put_qty = 0

        self.fetch_oi_retry_count = 0
        self.last_retry_time = None

        self.runnning_call: Position = None
        self.runnning_put: Position = None
        self.prev_max_oi_call = None
        self.prev_max_oi_put = None
        self.archived_positions: None|List[Position] = []

        self.is_shifting_call = False
        self.is_shifting_put = False
        self.call_shift_trigger_timestamp = None
        self.put_shift_trigger_timestamp = None

        self.last_call_shift_timestamp = None
        self.last_put_shift_timestamp = None

        self.next_call_instrument: Instrument = None
        self.next_put_instrument: Instrument = None

        self.pnl = 0

        self.status: StrategyStatus = StrategyStatus.CREATED
        self.manual_sq_off = False

    def __str__(self) -> str:
        return (
            f"Strategy("
            f"id={self.id}, "
            f"instance_id={self.instance_id}, "
            f"underlying={self.underlying.name}, "
            f"expiry_type={self.expiry_type.name}, "
            f"oi_type={self.oi_type}, "
            f"itm_count={self.itm_count}, "
            f"otm_count={self.otm_count}, "
            f"entry_times={self.entry_times}, "
            f"exit_time={self.exit_time}, "
            f"quantity={self.quantity}, "
            f"running_call_qty={self.running_call_qty}, "
            f"running_put_qty={self.running_put_qty}, "
            f"freeze_qty={self.freeze_qty}, "
            f"lot_size={self.lot_size}, "
            f"shift_delay={self.shift_delay}, "
            f"order_strike_diff={self.order_strike_diff}, "
            f"oi_condition=({self.oi_condition}), "
            f"oi_condition_type={self.oi_condition_type.name}"
            f")"
        )

class Position:
    def __init__(self, initial_order_id: str, instrument_id: str):
        self.initial_order_id = initial_order_id
        self.instrument_id = instrument_id

        self.status = PositionStatus.PENDING
        self.orders: List[Order] = []
        
class Order:
    def __init__(
        self,
        strategy_id: str,
        instance_id: str,
        id: str,
        intstrument_id: str,
        product_type: ProductType,
        order_type: OrderType,
        side: PositionType,
        quantity: int,
        order_strike_diff: int
    ):
        self.strategy_id = strategy_id
        self.id = id
        self.instance_id = instance_id
        self.instrument_id = intstrument_id
        self.product_type = product_type
        self.order_type = order_type
        self.side = side
        self.quantity = quantity
        self.order_strike_diff= order_strike_diff
        self.creation_time = datetime.now()
        
        self.status = OrderStatus.CREATED
        self.portfolio_names: Dict[str, str] = {}
        
    def __str__(self):
        return (
            f"Order(strategy_id={self.strategy_id}, "
            f"instance_id={self.instance_id}, "
            f"id={self.id}, "
            f"instrument_id={self.instrument_id}, "
            f"product_type={self.product_type}, "
            f"order_type={self.order_type}, "
            f"side={self.side}, "
            f"quantity={self.quantity}, "
            f"order_strike_diff={self.order_strike_diff}, "
            f"creation_time={self.creation_time}, "
            f"portfolio_names={self.portfolio_names}"
            f")"
        )

class OpenInterest:
    def __init__(
        self,
        instrument: Instrument,
        oi: int,
        previous_oi_close: int,
    ):
        self.instrument = instrument
        self.oi = oi
        self.previous_oi_close = previous_oi_close
    
    def __str__(self):
        ...