import enum


class ExchangeType(enum.Enum):
    NFO = "NFO"
    BFO = "BFO"
    MCX = "MCX"
    

class Underlying(enum.Enum):
    NIFTY = "NIFTY"
    BANKNIFTY = "BANKNIFTY"
    FINNIFTY = "FINNIFTY"
    MIDCPNIFTY = "MIDCPNIFTY"
    BANKEX = "BANKEX"
    SENSEX = "SENSEX"
    CRUDEOIL = "CRUDEOIL"
    CRUDEOILM = "CRUDEOILM"


class OiType(enum.Enum):
    MAXOI1 = "MAXOI1"
    MAXOI2 = "MAXOI2" 
    MAXOI3 = "MAXOI3"
    MAXCOI1 = "MAXCOI1"
    MAXCOI2 = "MAXCOI2"
    MAXCOI3 = "MAXCOI3"


class OiCriteria(enum.Enum):
    NONE = None 
    ON_STRIKE = "ON_STRIKE"
    ON_CUMULATIVE = "ON_CUMULATIVE"

class UnderlyingType(enum.Enum):
    SPOT = "SPOT"
    FUTURE = "FUTURE"
    SYNTHETIC = "SYNTHETIC"


class InstrumentType(enum.Enum):
    OPTIDX = "OPTIDX"
    FUTIDX = "FUTIDX"
    INDICES = "INDICES"


class ExpiryType(enum.Enum):
    WEEKLY = "WEEKLY"
    NEXTWEEKLY = "NEXTWEEKLY"
    MONTHLY = "MONTHLY"


class OptionType(enum.Enum):
    CE = "CE"
    PE = "PE"
    FUT = "FUT"
    EQ = "EQ"


class ShiftDirection(enum.Enum):
    CENTER = "CENTER"
    UP = "UP"
    DOWN = "DOWN"
    

class DecrType(enum.Enum):
    LOTS = "LOTS"
    PERCENTAGE = "PERCENTAGE"


class PnlType(enum.Enum):
    POINTS = "POINTS"
    PERCENTAGE = "PERCENTAGE"


class OrderType(enum.Enum):
    MARKET = "MARKET"


class ProductType(enum.Enum):
    NRML = "NRML"
    MIS = "MIS"


class PositionType(enum.Enum):
    BUY = 1
    SELL = -1


class PositionStatus:
    PENDING = "PENDING"
    COMPLETE = "COMPLETE"
    ERROR = "ERROR"
    
    
class StrategyStatus(enum.Enum):
    CREATED = "CREATED"
    HEDGE_SELECTED = "HEDGE_SELECTED"
    RUNNING = "RUNNING"
    PAUSE = "PAUSE"
    SQUARING_OFF = "SQUARING_OFF"
    SQUARED_OFF = "SQUARED_OFF"
    STOPPING = "STOPPED"
    STOPPED = "STOPPED"
    COMPLETED = "COMPLETED"
    ERROR = "ERROR"


class OrderStatus(enum.Enum):
    CREATED = "CREATED"
    SENT = "SENT"
    WORKING = "WORKING"
    OPEN = "OPEN"
    FILLED = "FILLED"
    CANCELLED = "CANCELLED"
    REJECTED = "REJECTED"


class BrokerType(enum.Enum):
    ALGOBABA_WEBHOOK = "ALGOBABA_WEBHOOK"
    ALGOBABA_HTTP = "ALGOBABA_HTTP"
    OWN_PLATFORM = "OWN_PLATFORM"


class BrokerResponseType(enum.Enum):
    PLACEORDER = "PLACEORDER"
    MODIFYORDER = "MODIFYORDER"
    CANCELORDER = "CANCELORDER"
    GETORDERDETAILS = "GETORDERDETAILS"


class UserMessageType(enum.Enum):
    CONNECT = "CONNECT"
    START = "START"
    PAUSE = "PAUSE"
    RESUME = "RESUME"
    UPDATE = "UPDATE"
    STOP = "STOP"


class EntryBy(enum.Enum):
    ATM_TO_OTM = "ATM_TO_OTM"
    OTM_TO_ATM = "OTM_TO_ATM"
    RANDOM = "RANDOM"


class LogType(enum.Enum):
    STRATEGYUPDATE = "STRATEGYUPDATE"
    LTPUPDATE = "LTPUPDATE"
    ORDERUPDATE = "ORDERUPDATE"
    FUNDUPDATE = "FUNDUPDATE"
    BACKFILL = "BACKFILL"
    SYNTHUPDATE = "SYNTHUPDATE"
    STRADDLEUPDATE = "STRADDLEUPDATE"
