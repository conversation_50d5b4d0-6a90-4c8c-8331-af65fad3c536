import pandas as pd
import requests
from zipfile import ZipFile
from io import BytesIO, <PERSON><PERSON>
from typing import List
from datetime import datetime, timedelta, time
from time import sleep
from dateutil.relativedelta import relativedelta
from commons.models import Instrument
from commons.utils import generate_trading_symbol, is_holiday
from commons.constants import INDICES, LOT_SIZE
from commons.enums import InstrumentType, OiType, OptionType, ExchangeType, Underlying
from config import Config
from data import Cache, instruments, underlying_instruments, user_strategies
from pricefeed import previous_oi
import logging

logger = logging.getLogger(__name__)


def filter_master_data_by_index(
    master_data: dict, exchange: str, fno_name: str, underlying: str
):
    und_name = underlying
    if underlying == "NIFTY":
        und_name = "NIFTY 50"
    elif underlying == "BANKNIFTY":
        und_name = "NIFTY BANK"
    elif underlying == "FINNIFTY":
        und_name = "NIFTY FIN SERVICE"
    elif underlying == "MIDCPNIFTY":
        und_name = "NIFTY MID SELECT"
    elif underlying == "SENSEX50":
        und_name = "SENSEX"
    elif underlying == "CRUDEOIL":
        und_name = "MCXCRUDEX"
    elif underlying == "CRUDEOILM":
        und_name = "MCXCRUDEX"

    fno_data = master_data[
        (master_data["exchange"] == exchange) & (master_data["name"] == fno_name)
    ].copy()

    fno_data.loc[:, "expiry_int"] = (
        fno_data["expiry"].str.replace("-", "").astype(int) - ********
    )
    opt_expiries = fno_data[fno_data["segment"] == f"{exchange}-OPT"][
        "expiry_int"
    ].unique()
    fut_expiries = fno_data[fno_data["segment"] == f"{exchange}-FUT"][
        "expiry_int"
    ].unique()
    opt_expiries.sort()
    fut_expiries.sort()

    fno_data["expiry"] = pd.to_datetime(fno_data["expiry"], format="%Y-%m-%d")
    weekly_expiry = int(opt_expiries[0])
    next_weekly_expiry = int(opt_expiries[1])
    monthly_expiry = int(fut_expiries[0])
    if exchange in [ExchangeType.BFO.name, ExchangeType.MCX.name]:
        ## getting monthly expiry for BSE indexes
        today = datetime.now().date()
        weekly_expiry_date = datetime.strptime(str(weekly_expiry), "%y%m%d").date()
        if today.month != weekly_expiry_date.month:
            today += relativedelta(months=1)
        current_month_expiry = fno_data[(fno_data["expiry"].dt.month == today.month) & (fno_data["expiry"].dt.year == today.year)]
        monthly_expiry = int(current_month_expiry["expiry"].max().strftime('%y%m%d'))

    logger.info(f"{underlying}, {weekly_expiry}, {next_weekly_expiry}, {monthly_expiry}")

    fut_instrument = fno_data[
        (fno_data["segment"] == f"{exchange}-FUT")
        & (fno_data["expiry_int"] == monthly_expiry)
    ].iloc[0]

    if underlying in ["CRUDEOIL", "CRUDEOILM"]:
        cash_instrument = fut_instrument
    else:
        cash_instrument = master_data[
            (master_data["tradingsymbol"] == und_name)
            & (master_data["segment"] == "INDICES")
        ].iloc[0]

    underlying_instruments[cash_instrument["instrument_token"]] = Instrument(
        cash_instrument["instrument_token"],
        cash_instrument["exchange_token"],
        ExchangeType(exchange),
        Underlying(underlying),
        InstrumentType("INDICES"),
        datetime(1970, 1, 1),
        0,
        OptionType(cash_instrument["instrument_type"]),
        f"{underlying}",
    )

    Cache().push(f"{underlying}_WEEKLY", weekly_expiry)
    Cache().push(f"{underlying}_NEXTWEEKLY", next_weekly_expiry)
    Cache().push(f"{underlying}_MONTHLY", monthly_expiry)
    Cache().push(
        f"{underlying}_FUT",
        Instrument(
            fut_instrument["instrument_token"],
            fut_instrument["exchange_token"],
            ExchangeType(exchange),
            Underlying(underlying),
            InstrumentType("FUTIDX"),
            fut_instrument["expiry"],
            0,
            OptionType(fut_instrument["instrument_type"]),
            f"{underlying}_FUT",
        ),
    )
    Cache().push(
        fut_instrument["instrument_token"],
        Instrument(
            fut_instrument["instrument_token"],
            fut_instrument["exchange_token"],
            ExchangeType(exchange),
            Underlying(underlying),
            InstrumentType("FUTIDX"),
            fut_instrument["expiry"],
            0,
            OptionType(fut_instrument["instrument_type"]),
            f"{underlying}_FUT",
        ),
    )
    Cache().push(
        f"{underlying}",
        Instrument(
            cash_instrument["instrument_token"],
            cash_instrument["exchange_token"],
            ExchangeType(exchange),
            Underlying(underlying),
            InstrumentType("INDICES"),
            datetime(1970, 1, 1),
            0,
            OptionType(cash_instrument["instrument_type"]),
            f"{underlying}",
        ),
    )
    Cache().push(
        cash_instrument["instrument_token"],
        Instrument(
            cash_instrument["instrument_token"],
            cash_instrument["exchange_token"],
            ExchangeType(exchange),
            Underlying(underlying),
            InstrumentType("INDICES"),
            datetime(1970, 1, 1),
            0,
            OptionType(cash_instrument["instrument_type"]),
            f"{underlying}",
        ),
    )

    opt_data = fno_data[
        (fno_data["segment"] == f"{exchange}-OPT")
        & (fno_data["expiry_int"].isin([weekly_expiry, next_weekly_expiry, monthly_expiry]))
    ]

    lot_size = opt_data["lot_size"].unique()[0]
    LOT_SIZE[underlying] = int(lot_size)

    for _, row in opt_data.iterrows():
        generated_trading_symbol = generate_trading_symbol(
            row["exchange"],
            row["name"],
            "OPTIDX",
            row["expiry_int"],
            row["strike"],
            row["instrument_type"],
        )
        Cache().push(
            generated_trading_symbol,
            Instrument(
                row["instrument_token"],
                row["exchange_token"],
                ExchangeType(exchange),
                Underlying(underlying),
                InstrumentType("OPTIDX"),
                row["expiry"],
                row["strike"],
                OptionType(row["instrument_type"]),
                generated_trading_symbol,
                row["lot_size"],
            ),
        )
        Cache().push(
            row["instrument_token"],
            Instrument(
                row["instrument_token"],
                row["exchange_token"],
                ExchangeType(exchange),
                Underlying(underlying),
                InstrumentType("OPTIDX"),
                row["expiry"],
                row["strike"],
                OptionType(row["instrument_type"]),
                generated_trading_symbol,
                row["lot_size"],
            ),
        )
        instruments.append(
            Instrument(
                row["instrument_token"],
                row["exchange_token"],
                ExchangeType(exchange),
                Underlying(underlying),
                InstrumentType("OPTIDX"),
                row["expiry"],
                row["strike"],
                OptionType(row["instrument_type"]),
                generated_trading_symbol,
                row["lot_size"],
            )
        )
    instruments.append(
        Instrument(
            fut_instrument["instrument_token"],
            fut_instrument["exchange_token"],
            ExchangeType(exchange),
            Underlying(underlying),
            InstrumentType("FUTIDX"),
            fut_instrument["expiry"],
            0,
            OptionType(fut_instrument["instrument_type"]),
            f"{underlying}_FUT",
        )
    )
    instruments.append(
        Instrument(
            cash_instrument["instrument_token"],
            cash_instrument["exchange_token"],
            ExchangeType(exchange),
            Underlying(underlying),
            InstrumentType("INDICES"),
            datetime(1970, 1, 1),
            0,
            OptionType(cash_instrument["instrument_type"]),
            f"{underlying}",
        )
    )

    return instruments


def load_instruments():
    master_data = pd.read_csv("https://api.kite.trade/instruments")

    all_instruments: List[Instrument] = []
    for index, index_info in INDICES.items():
        if index not in Config.INDEX_NAMES.values():
            continue
        instruments = filter_master_data_by_index(
            master_data=master_data,
            exchange=index_info[0],
            fno_name=index_info[1],
            underlying=index,
        )
        all_instruments += instruments

    logging.info(len(instruments))


def get_previous_trading_day_bhavcopy():
    if not any(
        strategy.oi_type in [OiType.MAXCOI1, OiType.MAXCOI2, OiType.MAXCOI3]
        for strategy in user_strategies.values()
    ):
        return
    logger.info(f"Loading previous day OI values")
    underlying_names = []
    expiries = {}
    for instrument in underlying_instruments.values():
        underlying = instrument.underlying.name
        underlying_names.append(underlying)
        expiries[underlying] = [Cache().pull(f"{underlying}_WEEKLY"), Cache().pull(f"{underlying}_NEXTWEEKLY"), Cache().pull(f"{underlying}_MONTHLY")]
    
    today = datetime.now()
    previous_trading_day = today - timedelta(days=1)
    while is_holiday(previous_trading_day):
        previous_trading_day -= timedelta(days=1)

    ##NSE
    try:
        url = f"https://nsearchives.nseindia.com/content/fo/BhavCopy_NSE_FO_0_0_0_{previous_trading_day.strftime("%Y%m%d")}_F_0000.csv.zip"
        headers = {
            'Referer': 'https://www.nseindia.com/all-reports',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:80.0) Gecko/20100101 Firefox/80.0'
        }

        response = requests.get(url, headers=headers)
        if response.status_code != 200:
            raise ValueError(f"Failed to download nse bhavcopy file. {response.status_code}:::{response.text}")

        bhavcopy_data = []
        with ZipFile(BytesIO(response.content)) as zip_file:
            bhavcopy_file = zip_file.namelist()[0]
            
            with zip_file.open(bhavcopy_file) as f:
                _ = f.readline() # Skip the header
                while True:
                    line = f.readline()
                    if not line:
                        break
                    bhavcopy_data.append(line.decode('utf-8').split(','))

        for data in bhavcopy_data:
            underlying_name = data[7]
            if underlying_name not in underlying_names:
                continue
            lot_size = LOT_SIZE[underlying_name]
            expiry = data[10]
            expiry_parts = expiry.split("-")
            expiry_int = int(str(int(expiry_parts[0])-2000)+expiry_parts[1]+expiry_parts[2])
            if expiry_int not in expiries[underlying_name]:
                continue
            oi = int(data[22])
            option_type = data[12]
            if option_type not in [OptionType.CE.name, OptionType.PE.name]:
                continue
            strike = float(data[11])
            if underlying_name not in previous_oi:
                previous_oi[underlying_name] = {}
            if expiry not in previous_oi[underlying_name]:
                previous_oi[underlying_name][expiry] = {"CE": {}, "PE": {}}
            previous_oi[underlying_name][expiry][option_type][strike] = int(oi/lot_size)
    except:
        logger.debug(f"Error while loading nse bhavcopy", exc_info=True)
        logger.warning(f"Error while loading previous day oi from NSE bhavcopy")

    ##BSE
    try:
        url = f"https://www.bseindia.com/download/Bhavcopy/Derivative/BhavCopy_BSE_FO_0_0_0_{previous_trading_day.strftime("%Y%m%d")}_F_0000.CSV"
        headers = {
            'Referer': 'https://www.nseindia.com/all-reports',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:80.0) Gecko/20100101 Firefox/80.0'
        }
        response = requests.get(url, headers=headers)
        if response.status_code != 200:
            raise ValueError(f"Failed to download bse bhavcopy file. {response.status_code}:::{response.text}")
        bse_data= pd.read_csv(StringIO(response.text))

        for _, row in bse_data.iterrows():
            underlying_name = row["TckrSymb"]
            if underlying_name not in underlying_names:
                continue
            lot_size = LOT_SIZE[underlying_name]
            expiry = row["XpryDt"]
            expiry_parts = expiry.split("-")
            exp_int = int(str(int(expiry_parts[0])-2000)+expiry_parts[1]+expiry_parts[2])
            if exp_int not in expiries[underlying_name]:
                continue
            option_type = row["OptnTp"]
            if option_type not in [OptionType.CE.name, OptionType.PE.name]:
                continue
            oi = int(row["OpnIntrst"])
            strike = float(row["StrkPric"])
            if underlying_name not in previous_oi:
                previous_oi[underlying_name] = {}
            if expiry not in previous_oi[underlying_name]:
                previous_oi[underlying_name][expiry] = {"CE": {}, "PE": {}}
            previous_oi[underlying_name][expiry][option_type][strike] = int(oi/lot_size)
    except:
        logger.debug(f"Error while loading bse bhavcopy", exc_info=True)
        logger.warning(f"Error while loading previous day oi from BSE bhavcopy")

    return


def get_today_oi_open(instrument_token):
    try:
        url = f"https://api.kite.trade/instruments/historical/{instrument_token}/minute"
        today = datetime.today().date()
        start_time = time(hour=9, minute=00)
        end_time = time(hour=15, minute=30)
        start_datetime = datetime.combine(today, start_time)
        end_datetime = datetime.combine(today, end_time)
        params = {
            "from": start_datetime,
            "to": end_datetime,
            "oi": 1
        }
        headers = {
            "Authorization" : f"token {Config.PRICEFEED_API_KEY}:{Config.PRICEFEED_ACCESS_TOKEN}"
        }
        for count in range(3):
            resp = requests.get(url=url, params=params, headers=headers)
            if resp.status_code != 200:
                error_json_data = resp.json()
                if "message" in error_json_data:
                    if error_json_data["message"] == "Too many requests":
                        logger.debug(f"Error: {resp.text} for going to retry, count: {count}")
                        sleep(1)
                        continue
                raise Exception(f"{resp.status_code}::{resp.text}")
            data = resp.json()["data"]
            flat_data = data["candles"]
            oi = 0
            if flat_data:
                tick_count = 0
                for tick in flat_data:
                    tick_count += 1
                    oi = tick[-1]
                    if oi != 0 or tick_count >= 3:
                        break
            return oi
    except:
        logger.debug(f"Error while fetching historical data for instrument {instrument_token}", exc_info=True)
        raise ValueError(f"Error while fetching historical data for instrument {instrument_token}")


def load_missing_oi_values():
    if not any(
        strategy.oi_type in [OiType.MAXCOI1, OiType.MAXCOI2, OiType.MAXCOI3]
        for strategy in user_strategies.values()
    ):
        return
    logger.info(f"Loading missing OI values")
    required_underlyings = []
    for strategy in user_strategies.values():
        if strategy.oi_type in [OiType.MAXCOI1, OiType.MAXCOI2, OiType.MAXCOI3]:
            required_underlyings.append(strategy.underlying)
    for instrument in instruments:
        if instrument.option_type not in [OptionType.CE, OptionType.PE]:
            continue
        if instrument.underlying not in required_underlyings:
            continue
        underlying_name = instrument.underlying.name
        expiry = instrument.expiry_date.strftime("%Y-%m-%d")
        if underlying_name not in previous_oi:
            previous_oi[underlying_name] = {}
        if expiry not in previous_oi[underlying_name]:
            previous_oi[underlying_name][expiry] = {"CE": {}, "PE": {}}
        if instrument.strike_price not in previous_oi[underlying_name][expiry][instrument.option_type.name]:
            oi = get_today_oi_open(instrument.pricefeed_token)
            previous_oi[underlying_name][expiry][instrument.option_type.name][instrument.strike_price] = int(oi/instrument.lot_size)