from commons.constants import (
    MARKET_END_TIME, 
    MARKET_START_TIME, 
    STRIKE_DIFF, 
    VERSION
)
from commons.enums import (
    OiType,
    PositionType,
    StrategyStatus
)
from commons.models import (
    Ohlc,
    Order,
    Position
)
from commons.utils import (
    get_current_time_int
)
import pricefeed
from pricefeed.utils import (
    get_previous_trading_day_bhavcopy, 
    load_instruments, 
    load_missing_oi_values
)
from engine.utils import (
    check_instance_restart,
    check_sqaureoff_instance,
    get_atm,
    get_instrument_by_token,
    load_strategies,
    get_max_oi_options
)
import brokers.zerodha
from brokers import (
    place_entry_order,
    place_exit_order
)
from data import user_strategies
from errors.system_defined import OptionChainNotFoundError
from config import Config
import logging
import time
import uuid


logger = logging.getLogger()


def setup():
    logger.info(VERSION)
    load_instruments()
    load_strategies()
    brokers.zerodha.login()
    get_previous_trading_day_bhavcopy()
    load_missing_oi_values()
    logger.info("Waiting for market to start...")
    while get_current_time_int() <= min(MARKET_START_TIME.values()):
        time.sleep(0.5)
        continue
    pricefeed.connect()

def run():
    time.sleep(5)
    ##small sleep to get all the oi values in pricefeed
    while True:
        if get_current_time_int() <= min(MARKET_START_TIME.values()) + 10:
            time.sleep(0.5)
            continue
        underlying_ohlc: Ohlc = pricefeed.get_quote_from_stream()
        if not underlying_ohlc:
            continue
        if underlying_ohlc.token not in [strategy.underlying_token for strategy in user_strategies.values()]:
            continue
        check_instance_restart(user_strategies)
        check_sqaureoff_instance(user_strategies)
        for strategy in user_strategies.values():
            try:
                if strategy.status not in [
                    StrategyStatus.ERROR,
                    StrategyStatus.SQUARED_OFF,
                    StrategyStatus.STOPPED
                ]:
                    underlying_instrument = get_instrument_by_token(strategy.underlying_token)
                    if get_current_time_int() <= MARKET_START_TIME[underlying_instrument.exchange.name]:
                        continue
                    if get_current_time_int() > MARKET_END_TIME[underlying_instrument.exchange.name]:
                        logger.info(f"Market time is over for instance no: {strategy.instance_id}")
                        strategy.status = StrategyStatus.STOPPED
                        continue
                    if strategy.status == StrategyStatus.CREATED and get_current_time_int() >= strategy.exit_time:
                        logger.info(f"Strategy exit time already passed.")
                        strategy.status = StrategyStatus.SQUARED_OFF
                        continue
                    ltp = pricefeed.get_quote(strategy.underlying_token)
                    atm = get_atm(ltp, STRIKE_DIFF[strategy.underlying.name])
                    try:
                        max_oi_call, max_oi_put = get_max_oi_options(
                            strategy.underlying, 
                            atm,
                            strategy.itm_count,
                            strategy.otm_count, 
                            strategy.oi_type, 
                            strategy.expiry_type,
                            strategy.oi_condition,
                            strategy.oi_condition_type
                        )
                    except OptionChainNotFoundError as ex:
                        current_time = time.time()
                        if strategy.last_retry_time and (current_time - strategy.last_retry_time) < 1:
                            continue
                        strategy.fetch_oi_retry_count += 1
                        strategy.last_retry_time = current_time
                        logger.info(f"Retrying to get option chain, count: {strategy.fetch_oi_retry_count}")
                        if strategy.fetch_oi_retry_count >= 3:
                            logger.error(f"Instance id: {strategy.instance_id}, {ex}")
                            strategy.status = StrategyStatus.ERROR
                        continue
                    for entry_time in strategy.entry_times.keys():
                        if get_current_time_int() >= entry_time:  
                            entry_qty = strategy.entry_times[entry_time]["qty"]
                            is_call_entered = strategy.entry_times[entry_time]["is_call_entered"]
                            is_put_entered = strategy.entry_times[entry_time]["is_put_entered"]
                            if max_oi_call and not is_call_entered:
                                if strategy.oi_type in [OiType.MAXOI1, OiType.MAXOI2, OiType.MAXOI3]:
                                    logger.info(f"Strategy start time, max_oi_call: {max_oi_call.instrument.trading_symbol}, call_oi: {max_oi_call.oi}")
                                else:    
                                    logger.info(f"Strategy start time, max_oi_call: {max_oi_call.instrument.trading_symbol}, call_oi: {max_oi_call.oi}, prev_oi_close: {max_oi_call.previous_oi_close}, coi: {max_oi_call.oi-max_oi_call.previous_oi_close}")
                                call_order = Order(
                                    strategy.id,
                                    strategy.instance_id,
                                    uuid.uuid4().hex,
                                    max_oi_call.instrument.pricefeed_token,
                                    Config.PRODUCT_TYPE,
                                    Config.ORDER_TYPE,
                                    PositionType.SELL,
                                    entry_qty,
                                    strategy.order_strike_diff
                                )
                                if not strategy.runnning_call:
                                    strategy.runnning_call = Position(call_order.id, call_order.instrument_id)
                                portfolio_names = place_entry_order(call_order)
                                call_order.portfolio_names = portfolio_names
                                strategy.runnning_call.orders.append(call_order)
                                strategy.running_call_qty += entry_qty
                                logger.info(f"Strategy running call qty: {strategy.running_call_qty}")
                                strategy.entry_times[entry_time]["is_call_entered"] = True
                                strategy.last_call_shift_timestamp = time.time()
                                strategy.status = StrategyStatus.RUNNING
                            if max_oi_put and not is_put_entered:
                                if strategy.oi_type in [OiType.MAXOI1, OiType.MAXOI2, OiType.MAXOI3]:
                                    logger.info(f"Strategy start time, max_oi_put: {max_oi_put.instrument.trading_symbol}, put_oi: {max_oi_put.oi}")
                                else:
                                    logger.info(f"Strategy start time, max_oi_put: {max_oi_put.instrument.trading_symbol}, put_oi: {max_oi_put.oi}, prev_oi_close: {max_oi_put.previous_oi_close}, coi: {max_oi_put.oi-max_oi_put.previous_oi_close}")
                                put_order = Order(
                                    strategy.id,
                                    strategy.instance_id,
                                    uuid.uuid4().hex,
                                    max_oi_put.instrument.pricefeed_token,
                                    Config.PRODUCT_TYPE,
                                    Config.ORDER_TYPE,
                                    PositionType.SELL,
                                    entry_qty,
                                    strategy.order_strike_diff
                                )
                                if not strategy.runnning_put:
                                    strategy.runnning_put = Position(put_order.id, put_order.instrument_id)
                                portfolio_names = place_entry_order(put_order)
                                put_order.portfolio_names = portfolio_names
                                strategy.runnning_put.orders.append(put_order)
                                strategy.running_put_qty += entry_qty
                                logger.info(f"Strategy running qty: {strategy.running_put_qty}")
                                strategy.entry_times[entry_time]["is_put_entered"] = True
                                strategy.last_put_shift_timestamp = time.time()
                                strategy.status = StrategyStatus.RUNNING
                    remove_times = []
                    for entry_time, entry_data in strategy.entry_times.items():
                        if entry_data["is_call_entered"] and entry_data["is_put_entered"]:
                            remove_times.append(entry_time)
                    for entry_time in remove_times:
                        del strategy.entry_times[entry_time]
                    if strategy.status == StrategyStatus.RUNNING:
                        if (get_current_time_int() >= strategy.exit_time or strategy.manual_sq_off) and (not strategy.is_shifting_call or not strategy.is_shifting_put):
                            if strategy.manual_sq_off:
                                logger.info(f"Strategy Manual Sq off received")
                            else:
                                logger.info(f"Strategy Sq off time hit")
                            for call_order in strategy.runnning_call.orders:
                                call_sq_off_order = Order(
                                    strategy.id,
                                    strategy.instance_id,
                                    uuid.uuid4().hex,
                                    strategy.runnning_call.instrument_id,
                                    Config.PRODUCT_TYPE,
                                    Config.ORDER_TYPE,
                                    PositionType.BUY,
                                    call_order.quantity,
                                    strategy.order_strike_diff
                                )
                                call_sq_off_order.portfolio_names = call_order.portfolio_names
                                place_exit_order(call_sq_off_order)
                            for put_order in strategy.runnning_put.orders:
                                put_sq_off_order = Order(
                                    strategy.id,
                                    strategy.instance_id,
                                    uuid.uuid4().hex,
                                    strategy.runnning_put.instrument_id,
                                    Config.PRODUCT_TYPE,
                                    Config.ORDER_TYPE,
                                    PositionType.BUY,
                                    put_order.quantity,
                                    strategy.order_strike_diff
                                )
                                put_sq_off_order.portfolio_names = put_order.portfolio_names
                                place_exit_order(put_sq_off_order)
                            strategy.status = StrategyStatus.SQUARED_OFF
                            break

                        if (max_oi_call) and (not strategy.is_shifting_call) and (max_oi_call.instrument.pricefeed_token != strategy.runnning_call.instrument_id) and (time.time() - strategy.last_call_shift_timestamp >= Config.ATM_CHANGE_DELAY):
                            squared_call_instrument = get_instrument_by_token(
                                strategy.runnning_call.instrument_id
                            )
                            if strategy.oi_type in [OiType.MAXOI1, OiType.MAXOI2, OiType.MAXOI3]:
                                logger.info(f"Shift in Call, old_call: {squared_call_instrument.trading_symbol}, new_call: {max_oi_call.instrument.trading_symbol}, call_oi: {max_oi_call.oi}")
                            else:
                                logger.info(f"Shift in Call, old_call: {squared_call_instrument.trading_symbol}, new_call: {max_oi_call.instrument.trading_symbol}, call_oi: {max_oi_call.oi}, prev_oi_close: {max_oi_call.previous_oi_close}, coi: {max_oi_call.oi-max_oi_call.previous_oi_close}")
                            for call_order in strategy.runnning_call.orders:
                                call_sq_off_order = Order(
                                    strategy.id,
                                    strategy.instance_id,
                                    uuid.uuid4().hex,
                                    strategy.runnning_call.instrument_id,
                                    Config.PRODUCT_TYPE,
                                    Config.ORDER_TYPE,
                                    PositionType.BUY,
                                    call_order.quantity,
                                    strategy.order_strike_diff
                                )
                                call_sq_off_order.portfolio_names = call_order.portfolio_names
                                place_exit_order(call_sq_off_order)
                            strategy.runnning_call.orders = []
                            strategy.call_shift_trigger_timestamp = time.time()
                            strategy.is_shifting_call = True
                            strategy.next_call_instrument = max_oi_call.instrument
                        if (max_oi_put) and (not strategy.is_shifting_put) and (max_oi_put.instrument.pricefeed_token != strategy.runnning_put.instrument_id) and (time.time() - strategy.last_put_shift_timestamp >= Config.ATM_CHANGE_DELAY):
                            squared_put_instrument = get_instrument_by_token(
                                strategy.runnning_put.instrument_id
                            )
                            if strategy.oi_type in [OiType.MAXOI1, OiType.MAXOI2, OiType.MAXOI3]: 
                                logger.info(f"Shift in Put, old_put: {squared_put_instrument.trading_symbol}, new_put: {max_oi_put.instrument.trading_symbol}, put_oi: {max_oi_put.oi}")
                            else:
                                logger.info(f"Shift in Put, old_put: {squared_put_instrument.trading_symbol}, new_put: {max_oi_put.instrument.trading_symbol}, put_oi: {max_oi_put.oi}, prev_oi_close: {max_oi_put.previous_oi_close}, coi: {max_oi_put.oi-max_oi_put.previous_oi_close}")
                            for put_order in strategy.runnning_put.orders:
                                put_sq_off_order = Order(
                                    strategy.id,
                                    strategy.instance_id,
                                    uuid.uuid4().hex,
                                    strategy.runnning_put.instrument_id,
                                    Config.PRODUCT_TYPE,
                                    Config.ORDER_TYPE,
                                    PositionType.BUY,
                                    put_order.quantity,
                                    strategy.order_strike_diff
                                )
                                put_sq_off_order.portfolio_names = put_order.portfolio_names
                                place_exit_order(put_sq_off_order)
                            strategy.runnning_put.orders = []
                            strategy.put_shift_trigger_timestamp = time.time()
                            strategy.is_shifting_put = True
                            strategy.next_put_instrument = max_oi_put.instrument
                        if strategy.is_shifting_call:
                            if time.time() - strategy.call_shift_trigger_timestamp >= strategy.shift_delay:
                                new_call_order = Order(
                                    strategy.id,
                                    strategy.instance_id,
                                    uuid.uuid4().hex,
                                    strategy.next_call_instrument.pricefeed_token,
                                    Config.PRODUCT_TYPE,
                                    Config.ORDER_TYPE,
                                    PositionType.SELL,
                                    strategy.running_call_qty,
                                    strategy.order_strike_diff
                                )
                                strategy.runnning_call = Position(new_call_order.id, new_call_order.instrument_id)
                                portfolio_names = place_entry_order(new_call_order)
                                new_call_order.portfolio_names = portfolio_names
                                strategy.runnning_call.orders.append(new_call_order)
                                strategy.next_call_instrument = None
                                strategy.is_shifting_call = False
                                strategy.call_shift_trigger_timestamp = None
                                strategy.last_call_shift_timestamp = time.time()
                        if strategy.is_shifting_put:
                            if time.time() - strategy.put_shift_trigger_timestamp >= strategy.shift_delay:
                                new_put_order = Order(
                                    strategy.id,
                                    strategy.instance_id,
                                    uuid.uuid4().hex,
                                    strategy.next_put_instrument.pricefeed_token,
                                    Config.PRODUCT_TYPE,
                                    Config.ORDER_TYPE,
                                    PositionType.SELL,
                                    strategy.running_put_qty,
                                    strategy.order_strike_diff
                                )
                                portfolio_names = place_entry_order(new_put_order)
                                new_put_order.portfolio_names = portfolio_names
                                strategy.runnning_put = Position(new_put_order.id, new_put_order.instrument_id)
                                strategy.runnning_put.orders.append(new_put_order)
                                strategy.next_put_instrument = None
                                strategy.is_shifting_put = False
                                strategy.put_shift_trigger_timestamp = None
                                strategy.last_put_shift_timestamp = time.time()
            except Exception as ex:
                logger.error(f"Instance: {strategy.instance_id} got into error: {ex}")
                logger.debug(f"Instance: {strategy.instance_id} got into error.", exc_info=True)
                strategy.status = StrategyStatus.ERROR

        if all(
            strategy.status in [
                StrategyStatus.ERROR,
                StrategyStatus.SQUARED_OFF,
                StrategyStatus.STOPPED
            ] 
            for strategy in user_strategies.values()
        ):
            logger.info(f"All the strategies are squared off.")
            return