from datetime import datetime
import json
import logging
from typing import List, Tu<PERSON>, Dict 
import uuid
from commons.constants import LOT_SIZE, RESTART_INSTANCE_FILE_PATH, SQUAREOFF_INSTANCE_FILE_PATH, STRIKE_DIFF
from commons.enums import (
    ExchangeType,
    ExpiryType,
    InstrumentType,
    OiType,
    Underlying,
    OptionType,
    OiCriteria
)
from commons.models import Instrument, OpenInterest, Strategy
from commons.utils import generate_trading_symbol, round_to
from data.models import Cache
from errors.system_defined import NotSupportedError, OptionChainNotFoundError
import pricefeed
from data import user_strategies
from config import Config

logger = logging.getLogger(__name__)

def get_atm(ltp: float, strike_diff: int):
    return round_to(ltp, strike_diff)


def get_pricefeed_token(instrument_token):
    return Cache().pull(instrument_token).pricefeed_token


def load_strategies() -> Dict[str, Strategy]:
    restart_instance_state = {}
    squareoff_instance_state = {}
    for instance_id in Config.INSTANCES:
        instance_id = str(instance_id)
        restart_instance_state[instance_id] = "false"
        squareoff_instance_state[instance_id] = "false"
        day_qty = Config.DAY_QTYS[instance_id]
        entry_times = Config.ENTRY_TIMES[instance_id]
        index_name = Config.INDEX_NAMES[instance_id]
        if index_name not in [und.name for und in Underlying]:
            raise ValueError(f"{index_name} index is not supported")
        expiry_type = Config.EXPIRY_TYPES[instance_id]
        itm_count = Config.NO_OF_ITM[instance_id]
        otm_count = Config.NO_OF_OTM[instance_id]
        if expiry_type == "CURRENT":
            expiry_type = ExpiryType.WEEKLY
        elif expiry_type == "NEXT":
            expiry_type = ExpiryType.NEXTWEEKLY
        else:
            raise NotSupportedError(f"Expiry-type: {expiry_type} is not supported")
        oi_type = Config.OI_TYPES[instance_id]
        order_strike_diff = 0
        if "+" in oi_type:
            parts = oi_type.split("+")
            oi_type = parts[0]
            order_strike_diff = int(parts[-1])*STRIKE_DIFF[index_name]
        if "-" in oi_type:
            parts = oi_type.split("-")
            oi_type = parts[0]
            order_strike_diff = int(parts[-1])*-1*STRIKE_DIFF[index_name]
        qty_per_entry_time = {}
        if int(day_qty)%len(entry_times) != 0:
            raise TypeError(f"Instance: {instance_id}, Day qty: {day_qty} is not in multiple of instances")
        entry_qty = int(day_qty)//len(entry_times)
        if entry_qty%LOT_SIZE[index_name] != 0:
            raise TypeError(f"Instance: {instance_id}, Instance qty: {entry_qty} is not in multiple of lot size")
        for entry_time in entry_times:
            if entry_time >= int(Config.EXIT_TIME):
                raise TypeError(f"Instance: {instance_id}, Entry time: {entry_time} is greater than Exit time: {int(Config.EXIT_TIME)}")
            qty_per_entry_time[entry_time] = {
                "qty": entry_qty,
                "is_call_entered": False,
                "is_put_entered": False
            }
        oi_condition_data = Config.OI_CONDITION.get(instance_id)
        oi_condition = None
        oi_condition_type = OiCriteria.NONE
        if oi_condition_data:
            oi_condition = f"{oi_condition_data["condition"]} {oi_condition_data["value"]}"
            oi_condition_type = OiCriteria(oi_condition_data["type"])
        strategy = Strategy(
            uuid.uuid4().hex,
            instance_id,
            Underlying(index_name),
            expiry_type,
            OiType(oi_type),
            itm_count,
            otm_count,
            qty_per_entry_time,
            int(Config.EXIT_TIME),
            int(day_qty),
            int(Config.SHIFT_DELAY),
            order_strike_diff,
            oi_condition,
            oi_condition_type
        )
        strategy.underlying_token = get_pricefeed_token(
            strategy.underlying.name
        )
        logger.info(strategy)
        user_strategies[instance_id] = strategy

    with open(RESTART_INSTANCE_FILE_PATH, "w") as json_file:
        json.dump(restart_instance_state, json_file)
    with open(SQUAREOFF_INSTANCE_FILE_PATH, "w") as json_file:
        json.dump(squareoff_instance_state, json_file)
        
    return user_strategies


def get_max_oi_options(
        underlying: Underlying, 
        atm: int, 
        itm_count: int,
        otm_count: int, 
        oi_type: OiType, 
        expiry_type: ExpiryType, 
        oi_condition: str, 
        oi_condition_type: OiCriteria
    ):
    selected_call = None
    selected_put = None
    strike_diff = STRIKE_DIFF[underlying.name]

    max_call_strike = atm + otm_count*strike_diff
    min_call_strike = atm - itm_count*strike_diff
    max_put_strike = atm + itm_count*strike_diff
    min_put_strike = atm - otm_count*strike_diff

    if oi_type.name in ["MAXCOI1", "MAXOI1"]:
        oi_index = 1
    elif oi_type.name in ["MAXCOI2", "MAXOI2"]:
        oi_index = 2
    elif oi_type.name in ["MAXCOI3", "MAXOI3"]:
        oi_index = 3

    expiry_int = Cache().pull(f"{underlying.name}_{expiry_type.name}")
    expiry = datetime.strptime(
        str(expiry_int),
        "%y%m%d",
    ).strftime("%Y-%m-%d")

    try:
        call_oi_dict: Dict[Instrument, int] = pricefeed.oi_dict[underlying.name][expiry][OptionType.CE.name]
        put_oi_dict: Dict[Instrument, int] = pricefeed.oi_dict[underlying.name][expiry][OptionType.PE.name] 
    except KeyError as ex:
        raise OptionChainNotFoundError(f"Option Chain not found for expiry={expiry}")
    
    if oi_type.name in ["MAXCOI1", "MAXCOI2", "MAXCOI3"]:
        prev_calls_oi =  pricefeed.previous_oi[underlying.name][expiry][OptionType.CE.name]
        prev_puts_oi =  pricefeed.previous_oi[underlying.name][expiry][OptionType.PE.name]
    else:
        prev_call_oi = None
        prev_put_oi = None

    required_call_dict = {
        option: oi
        for option, oi in list(call_oi_dict.items())
        if min_call_strike <= option.strike_price <= max_call_strike
    }
    required_put_dict = {
        option: oi
        for option, oi in list(put_oi_dict.items())
        if min_put_strike <= option.strike_price <= max_put_strike
    }

    if oi_type.name in ["MAXCOI1", "MAXCOI2", "MAXCOI3"]:
        call_oi_diffs = {}
        put_oi_diffs = {}
        for call_inst, current_call_oi in required_call_dict.items():
            prev_call_oi = prev_calls_oi[call_inst.strike_price]
            diff = current_call_oi - prev_call_oi
            call_oi_diffs[call_inst] = diff
        for put_inst, current_put_oi,  in required_put_dict.items():
            prev_put_oi = prev_puts_oi[put_inst.strike_price]
            diff = current_put_oi - prev_put_oi
            put_oi_diffs[put_inst] = diff

        sorted_calls = sorted(call_oi_diffs.items(), key=lambda x: x[1], reverse=True)
        sorted_puts = sorted(put_oi_diffs.items(), key=lambda x: x[1], reverse=True)

        call_instrument, call_oi_diff = sorted_calls[oi_index - 1]
        call_oi = required_call_dict[call_instrument]
        previous_call_oi = prev_calls_oi[call_instrument.strike_price]
        put_instrument, put_oi_diff = sorted_puts[oi_index - 1]
        put_oi = required_put_dict[put_instrument]
        previous_put_oi = prev_puts_oi[put_instrument.strike_price]

        selected_call = OpenInterest(call_instrument, call_oi, previous_call_oi)
        selected_put = OpenInterest(put_instrument, put_oi, previous_put_oi) 

    elif oi_type.name in ["MAXOI1", "MAXOI2", "MAXOI3"]:
        sorted_calls: List[Tuple[Instrument, int]] = sorted(required_call_dict.items(), key=lambda x: x[1], reverse=True)
        sorted_puts: List[Tuple[Instrument, int]] = sorted(required_put_dict.items(), key=lambda x: x[1], reverse=True)
        if len(sorted_calls) == 0 or len(sorted_puts) == 0:
            return selected_call, selected_put
        call_instrument, call_oi_value = sorted_calls[oi_index - 1]
        put_instrument, put_oi_value = sorted_puts[oi_index - 1]
        if oi_condition:
            if oi_condition_type == OiCriteria.ON_STRIKE:
                for call_inst, call_oi_val in sorted_calls:
                    if eval(f"{call_oi_val} {oi_condition}"):
                        call_idx = sorted_calls.index((call_inst, call_oi_val))
                        call_instrument, call_oi_value = sorted_calls[call_idx + oi_index - 1]
                        selected_call = OpenInterest(call_instrument, call_oi_value, None)
                        break
                for put_inst, put_oi_val in sorted_puts:
                    if eval(f"{put_oi_val} {oi_condition}"):
                        put_idx = sorted_puts.index((put_inst, put_oi_val))
                        put_instrument, put_oi_value = sorted_puts[put_idx + oi_index - 1]
                        selected_put = OpenInterest(put_instrument, put_oi_value, None)
                        break
            elif oi_condition_type == OiCriteria.ON_CUMULATIVE:
                call_cumulative_oi = sum(required_call_dict.values())
                put_cumuative_oi = sum(required_put_dict.values())
                if eval(f"{call_cumulative_oi} {oi_condition}"):
                    selected_call = OpenInterest(call_instrument, call_oi_value, None)
                if eval(f"{put_cumuative_oi} {oi_condition}"):
                    selected_put = OpenInterest(put_instrument, put_oi_value, None)
        else:
            selected_call = OpenInterest(call_instrument, call_oi_value, None)
            selected_put = OpenInterest(put_instrument, put_oi_value, None)

    return selected_call, selected_put 


def get_instrument_by_token(instrument_token) -> Instrument:
    instrument_data = Cache().pull(instrument_token)
    return instrument_data


def get_instrument(
    underlying: Underlying,
    instrument_type: InstrumentType,
    expiry_int: int,
    strike_price: float,
    option_type: OptionType,
) -> Instrument:
    exchange = ExchangeType.NFO
    if underlying in [Underlying.SENSEX, Underlying.BANKEX]:
        exchange = ExchangeType.BFO
    if underlying in [Underlying.CRUDEOIL, Underlying.CRUDEOILM]:
        exchange = ExchangeType.MCX
    return Cache().pull(
        generate_trading_symbol(
            exchange.name,
            underlying.name,
            instrument_type.name,
            expiry_int,
            strike_price,
            option_type.name,
        )
    )


def check_instance_restart(user_strategies: Dict[str, Strategy]):
    update_file = False
    try:
        with open(RESTART_INSTANCE_FILE_PATH, "r") as file:
            restart_instances: Dict = json.load(file)
    except Exception as ex: 
        logger.info(f"Error in reading {RESTART_INSTANCE_FILE_PATH} :: Try again to restart the instance, {ex}")
        logger.debug(f"Error in reading {RESTART_INSTANCE_FILE_PATH}", exc_info=True)
        with open(RESTART_INSTANCE_FILE_PATH, "w") as file:
            restart_instances = {strategy.instance_id: "false" for strategy in user_strategies.values()}
            json.dump(restart_instances, file)
        return
    for instance_id in restart_instances.keys():
        if restart_instances[instance_id].lower() == "true":
            update_file = True
            restart_instances[instance_id] = "false"
            day_qty = Config.DAY_QTYS[instance_id]
            entry_times = Config.ENTRY_TIMES[instance_id]
            index_name = Config.INDEX_NAMES[instance_id]
            expiry_type = Config.EXPIRY_TYPES[instance_id]
            itm_count = Config.NO_OF_ITM[instance_id]
            otm_count = Config.NO_OF_OTM[instance_id]
            if expiry_type == "CURRENT":
                expiry_type = ExpiryType.WEEKLY
            elif expiry_type == "NEXT":
                expiry_type = ExpiryType.NEXTWEEKLY
            else:
                raise NotSupportedError(f"Expiry-type: {expiry_type} is not supported")
            oi_type = Config.OI_TYPES[instance_id]
            order_strike_diff = 0
            if "+" in oi_type:
                parts = oi_type.split("+")
                oi_type = parts[0]
                order_strike_diff = int(parts[-1])*STRIKE_DIFF[index_name]
            if "-" in oi_type:
                parts = oi_type.split("-")
                oi_type = parts[0]
                order_strike_diff = int(parts[-1])*-1*STRIKE_DIFF[index_name]
            qty_per_entry_time = {}
            if int(day_qty)%len(entry_times) != 0:
                raise TypeError(f"Instance: {instance_id}, Day qty: {day_qty} is not in multiple of instances")
            entry_qty = int(day_qty)//len(entry_times)
            if entry_qty%LOT_SIZE[index_name] != 0:
                raise TypeError(f"Instance: {instance_id}, Instance qty: {entry_qty} is not in multiple of lot size")
            for entry_time in entry_times:
                if entry_time >= int(Config.EXIT_TIME):
                    raise TypeError(f"Instance: {instance_id}, Entry time: {entry_time} is greater than Exit time: {int(Config.EXIT_TIME)}")
                qty_per_entry_time[entry_time] = {
                    "qty": entry_qty,
                    "is_call_entered": False,
                    "is_put_entered": False
                }
            oi_condition_data = Config.OI_CONDITION.get(instance_id)
            oi_condition = None
            oi_condition_type = OiCriteria.NONE
            if oi_condition_data:
                oi_condition = f"{oi_condition_data["condition"]} {oi_condition_data["value"]}"
                oi_condition_type = OiCriteria(oi_condition_data["type"])
            strategy = Strategy(
                uuid.uuid4().hex,
                instance_id,
                Underlying(index_name),
                expiry_type,
                OiType(oi_type),
                itm_count,
                otm_count,
                qty_per_entry_time,
                int(Config.EXIT_TIME),
                int(day_qty),
                int(Config.SHIFT_DELAY),
                order_strike_diff,
                oi_condition,
                oi_condition_type
            )
            strategy.underlying_token = get_pricefeed_token(
                strategy.underlying.name
            )
            user_strategies[instance_id] = strategy
            logger.info(strategy)
            logger.info(f"----------------- Instance: {instance_id} Restarted -----------------")

    if update_file:
        with open(RESTART_INSTANCE_FILE_PATH, "w") as file:
            json.dump(restart_instances, file)


def check_sqaureoff_instance(user_strategies: Dict[str, Strategy]):
    update_file = False
    try:
        with open(SQUAREOFF_INSTANCE_FILE_PATH, "r") as file:
            squareoff_instance: Dict = json.load(file)
    except Exception as ex: 
        logger.info(f"Error in reading {SQUAREOFF_INSTANCE_FILE_PATH} :: Try again to square off the instance, {ex}")
        logger.debug(f"Error in reading {SQUAREOFF_INSTANCE_FILE_PATH}", exc_info=True)
        with open(SQUAREOFF_INSTANCE_FILE_PATH, "w") as file:
            squareoff_instance = {strategy.instance_id: "false" for strategy in user_strategies.values()}
            json.dump(squareoff_instance, file)
        return

    for instance_id in squareoff_instance.keys():
        if squareoff_instance[instance_id].lower() == "true":
            user_strategies[instance_id].manual_sq_off = True
            update_file = True
            squareoff_instance[instance_id] = "false"
            logger.info(f"----------------- Instance: {instance_id} Manual Sq off received -----------------")
            
    
    if update_file:
        with open(SQUAREOFF_INSTANCE_FILE_PATH, "w") as file:
            json.dump(squareoff_instance, file)