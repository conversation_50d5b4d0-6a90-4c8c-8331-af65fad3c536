import os
import json
from dotenv import load_dotenv
from commons.enums import ProductType, OrderType

load_dotenv(".env")
            

class Config:
    PRICEFEED_CLIENT_ID = os.environ.get("PRICEFEED_CLIENT_ID")
    PRICEFEED_CLIENT_PASSWORD = os.environ.get("PRICEFEED_CLIENT_PASSWORD")
    PRICEFEED_TOTP_SECRET = os.environ.get("PRICEFEED_TOTP_SECRET")
    PRICEFEED_API_KEY = os.environ.get("PRICEFEED_API_KEY")
    PRICEFEED_API_SECRET = os.environ.get("PRICEFEED_API_SECRET")
    PRICEFEED_ACCESS_TOKEN = os.environ.get("PRICEFEED_ACCESS_TOKEN")
    IS_DEALER_API = eval(os.environ.get("IS_DEALER_API"))
    PRODUCT_TYPE = ProductType(os.environ.get("PRODUCT_TYPE"))
    ORDER_TYPE = OrderType(os.environ.get("ORDER_TYPE"))
    API_KEY = os.environ.get("API_KEY")
    API_SECRET = os.environ.get("API_SECRET")
    ACCESS_TOKEN = os.environ.get("ACCESS_TOKEN")
    EXTERNAL_REQUEST_TIMEOUT = 10

    INSTANCES = json.loads(os.environ.get("INSTANCES"))
    NO_OF_INSTANCES = len(INSTANCES)
    INDEX_NAMES = json.loads(os.environ.get("INDEX_NAMES"))
    EXPIRY_TYPES = json.loads(os.environ.get("EXPIRY_TYPES"))
    OI_TYPES = json.loads(os.environ.get("OI_TYPES"))
    NO_OF_ITM = json.loads(os.environ.get("NO_OF_ITM"))
    NO_OF_OTM = json.loads(os.environ.get("NO_OF_OTM"))
    DAY_QTYS = json.loads(os.environ.get("DAY_QTYS"))
    ENTRY_TIMES = json.loads(os.environ.get("ENTRY_TIMES"))
    OPTION_PORTFOLIO_NAMES = json.loads(os.environ.get("OPTION_PORTFOLIO_NAMES"))
    STRATEGY_TAGS = json.loads(os.environ.get("STRATEGY_TAGS"))
    PORTS = json.loads(os.environ.get("PORTS", '{"1":"1"}'))
    BASE_URLS = json.loads(os.environ.get("BASE_URLS"))
    EXIT_TIME = os.environ.get("EXIT_TIME")
    SHIFT_DELAY = os.environ.get("SHIFT_DELAY")
    ATM_CHANGE_DELAY = float(os.environ.get("ATM_CHANGE_DELAY"))
    OI_CONDITION = json.loads(os.environ.get("OI_CONDITION"))
    BROKER = os.environ.get("BROKER")

    REQUSET_SLEEP_TIME = float(os.environ.get("REQUSET_SLEEP_TIME"))
    RETRY_COUNT = float(os.environ.get("RETRY_COUNT"))
    RETRY_SLEEP_TIME = float(os.environ.get("RETRY_SLEEP_TIME"))

    ALERT_BOT_TOKEN = os.environ.get("ALERT_BOT_TOKEN", "")
    MESSAGE_BOT_TOKEN = os.environ.get("MESSAGE_BOT_TOKEN", "")
    CHAT_ID =  os.environ.get("CHAT_ID")