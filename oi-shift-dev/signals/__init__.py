import logging
import json
from datetime import datetime
import requests

from config import Config

logger = logging.getLogger(__name__)

log_file = f"/logs/signals_{datetime.now().strftime("%Y%m%d_%H%M%S")}.txt"
f = open(log_file, 'w')
f.close()

def add_new(msg):
    logger.debug(msg)
    signal_msg = f"{datetime.now()}:: {msg}"
    log = json.dumps(signal_msg)
    with open(log_file, 'a') as f:
        f.write(log + "\n")


def send_message(message: str):
    if len(Config.MESSAGE_BOT_TOKEN) == 0:
        return
    try:
        chat_id = Config.CHAT_ID
        url = f"https://api.telegram.org/bot{Config.MESSAGE_BOT_TOKEN}/sendMessage"
        payload = {
            "chat_id": chat_id,
            "text": message
        }
        response = requests.post(url, data=payload)
        if response.status_code != 200:
            raise Exception(f"Error in sending message:: {message}. Response: {response.text}")
    except:
        logger.debug(f"Error sending telegram message", exc_info=True)
        


def send_alert(message: str):
    if len(Config.ALERT_BOT_TOKEN) == 0:
        return
    try:
        chat_id = Config.CHAT_ID
        url = f"https://api.telegram.org/bot{Config.ALERT_BOT_TOKEN}/sendMessage"
        payload = {
            "chat_id": chat_id,
            "text": message
        }
        response = requests.post(url, data=payload)
        if response.status_code != 200:
            raise Exception(f"Error in sending message:: {message}. Response: {response.text}")
    except:
        logger.debug(f"Error sending telegram message", exc_info=True)