import logging
from config import Config
from commons.models import Order
from commons.enums import BrokerType
from errors.system_defined import NotSupportedError
from brokers import algobaba, own_platform
from brokers.utils import slice_order

logger = logging.getLogger(__name__)

def place_entry_order(order: Order):
    if Config.BROKER == BrokerType.ALGOBABA_HTTP.name:
        return algobaba.place_entry_order(order)
    elif Config.BROKER == BrokerType.OWN_PLATFORM.name:
        return own_platform.place_entry_order(order)
    else:
        raise NotSupportedError(f"{Config.BROKER} is not supported")

def place_exit_order(order: Order):
    if Config.BROKER == BrokerType.ALGOBABA_HTTP.name:
        algobaba.place_exit_order(order)
    elif Config.BROKER == BrokerType.OWN_PLATFORM.name:
        own_platform.place_exit_order(order)
    else:
        raise NotSupportedError(f"{Config.BROKER} is not supported")