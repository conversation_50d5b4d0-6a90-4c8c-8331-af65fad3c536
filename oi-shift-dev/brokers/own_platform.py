import json
import logging

import requests
from commons.constants import LOT_SIZE
from commons.enums import OptionType
from commons.models import Order
from config import Config
from engine.utils import get_instrument_by_token, get_instrument
import signals


logger = logging.getLogger(__name__)

## [{"SYMBOL": "NIFTY", "TYPE": "ENTRY", "LOTS": "2", "Port": "stag_1_port_1","STAG": "test_strategy_1", "TOKEN":25265}]

error_list = set()
last_hit_time = {}

def place_entry_order(order: Order):
    instrument = get_instrument_by_token(order.instrument_id)
    expiry_int = int(instrument.expiry_date.date().strftime("%y%m%d"))
    lots = int(order.quantity/LOT_SIZE[instrument.underlying.name])
    strategy_id = order.strategy_id
    instance_id = order.instance_id
    instance_strategy_tag = Config.STRATEGY_TAGS[instance_id]
    instance_port = Config.PORTS[instance_id]
    order_type = "ENTRY"
    if instrument.option_type == OptionType.CE:
        strike_price = instrument.strike_price + order.order_strike_diff
    elif instrument.option_type == OptionType.PE:
        strike_price = instrument.strike_price - order.order_strike_diff
    order_instrument = get_instrument(
        instrument.underlying,
        instrument.instrument_type,
        expiry_int,
        strike_price,
        instrument.option_type
    )
    for base_url in Config.BASE_URLS:
        try:
            if base_url in error_list:
                continue
            payload = [{
                "SYMBOL": order_instrument.underlying.name,
                "TYPE": order_type,
                "LOTS": lots,
                "Port": instance_port,
                "STAG": instance_strategy_tag,
                "TOKEN": order_instrument.exchange_token
            }]
            logger.debug(payload)
            signals.add_new(f"{base_url}::{payload}")
            signals.send_message(f"Entry order for {order_instrument.trading_symbol} with order_id={order.id}")
            resp = requests.post(url=base_url, data=json.dumps(payload))
            signals.add_new(f"{resp.status_code}::{resp.text}")
            if resp.status_code != 200:
                raise ValueError(f"Error in broker with response, {resp.status_code}::{resp.text}")  
            logger.debug(f"url: {base_url}, stg-id: {strategy_id}, instance-id: {instance_id}, instrument: {instrument.trading_symbol}, qty: {order.quantity} entry order")
            logger.info(f"Place entry order for Instrument(strike={strike_price}, option_type={instrument.option_type.name}) with order_id={order.id}")
        except Exception as ex:
            signals.send_alert(f"Error in url: {base_url}, error: {ex}")
            logger.error(f"Error in entry order, {ex}")
            logger.debug(f"Error in entry order", exc_info=True)
            error_list.add(base_url)
            signals.add_new(f"Error - {base_url}::{ex}")


def place_exit_order(order: Order):
    instrument = get_instrument_by_token(order.instrument_id)
    expiry_int = int(instrument.expiry_date.date().strftime("%y%m%d"))
    lots = int(order.quantity/LOT_SIZE[instrument.underlying.name])
    strategy_id = order.strategy_id
    instance_id = order.instance_id
    instance_strategy_tag = Config.STRATEGY_TAGS[instance_id]
    instance_port = Config.PORTS[instance_id]
    order_type = "EXIT"
    if instrument.option_type == OptionType.CE:
        strike_price = instrument.strike_price + order.order_strike_diff
    elif instrument.option_type == OptionType.PE:
        strike_price = instrument.strike_price - order.order_strike_diff
    order_instrument = get_instrument(
        instrument.underlying,
        instrument.instrument_type,
        expiry_int,
        strike_price,
        instrument.option_type
    )
    for base_url in Config.BASE_URLS:
        try:
            if base_url in error_list:
                continue
            payload = [{
                "SYMBOL": order_instrument.underlying.name,
                "TYPE": order_type,
                "LOTS": lots,
                "Port": instance_port,
                "STAG": instance_strategy_tag,
                "TOKEN": order_instrument.exchange_token
            }]
            logger.debug(payload)
            signals.add_new(f"{base_url}::{payload}")
            signals.send_message(f"Exit for {order_instrument.trading_symbol} with order_id={order.id}")
            resp = requests.post(url=base_url, data=json.dumps(payload))
            signals.add_new(f"{resp.status_code}::{resp.text}")
            if resp.status_code != 200:
                raise ValueError(f"Error in broker with response, {resp.status_code}::{resp.text}")  
            logger.debug(f"url: {base_url}, stg-id: {strategy_id}, instance-id: {instance_id}, instrument: {instrument.trading_symbol}, qty: {order.quantity}, exit-order")
            logger.info(f"Place exit order for Instrument(strike={strike_price}, option_type={instrument.option_type.name}) with order_id={order.id}")
        except Exception as ex:
            signals.send_alert(f"Error in url: {base_url}, error: {ex}")
            logger.error(f"Error in exit order, {ex}")
            logger.debug(f"Error in exit order", exc_info=True)
            error_list.add(base_url)
            signals.add_new(f"Error - {base_url}::{ex}")