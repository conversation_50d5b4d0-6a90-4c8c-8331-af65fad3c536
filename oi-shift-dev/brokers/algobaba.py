import json
import random
import time
import requests
from requests.exceptions import ConnectTimeout, ReadTimeout
from brokers.utils import get_expiry_code
from commons.enums import OptionType
from commons.models import Order
from commons.constants import LOT_SIZE
from config import Config
from engine.utils import get_instrument_by_token
import logging
from errors.system_defined import BrokerError
import signals

logger = logging.getLogger(__name__)

error_list = set()
last_hit_time = {}

def place_entry_order(order: Order):
    instrument = get_instrument_by_token(order.instrument_id)
    underlying_name = instrument.underlying.name
    expiry = instrument.expiry_date
    expiry_code = get_expiry_code(underlying_name, expiry)
    strategy_id = order.strategy_id
    instance_id = order.instance_id
    lots = int(order.quantity/LOT_SIZE[instrument.underlying.name])
    instance_portfolio_name = Config.OPTION_PORTFOLIO_NAMES[instance_id]
    instance_strategy_tag = Config.STRATEGY_TAGS[instance_id]
    index_name = Config.INDEX_NAMES[instance_id]
    portfolio_names = {}
    retry_urls = {}
    for base_url in Config.BASE_URLS:
        if base_url in error_list:
            continue
        if instrument.option_type == OptionType.CE:
            option_portfolio_name = instance_portfolio_name["CALL"]
            strike_price = instrument.strike_price + order.order_strike_diff
        elif instrument.option_type == OptionType.PE:
            option_portfolio_name = instance_portfolio_name["PUT"]
            strike_price = instrument.strike_price - order.order_strike_diff
        
        url = base_url + f"/PlaceMultiLegOrderAdv?OptionPortfolioName={option_portfolio_name}&StrategyTag={instance_strategy_tag}&Symbol={index_name}&Product=NRML&NoDuplicateOrderForSeconds=0&PortLegs=Strike:{int(strike_price)}|Lots:{lots}|Expiry:{expiry_code}"
        try:
            if base_url not in last_hit_time:
                last_hit_time[base_url] = {}
                last_hit_time[base_url][OptionType.CE.name] = 0
                last_hit_time[base_url][OptionType.PE.name] = 0
            last_hit_time_diff = time.time() - last_hit_time[base_url][instrument.option_type.name]
            if last_hit_time_diff < Config.REQUSET_SLEEP_TIME:
                time.sleep(Config.REQUSET_SLEEP_TIME - last_hit_time_diff)
            last_hit_time[base_url][instrument.option_type.name] = time.time()

            signals.add_new(url)
            signals.send_message(f"Entry order for {instrument.trading_symbol} with order_id={order.id}")
            resp = requests.get(url=url, timeout=3)
            signals.add_new(f"{resp.status_code}::{resp.text}")

            if resp.status_code != 200:
                raise ValueError(f"Error in broker with response, {resp.status_code}::{resp.text}")
            data = resp.json()
            logger.debug(f"data_type before: {type(data)}, {data}")
            if isinstance(data, str):
                data = json.loads(data)
            logger.debug(f"data_type after: {type(data)}, {data}")
            if data["status"] != "success":
                if "Invalid or Disabled Strategy Tag" in data["error"]:
                    raise BrokerError(data['error'])
                raise ValueError(f"Error in broker with response {resp.status_code}::{resp.text}")
            portfolio_name = data["response"]
            portfolio_names[base_url] = portfolio_name
            logger.debug(f"url: {base_url}, stg-id: {strategy_id}, instance-id: {instance_id}, instrument: {instrument.trading_symbol}, qty: {order.quantity}, portfolio-name: {portfolio_name} added")
            logger.info(f"Place entry order for Instrument(strike={strike_price}, option_type={instrument.option_type.name}) with order_id={order.id}")
        except (BrokerError, ReadTimeout, ConnectTimeout) as ex:
            logger.debug(f"Error in entry order", exc_info=True)
            signals.send_alert(f"Error in url going to retry: {url}, error: {ex}")
            retry_urls[base_url] = {
                "url": url,
                "retry_count": 0,
                "last_hit_time": time.time()
            }
        except Exception as ex:
            signals.send_alert(f"Error in url: {url}, error: {ex}")
            logger.error(f"Error in entry order, {ex}")
            logger.debug(f"Error in entry order", exc_info=True)
            error_list.add(base_url)
            signals.add_new(f"Error - {url}::{ex}")

    while len(retry_urls) != 0:
        for base_url, data in list(retry_urls.items()):
            try:
                url = data["url"]
                retry_count = data["retry_count"]
                if retry_count >= Config.RETRY_COUNT:
                    raise ValueError(f"Max retry achived: {retry_count} base_url: {base_url}")
                if time.time() - data["last_hit_time"] < Config.RETRY_SLEEP_TIME:
                    continue
                logger.info(f"Going to retry for base_url: {base_url}, retry_count: {retry_count + 1}")
                signals.add_new(url)
                signals.send_message(f"Entry retry order for {instrument.trading_symbol} with order_id={order.id}")
                resp = requests.get(url=url, timeout=3)
                signals.add_new(f"{resp.status_code}::{resp.text}")
                if resp.status_code != 200:
                    raise ValueError(f"Error in broker with response, {resp.status_code}::{resp.text}")
                data = resp.json()
                logger.debug(f"data_type before: {type(data)}, {data}")
                if isinstance(data, str):
                    data = json.loads(data)
                logger.debug(f"data_type after: {type(data)}, {data}")
                if data["status"] != "success":
                    if "Invalid or Disabled Strategy Tag" in data["error"]:
                        raise BrokerError(data['error'])
                    raise ValueError(f"Error in broker with response {resp.status_code}::{resp.text}")
                portfolio_name = data["response"]
                portfolio_names[base_url] = portfolio_name
                logger.debug(f"url: {base_url}, stg-id: {strategy_id}, instance-id: {instance_id}, instrument: {instrument.trading_symbol}, qty: {order.quantity}, portfolio-name: {portfolio_name} added")
                logger.info(f"Place entry order for Instrument(strike={strike_price}, option_type={instrument.option_type.name}) with order_id={order.id}")
                del retry_urls[base_url]
            except (BrokerError, ReadTimeout, ConnectTimeout) as ex:
                signals.send_alert(f"Error in url going to retry: {url}, error: {ex}")
                logger.debug(f"Error in retry entry order", exc_info=True)
                retry_urls[base_url]["last_hit_time"] = time.time()
                retry_urls[base_url]["retry_count"] += 1
            except Exception as ex:
                signals.send_alert(f"Error in url: {url}, error: {ex}")
                del retry_urls[base_url]
                logger.error(f"Error in entry order, {ex}")
                logger.debug(f"Error in entry order", exc_info=True)
                error_list.add(base_url)
                signals.add_new(f"Error - {url}::{ex}")

    if len(error_list) == len(Config.BASE_URLS):
        raise BrokerError(f"All broker urls got into error")
    
    return portfolio_names

def place_exit_order(order: Order):
    instrument = get_instrument_by_token(order.instrument_id)
    strategy_id = order.strategy_id
    instance_id = order.instance_id
    if instrument.option_type == OptionType.CE:
        strike_price = instrument.strike_price + order.order_strike_diff
    elif instrument.option_type == OptionType.PE:
        strike_price = instrument.strike_price - order.order_strike_diff
    
    retry_urls = {}
    for base_url in Config.BASE_URLS: 
        if base_url in error_list:
            continue
        portfolio_name = order.portfolio_names[base_url]
        url = base_url + f"/ExitMultiLegOrder?OptionPortfolioName={portfolio_name}"

        try:
            if base_url not in last_hit_time:
                last_hit_time[base_url] = {}
                last_hit_time[base_url][OptionType.CE.name] = 0
                last_hit_time[base_url][OptionType.PE.name] = 0
            
            last_hit_time_diff = time.time() - last_hit_time[base_url][instrument.option_type.name]
            if last_hit_time_diff < Config.REQUSET_SLEEP_TIME:
                time.sleep(Config.REQUSET_SLEEP_TIME - last_hit_time_diff)
            last_hit_time[base_url][instrument.option_type.name] = time.time()

            signals.add_new(url)
            signals.send_message(f"Exit for {instrument.trading_symbol} with order_id={order.id}")
            resp = requests.get(url=url, timeout=3)
            signals.add_new(f"{resp.status_code}::{resp.text}")
            if resp.status_code != 200:
                raise ValueError(f"Error in broker with response, {resp.status_code}::{resp.text}")
            logger.debug(f"url: {base_url}, stg-id: {strategy_id}, instance-id: {instance_id}, instrument: {instrument.trading_symbol}, qty: {order.quantity}, portfolio-name: {portfolio_name} deleted")
            logger.info(f"Place exit order for Instrument(strike={strike_price}, option_type={instrument.option_type.name}) with order_id={order.id}")
        except (ReadTimeout, ConnectTimeout) as ex:
            logger.debug(f"Error in exit order", exc_info=True)
            signals.send_alert(f"Error in url going to retry: {url}, error: {ex}")
            retry_urls[base_url] = {
                "url": url,
                "retry_count": 0,
                "last_hit_time": time.time()
            }
        except Exception as ex:
            signals.send_alert(f"Error in url: {url}, error: {ex}")
            logger.error(f"Error in exit order, {ex}")
            logger.debug(f"Error in exit order", exc_info=True)
            error_list.add(base_url)
            signals.add_new(f"Error - {url}::{ex}")

    while len(retry_urls) != 0:
        for base_url, data in list(retry_urls.items()):
            try:
                url = data["url"]
                retry_count = data["retry_count"]
                if retry_count >= Config.RETRY_COUNT:
                    raise ValueError(f"Max retry achived: {retry_count} base_url: {base_url}")
                if time.time() - data["last_hit_time"] < Config.RETRY_SLEEP_TIME:
                    continue
                logger.info(f"Going to retry for base_url: {base_url}, retry_count: {retry_count + 1}")
                signals.add_new(url)
                signals.send_message(f"Exit retry order for {instrument.trading_symbol} with order_id={order.id}")
                resp = requests.get(url=url, timeout=3)
                signals.add_new(f"{resp.status_code}::{resp.text}")
                if resp.status_code != 200:
                    raise ValueError(f"Error in broker with response, {resp.status_code}::{resp.text}")
                logger.debug(f"url: {base_url}, stg-id: {strategy_id}, instance-id: {instance_id}, instrument: {instrument.trading_symbol}, qty: {order.quantity}, portfolio-name: {portfolio_name} deleted")
                logger.info(f"Place exit order for Instrument(strike={strike_price}, option_type={instrument.option_type.name}) with order_id={order.id}")
                del retry_urls[base_url]
            except (ReadTimeout, ConnectTimeout) as ex:
                signals.send_alert(f"Error in url going to retry: {url}, error: {ex}")
                logger.debug(f"Error in exit order", exc_info=True)
                retry_urls[base_url]["last_hit_time"] = time.time()
                retry_urls[base_url]["retry_count"] += 1
            except Exception as ex:
                signals.send_alert(f"Error in url: {url}, error: {ex}")
                del retry_urls[base_url]
                logger.error(f"Error in exit order, {ex}")
                logger.debug(f"Error in exit order", exc_info=True)
                error_list.add(base_url)
                signals.add_new(f"Error - {url}::{ex}")
    
    if len(error_list) == len(Config.BASE_URLS):
        raise BrokerError(f"All broker urls got into error")